<%
const { utils, route, config, modelTypes } = it;
const { _, classNameCase, require } = utils;
const apiClassName = classNameCase(route.moduleName);
const routes = route.routes;
const dataContracts = _.map(modelTypes, "name");

%>

import { createRequest } from '@/utils/request'

<% if (dataContracts.length) { %>
import { <%~ dataContracts.join(", ") %> ,baseUrl} from "./<%~ config.fileNames.dataContracts %>"
<% } %>

export class <%= apiClassName %>{

    <% routes.forEach((route) => { %>
        <%~ includeFile('./procedure-call.eta', { ...it, route }) %>
    <% }) %>
}
