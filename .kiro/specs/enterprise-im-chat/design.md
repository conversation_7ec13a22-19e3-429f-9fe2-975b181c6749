# 企业端IM聊天功能设计文档

## 概述

本文档描述了企业端IM聊天功能的设计方案。该功能将允许企业用户与求职者进行实时聊天交流，查看聊天历史，并管理聊天会话。设计基于现有的求职端IM聊天功能，复用相关组件和逻辑，但针对企业端用户的特定需求进行定制。

## 架构

### 整体架构

企业端IM聊天功能的架构分为以下几个主要部分：

1. **IM SDK集成**：使用网易云信SDK进行IM功能的实现
2. **状态管理**：使用Pinia进行状态管理，包括IM连接状态、会话列表、消息列表等
3. **UI组件**：聊天列表、聊天详情、消息输入框等UI组件
4. **API服务**：与后端API交互，获取IM账号、发送消息、获取会话列表等

### 数据流向

```mermaid
graph TD
    A[企业用户] --> B[企业端UI界面]
    B --> C[状态管理层]
    C --> D[IM SDK]
    D --> E[网易云信服务]
    C --> F[API服务]
    F --> G[后端服务]
    E --> D
    D --> C
    G --> F
    F --> C
    C --> B
```

## 组件和接口

### 核心组件

1. **IM SDK初始化组件**
   - 负责初始化网易云信SDK
   - 处理连接、断开连接、重连等逻辑
   - 监听消息接收、会话更新等事件

2. **状态管理组件**
   - 管理IM相关的状态数据
   - 包括会话列表、消息列表、未读消息数等
   - 提供状态更新和查询方法

3. **聊天列表组件**
   - 显示最近的聊天会话列表
   - 支持会话搜索、筛选、排序等功能
   - 显示未读消息数、最新消息预览等

4. **聊天详情组件**
   - 显示与特定求职者的聊天历史记录
   - 支持发送文本消息、图片等
   - 支持查看求职者简历信息

5. **消息输入组件**
   - 提供消息输入框
   - 支持发送文本、图片等类型的消息
   - 提供快捷回复功能

### 核心接口

1. **IM登录接口**
   - 接口：`apiVideoenGetaccountGet`
   - 功能：获取网易云信账号和token
   - 参数：企业ID等

2. **最近会话列表接口**
   - 接口：`apiVideoenLatestSessionsDayGet`
   - 功能：获取最近的聊天会话列表
   - 参数：分页信息等

3. **发送消息接口**
   - 接口：`apiVideoenSendtextPost`
   - 功能：发送文本消息给求职者
   - 参数：接收者ID、消息内容等

4. **设置消息已读接口**
   - 接口：`apiVideoenSetreadPost`
   - 功能：设置消息为已读状态
   - 参数：求职者ID、时间戳等

5. **获取快捷回复接口**
   - 接口：`apiVideoenQuickRepliesGet`
   - 功能：获取快捷回复列表
   - 参数：消息类型等

## 数据模型

### 会话模型

```typescript
interface ChatSession {
  id: string;           // 会话ID
  name: string;         // 求职者姓名
  avatar: string;       // 求职者头像
  lastMessage: string;  // 最后一条消息内容
  lastMessageType: string; // 最后一条消息类型
  lastMessageTime: string; // 最后一条消息时间
  unreadCount: number;  // 未读消息数
  isStarred: boolean;   // 是否收藏
  isOnline: boolean;    // 是否在线
  isMuted: boolean;     // 是否静音
  tags: string[];       // 标签列表
  candidateType: string; // 候选人类型
  positionId?: string;  // 应聘职位ID
  positionTitle?: string; // 应聘职位名称
}
```

### 消息模型

```typescript
interface ChatMessage {
  id: string;           // 消息ID
  type: string;         // 消息类型（文本、图片等）
  content: string;      // 消息内容
  isSelf: boolean;      // 是否自己发送的消息
  createTime: string;   // 消息创建时间
  status: string;       // 消息状态（发送中、已发送、发送失败等）
  // 其他特定类型消息的属性
  resumeId?: string;    // 简历ID（简历卡片消息）
  positionId?: string;  // 职位ID（职位卡片消息）
}
```

## 错误处理

### 连接错误处理

1. **连接失败**
   - 显示连接失败提示
   - 提供重连按钮
   - 自动尝试重连

2. **网络波动**
   - 检测网络状态变化
   - 网络恢复时自动重连
   - 显示网络状态提示

### 消息发送错误处理

1. **发送失败**
   - 显示发送失败标识
   - 提供重发选项
   - 记录失败原因

2. **消息格式错误**
   - 进行消息格式验证
   - 显示格式错误提示
   - 阻止发送格式错误的消息

## 测试策略

### 单元测试

1. **状态管理测试**
   - 测试状态更新逻辑
   - 测试状态查询方法
   - 测试状态初始化

2. **组件测试**
   - 测试组件渲染
   - 测试组件交互
   - 测试组件事件处理

### 集成测试

1. **IM SDK集成测试**
   - 测试SDK初始化
   - 测试连接和断开连接
   - 测试消息发送和接收

2. **API集成测试**
   - 测试API调用
   - 测试API错误处理
   - 测试API数据处理

### 端到端测试

1. **聊天流程测试**
   - 测试完整的聊天流程
   - 测试多设备同步
   - 测试长时间运行稳定性

2. **性能测试**
   - 测试大量消息处理性能
   - 测试大量会话处理性能
   - 测试网络波动情况下的性能

## 实现细节

### IM SDK初始化流程

1. 企业用户登录后，调用`apiVideoenGetaccountGet`接口获取网易云信账号和token
2. 使用获取到的账号和token初始化网易云信SDK
3. 注册消息接收、会话更新等事件监听
4. 连接成功后，更新连接状态，准备接收消息

### 消息发送流程

1. 用户在输入框中输入消息并点击发送
2. 调用`apiVideoenSendtextPost`接口发送消息
3. 发送成功后，根据imsdk更新消息历史，参考求职端
5. 发送失败后，更新本地消息状态为"发送失败"，并提供重发选项

### 会话列表获取流程

1. 用户进入消息页面
2. 调用`apiVideoenLatestSessionsDayGet`接口获取最近会话列表
3. 将获取到的会话列表显示在界面上，获取到会话列表后参考求职端的
4. 支持按时间、未读状态等进行排序
5. 支持搜索、筛选等功能

### 聊天历史记录获取流程

1. 用户点击会话列表中的某个会话
2. 进入聊天详情页面
3. 参考求职端获取更多历史记录的实现获取聊天记录
4. 将历史记录显示在界面上，并滚动到最新消息位置

### 未读消息处理流程

1. 收到新消息时，更新会话的未读消息计数
2. 如果用户当前在该会话的聊天详情页面，则自动将消息标记为已读
3. 如果用户不在该会话的聊天详情页面，则显示未读消息提示
4. 用户进入聊天详情页面后，调用`apiVideoenSetreadPost`接口将消息标记为已读