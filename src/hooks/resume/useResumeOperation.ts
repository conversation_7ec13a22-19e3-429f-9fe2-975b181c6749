import type { ResponseResult } from '@/utils/ResponseResult'

type FunctionArgs<Return = Promise<ResponseResult>> = () => Return

export function useResumeOperation() {
  const deleteItem = function <T extends FunctionArgs>(
    Fn: T,
    condition: boolean,
    tips: string,
    options: {}
  ) {
    if (condition) {
      uni.showModal({
        title: '提示',
        content: tips,
        success: async (res: any) => {
          if (res.confirm) {
            uni.showLoading({
              title: '删除中',
              mask: true
            })

            const result = await Fn().catch((err) => err)
            uni.hideLoading()
            const { code, message } = result
            if (code === 1) {
              navigationback(options)
            }

            uni.showToast({
              title: message,
              icon: 'none'
            })
          }
        }
      })
    }
  }

  const saveItem = async <T extends FunctionArgs>(Fn: T, options: {},fromOptimize= false) => {
    uni.showLoading({
      title: '保存中',
      mask: true
    })
    const result = await Fn().catch((err) => err)
    uni.hideLoading()
    const { code, message } = result
    if (code === 1) {
      // uni.redirectTo({ url })
      navigationback(options,fromOptimize)
    }
    uni.showToast({
      title: message,
      icon: 'none'
    })
  }

  const navigationback = (options: any,fromOptimize = false) => {
    const pages = getCurrentPages()
    if (pages.length <= 1) {
      uni.switchTab({ url: '/pages/my/index' })
    } else {
      const beforePage = pages[pages.length - 2]
      if (beforePage.route?.includes('resume') && !!beforePage.onLoad  && !fromOptimize) {
        beforePage.onLoad(options)
      }

      uni.navigateBack({
        delta: 1,
        fail: () => {
          if (options.resumeId) {
            uni.reLaunch({ url: '/mypages/my/resume/index?resumeId=' + options.resumeId })
          } else {
            uni.switchTab({ url: '/pages/my/index' })
          }
        }
      })
      if(fromOptimize){
        uni.$emit('updateResumeOptimization')
      }
      // #ifdef MP-ALIPAY
      uni.$emit('reload-alipay')
      // #endif
    }
  }

  return {
    saveItem,
    deleteItem,
    navigationback
  }
}
