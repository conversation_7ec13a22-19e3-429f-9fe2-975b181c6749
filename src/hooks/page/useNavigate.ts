export function useNavigate() {
  const navigateTo = (url: string) => {
    uni.navigateTo({
      url,
      fail: (err) => {
        uni.redirectTo({ url})
      }
    })
  }

  const redirectTo = (url: string) => {
    uni.redirectTo({ url })
  }

  const switchTab = (url: string) => {
    uni.switchTab({ url })
  }

  const navigateBack = () => {
    uni.navigateBack()
  }

  return {
    navigateTo,
    redirectTo,
    switchTab,
    navigateBack
  }
}
