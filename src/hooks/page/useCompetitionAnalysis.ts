import { ref, computed } from 'vue'
import { My } from '@/services/my/My'
import { Competionservice } from '@/services/my/Competionservice'
import { MyServiceDetailDto } from '@/services/my/data-contracts'
import { useAppStore } from '@/store/modules/app'

// 竞争力分析状态枚举
enum CompetitionAnalysisFlag {
  EXPIRED = 0,           // 已过期
  CAN_BUY = 1,          // 可以购买
  CAN_BUY_ALTERNATIVE = 2,  // 可以购买(备选)
  REMAINING_COUNT = -1   // 有剩余次数
}

// Hook选项接口
interface UseCompetitionAnalysisOptions {
  positionName?: string  // 职位名称，用于弹窗显示
  resumeGuid?: string   // 简历GUID，用于结果页面
}

// Hook返回值接口
interface UseCompetitionAnalysisReturn {
  showPopupResumeTopBox: import('vue').Ref<boolean>
  leftCount: import('vue').Ref<number>
  executeCompetitionAnalysis: (positionGuid: string, options?: UseCompetitionAnalysisOptions) => Promise<void>
  closeCompetitivenessAnalysisBox: () => void
  gotoCompetitivenessAnalysis: (positionGuid: string, options?: UseCompetitionAnalysisOptions) => Promise<void>
}

/**
 * 竞争力分析Hook
 * 整合所有页面中重复的竞争力分析逻辑
 */
export function useCompetitionAnalysis(): UseCompetitionAnalysisReturn {
  const store = useAppStore()
  const isIos = computed(() => store.isIos)
  const myallUrl = import.meta.env.VITE_MYMALL_URL

  // 弹窗状态
  const showPopupResumeTopBox = ref(false)
  const leftCount = ref(0)

  /**
   * 获取服务配置信息
   */
  const getServiceConfig = async () => {
    const data = await My.myServicesList({ type: 100001 })
    const topResult = data.data as MyServiceDetailDto
    
    if (!topResult?.businessInfoList || topResult.businessInfoList.length === 0) {
      return null
    }

    return topResult.businessInfoList.find(item => item.businessType === 100001)
  }

  /**
   * 跳转到竞争力分析页面
   */
  const navigateToCompetitionPage = (path: string, positionGuid?: string, resumeGuid?: string) => {
    let url = `${myallUrl}/MyMall/CompetionAnalysis/${path}`
    
    if (positionGuid) {
      const queryParams = [`positionGuid=${positionGuid}`]
      if (resumeGuid) {
        queryParams.push(`resumeGuid=${resumeGuid}`)
      }
      url += `?${queryParams.join('&')}`
    }

    uni.navigateTo({
      url: `/pages/web-view/index?src=${encodeURIComponent(url)}&login=true`
    })
  }

  /**
   * 处理购买流程
   */
  const handlePurchaseFlow = async (positionGuid: string, options?: UseCompetitionAnalysisOptions) => {
    uni.showLoading({ title: '处理中...' })
    
    try {
      const buyResult = await Competionservice.competionserviceBuy({ 
        positionguid: positionGuid 
      })
      
      uni.hideLoading()
      
      if (buyResult.code === 1) {
        navigateToCompetitionPage('CompetionAnlsRslt', positionGuid, options?.resumeGuid)
      } else {
        uni.showToast({ 
          title: buyResult.message || '购买失败',
          icon: 'none'
        })
      }
    } catch (error) {
      uni.hideLoading()
      uni.showToast({ 
        title: '购买处理失败',
        icon: 'none'
      })
      console.error('Competition analysis purchase error:', error)
    }
  }

  /**
   * 检查iOS限制
   */
  const checkIosRestriction = (): boolean => {
    if (isIos.value) {
      uni.showModal({
        title: '提示',
        content: '由于相关规范，iOS功能暂不可用',
        showCancel: false
      })
      return true
    }
    return false
  }

  /**
   * 主要的竞争力分析执行函数
   */
  const executeCompetitionAnalysis = async (positionGuid: string, options?: UseCompetitionAnalysisOptions) => {
    // iOS限制检查
    if (checkIosRestriction()) return

    uni.showLoading({ title: '请稍后' })

    try {
      // 1. 获取服务配置
      const serviceItem = await getServiceConfig()
      
      if (!serviceItem || !isValidCompetitionFlag(serviceItem.competionAnalysisFlag)) {
        uni.hideLoading()
        return navigateToCompetitionPage('Index')
      }

      // 2. 获取竞争力分析信息
      const competitionInfo = await Competionservice.competionserviceInfoList({ 
        positionguid: positionGuid 
      })
      
      uni.hideLoading()

      // 3. 处理不同状态
      await handleCompetitionStatus(serviceItem, competitionInfo, positionGuid, options)
      
    } catch (error) {
      uni.hideLoading()
      uni.showToast({ 
        title: '获取服务信息失败',
        icon: 'none'
      })
      console.error('Competition analysis error:', error)
    }
  }

  /**
   * 处理竞争力分析状态
   */
  const handleCompetitionStatus = async (
    serviceItem: any, 
    competitionInfo: any, 
    positionGuid: string,
    options?: UseCompetitionAnalysisOptions
  ) => {
    const flag = serviceItem.competionAnalysisFlag

    // 已使用过，直接查看结果
    if (competitionInfo.isUsed) {
      return navigateToCompetitionPage('CompetionAnlsRslt', positionGuid, options?.resumeGuid)
    }

    switch (flag) {
      case CompetitionAnalysisFlag.REMAINING_COUNT:
        // 有剩余次数，显示弹窗
        leftCount.value = serviceItem.leftCount || 0
        showPopupResumeTopBox.value = true
        break
        
      case CompetitionAnalysisFlag.CAN_BUY:
      case CompetitionAnalysisFlag.CAN_BUY_ALTERNATIVE:
        // 可以购买，执行购买流程
        await handlePurchaseFlow(positionGuid, options)
        break
        
      default:
        // 其他情况跳转到首页
        navigateToCompetitionPage('Index')
    }
  }

  /**
   * 验证竞争力分析标志是否有效
   */
  const isValidCompetitionFlag = (flag?: number): boolean => {
    return flag !== undefined && [
      CompetitionAnalysisFlag.REMAINING_COUNT,
      CompetitionAnalysisFlag.CAN_BUY,
      CompetitionAnalysisFlag.CAN_BUY_ALTERNATIVE
    ].includes(flag)
  }

  /**
   * 关闭竞争力分析弹窗
   */
  const closeCompetitivenessAnalysisBox = () => {
    showPopupResumeTopBox.value = false
  }

  /**
   * 确认购买竞争力分析
   */
  const gotoCompetitivenessAnalysis = async (positionGuid: string, options?: UseCompetitionAnalysisOptions) => {
    await handlePurchaseFlow(positionGuid, options)
    closeCompetitivenessAnalysisBox()
  }

  return {
    showPopupResumeTopBox,
    leftCount,
    executeCompetitionAnalysis,
    closeCompetitivenessAnalysisBox,
    gotoCompetitivenessAnalysis
  }
}