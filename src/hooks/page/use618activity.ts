import { computed, unref } from 'vue'

export function use618activity() {
  const showActivity = computed(() => {
    const now = new Date()
    const start = new Date('2023/06/15 00:00:00')
    const end = new Date('2023/06/21 23:59:59')
    if (now >= start && now <= end) {
      const value = uni.getStorageSync('show618')
      if (!value) return true
      let year = now.getFullYear()
      let month = now.getMonth() + 1
      let day = now.getDate()
      if (value == `${year}-${month}-${day}`) return false
      return true
    }
    return false
  })
  

  const showfn = () =>{
    const now = new Date()
    const start = new Date('2023/06/15 00:00:00')
    const end = new Date('2023/06/21 23:59:59')
    if (now >= start && now <= end) {
      const value = uni.getStorageSync('show618')
      if (!value) return true
      let year = now.getFullYear()
      let month = now.getMonth() + 1
      let day = now.getDate()
      if (value == `${year}-${month}-${day}`) return false
      return true
    }
    return false
  }

  return { showActivity ,showfn}
}
