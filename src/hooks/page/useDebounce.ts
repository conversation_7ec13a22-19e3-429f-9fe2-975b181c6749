type FunctionArgs<Args extends any[] = any[], Return = void> = (...args: Args) => Return

export function useDebounce<T extends FunctionArgs>(fn: T, ms: number = 200): T {
  let timer: ReturnType<typeof setTimeout> | undefined

  const debounce = function (this: any, ...args: any) {
    if (timer) clearTimeout(timer)

    timer = setTimeout(() => {
      fn.apply(this, args)
    }, ms)
  }

  return debounce as any as T
}
