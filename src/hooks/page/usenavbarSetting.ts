export function usenavbarSetting() {
  // 获取状态栏高度
  const { statusBarHeight, screenWidth, screenHeight } = uni.getSystemInfoSync()
  const { top, height } = uni.getMenuButtonBoundingClientRect()

  // 计算导航栏的高度
  // 此高度基于右上角菜单在导航栏位置垂直居中计算得到
  const navBarHeight = height + (top - (statusBarHeight || 20)) * 2

  // 计算状态栏与导航栏的总高度
  const statusNavBarHeight = (statusBarHeight || 20) + navBarHeight

  return {
    navBarHeight,
    statusNavBarHeight,
    screenWidth,
    screenHeight,
    statusBarHeight
  }
}
