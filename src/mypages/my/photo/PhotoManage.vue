<template>
  <view class="PhotoManage_all">
    <uni-nav-bar
      title="头像形象"
      left-icon="left"
      :fixed="true"
      :border="false"
      :statusBar="true"
      @clickLeft="back"
    ></uni-nav-bar>
    <!-- <button @click="open">照片主页打开</button> -->
    <view class="picture" v-if="showAvatar">
      <view class="picture_left">
        <view class="title"><text>头像</text></view>
        <view class="tips"
          ><text>不能通过审核：非本人照片，非头部位置，模糊不清晰，裸露不文明</text></view
        >
      </view>
      <view class="picture_right">
        <image
          v-if="Boolean(avatar)"
          class="img_1"
          :src="avatar"
          @click="open({ type: 2 })"
        ></image>
        <view v-else class="upLoad" @click="open({ type: 2 })">
          <i class="iconfont icon-camera"></i>
          <text>上传照片</text>
        </view>
      </view>
    </view>
    <view class="line" v-if="showAvatar"></view>
    <view class="picture_list" v-if="showAlbumlist">
      <view class="list_text">
        形象照(<text>{{ albumlist.length }}</text
        >/4)
      </view>
      <view v-if="showImg" class="row_1">
        <view class="row_1_button">
          <view class="button_1">
            <view v-if="showImg.sort > 1" @click="albumsort(showImg, -1)">
              <text>向前移</text>
            </view>
          </view>
          <view class="button_2">
            <view v-if="showImg.sort < albumlist.length" @click="albumsort(showImg, 1)">
              <text>向后移</text>
            </view>
          </view>
        </view>
        <image class="img_show" :mode="'aspectFit'" :src="showImg?.imgUrl"></image>
      </view>
      <view class="row_2">
        <view
          v-for="item in albumlist"
          :key="item.id"
          :class="{ active: item.id == showImg?.id }"
          class="col"
          :style="{ 'margin-left': item.sort == 1 ? '0' : '16rpx' }"
          @click="chooseImg(item)"
        >
          <view class="close" @click.stop="deleteImg(item)">
            <i class="iconfont icon-close"></i>
          </view>
          <image class="img_2" mode="aspectFit" :src="item.imgUrl"></image>
        </view>
        <view v-if="albumlist.length < 4" class="col" @click="open({ type: 3 })">
          <view class="add">
            <i class="iconfont icon-camera"></i>
            <text>上传照片</text>
          </view>
        </view>
      </view>
    </view>
    <selectPhotoPopup ref="selectPhotoPopupNode" @submit="submit"></selectPhotoPopup>
  </view>
</template>
<script setup lang="ts">
  import { nextTick, onBeforeMount, reactive, ref, Ref } from 'vue'
  import selectPhotoPopup from './components/selectPhotoPopup.vue'
  import type {
    selectPhotoPopupNodeType,
    selectPhotoPopupNodeTypeModel
  } from '@/pages/message/type/index'
  import { Photo } from '@/services/my/Photo'
  import { onLoad } from '@dcloudio/uni-app'

  const selectPhotoPopupNode: Ref<null | selectPhotoPopupNodeType> = ref(null)

  const open = (model: selectPhotoPopupNodeTypeModel) => {
    selectPhotoPopupNode.value?.open(model)
  }

  onBeforeMount(async () => {
    uni.showLoading({
      title: '加载中'
    })
    await getAvatar()
    await getAlbumlist()
    uni.hideLoading()
  })

  onLoad((options) => {
    if (options?.type && options?.type == 2) {
      showAlbumlist.value = true
    } else {
      showAvatar.value = true
    }
    if(options?.from){
      fromOptiomize.value = true
    }
  })
  const avatar = ref('')
  const fromOptiomize = ref(false)
  const getAvatar = () => {
    return Photo.photoAvatarList()
      .then((data) => {
        const math = new Date().getTime()
        let row = data as { avatar: string }
        avatar.value = row.avatar ? row.avatar + '?math=' + math : ''
      })
      .catch((e) => {
        console.log(e)
      })
  }

  type AlbumItem = {
    id: number
    imgUrl: string
    sort: number
  }
  const showImg: Ref<AlbumItem | null> = ref(null)
  const albumlist: Ref<AlbumItem[]> = ref([])
  const showAvatar = ref(false)
  const showAlbumlist = ref(false)
  const getAlbumlist = (init = true) => {
    return Photo.photoAlbumlistList()
      .then((data) => {
        let row = data as AlbumItem[]
        albumlist.value = row
        if (init) {
          if (row.length > 0) {
            showImg.value = albumlist.value[0]
          } else {
            showImg.value = null
          }
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }
  const chooseImg = (item: AlbumItem) => {
    if (item.id == showImg.value?.id) {
      return
    } else {
      showImg.value = item
    }
  }
  const deleteImg = (item: AlbumItem) => {
    console.log(item)

    uni.showLoading({
      title: '加载中'
    })
    Photo.photoAlbumdelete({ id: item.id })
      .then(async (data) => {
        getAlbumlist()
      })
      .catch((e) => {
        console.log(e)
      })
      .finally(() => {
        uni.hideLoading()
      })
  }

  const albumsort = (item: AlbumItem | null, type: number) => {
    if (!item) return
    uni.showLoading({
      title: '加载中'
    })
    Photo.photoAlbumsort({ id: item.id, sortType: type })
      .then(async (data) => {
        await getAlbumlist(false)
        let row = albumlist.value.find((item2) => item2.id == item.id)
        if (row) {
          showImg.value = row
        }
      })
      .catch((e) => {
        console.log(e)
      })
      .finally(() => {
        uni.hideLoading()
      })
  }

  const submit = (value: number) => {
    if (value == 1 || value == 2) {
      getAvatar()
    } else if (value == 3) {
      getAlbumlist()
    }
  }
  const back = () => {
    const pages = getCurrentPages()
    if (pages.length >= 2) {
      const prevPage = pages[pages.length - 2]
      if (prevPage?.route && prevPage.route.includes('mypages/register/step4')) {
        uni.switchTab({
          url: '/pages/index/index'
        })
      } else if (prevPage?.route && prevPage.route.includes('mypages/my/resume/index')) {
        const url = `/mypages/my/resume/index?resumeId=${prevPage?.options?.resumeId}`
        uni.navigateBack({
          delta: 1,
          success(e) {
            uni.$emit('refreshResume', {
              resumeId: prevPage?.options?.resumeId
            })
          }
        })
      } else {
        uni.navigateBack({
          delta: 1,
          fail(e) {
            uni.reLaunch({
              url: '/pages/index/index'
            })
          }
        })
      }
    } else {
      uni.navigateBack({
        delta: 1,
        fail(e) {
          uni.reLaunch({
            url: '/pages/index/index'
          })
        }
      })
    }
    if(fromOptiomize){
      uni.$emit('updateResumeOptimization')
    }
  }
</script>
<style lang="scss" scoped>
  .PhotoManage_all {
    position: relative;
    .picture_list {
      margin-top: 40rpx;
      padding: 0 30rpx;
      box-sizing: border-box;
      .list_text {
        font-size: 32rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #333333;
        opacity: 1;
        text {
          color: #608bf8;
        }
      }
      .row_1 {
        margin-top: 40rpx;
        height: 400rpx;
        width: 100%;
        position: relative;
        .img_show {
          height: 400rpx;
          width: 100%;
          animation: showImg 0.3s 1;
        }
        @keyframes showImg {
          0% {
            opacity: 0;
          }
          100% {
            opacity: 1;
          }
        }
        .row_1_button {
          position: absolute;
          width: 280rpx;
          height: 58rpx;
          left: 50%;
          top: 75%;
          transform: translate(-50%, 0);
          display: flex;
          justify-content: space-between;
          .button_1 {
            justify-content: flex-start;
          }
          .button_2 {
            justify-content: flex-end;
          }
          .button_1,
          .button_2 {
            flex: 1;
            display: flex;
            view {
              width: 128rpx;
              height: 58rpx;
              background: rgba(0, 0, 0, 0.39);
              border-radius: 30rpx;
              text-align: center;
              text {
                font-size: 24rpx;
                font-family: PingFang SC;
                font-weight: 400;
                line-height: 58rpx;
                color: #ffffff;
                opacity: 1;
              }
            }
          }
        }
      }
      .row_2 {
        margin-top: 32rpx;
        display: flex;
        .active {
          border: 1rpx #608bf8 solid !important;
        }
        .col {
          height: 160rpx;
          width: 160rpx;
          background: #f5f7fa;
          position: relative;
          margin-left: 16rpx;
          display: flex;
          justify-items: center;
          align-items: center;
          border: 1rpx #fff solid;
          transition: border 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
          .img_2 {
            height: 160rpx;
            width: 160rpx;
          }
          .add {
            display: flex;
            width: 100%;
            height: 100%;
            justify-content: center;
            align-items: center;
            background: #f5f7fa;
            flex-direction: column;
            .icon-camera {
              font-size: 44rpx;
              color: #dce7f9;
            }
            text {
              font-size: 24rpx;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #979ea7;
              opacity: 1;
              margin-top: 12rpx;
            }
          }

          .close {
            width: 48rpx;
            height: 48rpx;
            background: rgba(0, 0, 0, 0.39);
            position: absolute;
            top: 0;
            right: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            .icon-close {
              font-size: 24rpx;
              color: #fff;
              opacity: 1;
            }
          }
        }
      }
    }
    .line {
      height: 1rpx;
      background: #eeeeee;
      margin: 0 30rpx;
    }
    .picture {
      display: flex;
      padding: 40rpx 30rpx;
      .picture_right {
        margin-left: 54rpx;
        width: 128rpx;
        height: 128rpx;
        display: flex;
        justify-content: center;
        align-items: center;

        border-radius: 50%;
        overflow: hidden;
        .upLoad {
          display: flex;
          width: 128rpx;
          height: 128rpx;
          justify-content: center;
          align-items: center;
          background: #f5f7fa;
          flex-direction: column;
          .icon-camera {
            font-size: 44rpx;
            color: #dce7f9;
          }
          text {
            font-size: 24rpx;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #979ea7;
            opacity: 1;
            margin-top: 12rpx;
          }
        }
        .img_1 {
          width: 128rpx;
          height: 128rpx;
          opacity: 1;
        }
      }
      .picture_left {
        flex: 1;
        .title {
          text {
            font-size: 32rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #333333;
            opacity: 1;
          }
        }
        .tips {
          margin-top: 12rpx;
          text {
            font-size: 24rpx;
            font-family: PingFang SC;
            font-weight: 400;
            color: #999999;
            opacity: 1;
          }
        }
      }
    }
  }

  ::v-deep .uni-navbar--fixed {
    position: fixed;
    z-index: 20 !important;
    left: 0;
    right: 0;
  }
</style>
