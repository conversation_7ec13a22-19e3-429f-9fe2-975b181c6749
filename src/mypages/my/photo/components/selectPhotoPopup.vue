<template>
  <uni-popup ref="selectPhotoPopupRef">
    <view class="popup-content">
      <view v-if="showDelete" class="tips_box">
        <view class="row_1">
          <view class="row_1_left">
            <view class="top"><i class="iconfont icon-true"></i><text>正确示范</text></view>
            <view class="bottom"><text>上传真实头像，更容易赢得好感</text></view>
          </view>
          <view class="row_1_right">
            <image class="img_1" :src="img1"></image>
          </view>
        </view>
        <view class="row_2">
          <view class="title"><i class="iconfont icon-wrong"></i><text>不佳示范</text></view>
          <view class="row_2_col">
            <view class="col" v-for="(item, index) in imgList" :key="index">
              <image class="img_2" mode="heightFix" :src="item.img"></image>
              <text>{{ item.text }}</text>
            </view>
          </view>
        </view>
      </view>
      <view class="select_option">
        <view class="item" @click="camera('camera')"><text>拍照</text></view>
        <view class="line"></view>
        <view class="item" @click="camera('album')"><text>从相册中选择</text></view>
        <view class="line"></view>
        <view v-if="showDelete" class="item" @click="deleteFun"><text>删除头像</text></view>
        <view v-if="showDelete" class="line"></view>
        <view class="item close" @click="close"><text>取消</text></view>
      </view>

    </view>
  </uni-popup>
  <img-crop ref="cropRef" @cancel="cancel" @confirm="comfirm" />

      <uni-popup ref="alertDialogRef" type="dialog">
				<uni-popup-dialog :type="msgType" cancelText="关闭" confirmText="确定" title="提示" :content="messageText" @confirm="dialogConfirm"
					@close="dialogClose"></uni-popup-dialog>
			</uni-popup>

</template>
<script lang="ts" setup>
import { ref, Ref, onBeforeMount, computed } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import type { UniPopup, selectPhotoPopupNodeTypeModel } from '@/pages/message/type/index'
import { Photo } from '@/services/my/Photo'
import { baseUrl } from '@/services/my/data-contracts'
import { upLoadFileRequest } from '@/utils/request'
import imgCrop from './imgCrop.vue'

const props = withDefaults(defineProps<{
  type?: string
}>(), {
})
const emit = defineEmits<{
  (e: 'submit', value: number): void
}>()
const type = computed(() => props.type)
const selectPhotoPopupRef: Ref<UniPopup | null> = ref(null)
const cropRef: Ref<typeof imgCrop | null> = ref(null)
const RefuseSubmit = () => {
  emit('submit', openType.value)
}

const showDelete = ref(false)
const openType = ref(1)
const img1 = ref("https://image.gxrc.com/gxrcsite/wxMiniApp/2022/my/<EMAIL>")
const imgList = ref([
  { "img": "https://image.gxrc.com/gxrcsite/wxMiniApp/2022/my/<EMAIL>", "text": "非人物照" },
  { "img": "https://image.gxrc.com/gxrcsite/wxMiniApp/2022/my/<EMAIL>", "text": "五官遮挡" },
  { "img": "https://image.gxrc.com/gxrcsite/wxMiniApp/2022/my/<EMAIL>", "text": "模糊不清" },
  { "img": "https://image.gxrc.com/gxrcsite/wxMiniApp/2022/my/<EMAIL>", "text": "衣着不当" },
])

  const alertDialogRef: Ref<UniPopup | null> = ref(null)
const msgType = ref('success')
const messageText = ref('')

const open = (model: selectPhotoPopupNodeTypeModel) => {
  //1是没有头像的时候点击，是没有删除头像的
  //2是有头像的时候点击，有删除头像
  //3是上传相册
  openType.value = model.type
  if (model.type == 1 || model.type == 3) {
    showDelete.value = false
  } else {
    showDelete.value = true
  }
  if ((type.value != undefined || type.value != null) && type.value == 'registerBlue') {
    img1.value = "https://image.gxrc.com/gxrcsite/webApp/lanling/<EMAIL>"
    imgList.value = [
      { "img": "https://image.gxrc.com/gxrcsite/webApp/lanling/<EMAIL>", "text": "非人物照" },
      { "img": "https://image.gxrc.com/gxrcsite/webApp/lanling/<EMAIL>", "text": "五官遮挡" },
      { "img": "https://image.gxrc.com/gxrcsite/webApp/lanling/<EMAIL>", "text": "模糊不清" },
      { "img": "https://image.gxrc.com/gxrcsite/webApp/lanling/<EMAIL>", "text": "衣着不当" },
    ]
    showDelete.value = true
  }
  selectPhotoPopupRef.value?.open('bottom')
}

const close = () => {
  selectPhotoPopupRef.value?.close()
}
const dialogConfirm = () => {
  alertDialogRef.value?.close()
}
const dialogClose = () => {
  alertDialogRef.value?.close()
}
const camera = (type: string) => {
  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: [type], //这要注意，camera掉拍照，album是打开手机相册
    success: (res) => {
      const filter = ['jpg', 'png', 'jpeg', 'image']
      const maxSize = 5 * 1024000
      if (Array.isArray(res.tempFiles) && res.tempFiles[0]) {
        let files = res.tempFiles[0] as UniApp.ChooseImageSuccessCallbackResultFile
        let name = files.path
        let str = name.split('.')[name.split('.').length - 1]

        if (str) {
          if (filter.includes(str)) {
            if (files.size >= maxSize) {
              uni.showToast({
                title: `文件大小限制为${maxSize / 1000 / 1024}Mb`,
                icon: 'none'
              })
              return
            }
            if (openType.value == 1 || openType.value == 2) {
              cropRef?.value?.open(res.tempFilePaths[0])
            } else if (openType.value == 3) {
              upLoadFileRequest({
                url: baseUrl + `/api/photo/uploadalbum`,
                filePath: res.tempFilePaths[0],
                showLoad: true,
                name: 'image'
              })
                .then((data) => {
                  close()
                  RefuseSubmit()
                  uni.$emit('uploadalbum')
                })
                .catch((err) => {
                  console.log(err)
                })
            }
          } else {
            uni.showToast({
              title: '图片支持上传jpg，png格式文件',
              icon: 'none'
            })
          }
        }
      }
    }
  })
}

const deleteFun = () => {
  uni.showLoading({
    title: '加载中'
  })
  Photo.photoAvatardelete()
    .then((data) => {
      close()
      RefuseSubmit()
    })
    .catch((e) => {
      console.log(e)
    })
    .finally(() => {
      uni.hideLoading()
    })
}

const cancel = () => { }
const comfirm = (url: string) => {
  uni.showLoading({
    title: '图片上传中...',
    mask: true
  })
  upLoadFileRequest({
    url: baseUrl + `/api/photo/uploadavatar`,
    filePath: url,
    showLoad: true,
    name: 'image'
  })
    .then((data) => {
      data = JSON.parse(data.data)
      if (data.code == 1) {
        uni.showToast({
                title: data.message,
                icon: 'none'
              })
      } else {
        msgType.value = 'error'
				messageText.value = data.message
        alertDialogRef.value?.open()
      }

      close()
      RefuseSubmit()
      uni.$emit('uploadAvatar')
    })
    .catch((err) => {
      console.log(err)
    })
    .finally(() => {
      uni.hideLoading()
    })
}
defineExpose({
  open,
  close
})
</script>
<style lang="scss" scoped>
.popup-content {
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;

  .tips_box {
    height: 616rpx;
    background: #fff;
    opacity: 1;
    border-radius: 20rpx;
    margin-bottom: 16rpx;
    box-sizing: border-box;
    padding: 56rpx 28rpx;

    .row_2 {
      margin-top: 64rpx;

      .title {
        display: flex;
        align-items: center;

        .icon-wrong {
          font-size: 48rpx;
          color: #de553c;
        }

        text {
          font-size: 32rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #333333;
          opacity: 1;
          margin-left: 16rpx;
        }
      }

      .row_2_col {
        margin-top: 48rpx;
        display: flex;
        justify-content: space-between;

        .col {
          height: 144rpx;
          text-align: center;

          .img_2 {
            height: 144rpx;
          }

          text {
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 400;
            color: #666666;
            opacity: 1;
            margin-top: 16rpx;
          }
        }
      }
    }

    .row_1 {
      display: flex;
      justify-content: space-between;
      height: 144rpx;

      .row_1_right {
        .img_1 {
          width: 144rpx;
          height: 144rpx;
        }
      }

      .row_1_left {
        height: 100%;
        display: flex;
        justify-content: space-around;
        flex-direction: column;

        .top {
          display: flex;
          align-items: center;

          .icon-true {
            font-size: 48rpx;
            color: #457ccf;
          }

          text {
            font-size: 32rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #333333;
            opacity: 1;
            margin-left: 16rpx;
          }
        }

        .bottom {
          margin-bottom: 16rpx;

          text {
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 400;
            color: #666666;
            opacity: 1;
          }
        }
      }
    }
  }

  .select_option {
    width: 100%;
    max-height: 432rpx;
    background: #fff;
    opacity: 1;
    border-radius: 20rpx;

    .line {
      height: 1rpx;
      width: 100%;
      background: #eeeeee;
    }

    .item {
      height: 108rpx;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      text {
        font-size: 32rpx;
        font-family: PingFang SC;
        font-weight: 400;
        color: #333333;
        opacity: 1;
      }
    }

    .close {
      text {
        color: #bbbbbb;
      }
    }
  }
}
</style>
