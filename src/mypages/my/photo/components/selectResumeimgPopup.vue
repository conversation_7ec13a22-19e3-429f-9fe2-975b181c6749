<script setup lang="ts">
  import { upLoadFileRequest } from '@/utils/request'
  import imgCrop from './imgCrop.vue'
  import { baseUrl } from '@/services/my/data-contracts'

  type Prop = {
    isFromAnalysis?: boolean
  }

  const { isFromAnalysis = false } = defineProps<Prop>()
  const popupRef = $ref<any>(null)

  const cropRef = $ref<any>(null)
  let imgUrl = $ref('')

  const emit = defineEmits(['success'])

  const camera = (type: string) => {
    uni.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: [type], //这要注意，camera掉拍照，album是打开手机相册
      success: (res) => {
        const filter = ['jpg', 'png', 'jpeg', 'image']
        const maxSize = 5 * 1024000
        uni.showLoading({
          title: '解析中...',
          mask: true
        })
        if (Array.isArray(res.tempFiles) && res.tempFiles[0]) {
          let files = res.tempFiles[0] as UniApp.ChooseImageSuccessCallbackResultFile
          let name = files.path
          let str = name.split('.')[name.split('.').length - 1]
          if (str) {
            if (filter.includes(str)) {
              console.log(str)
              if (files.size >= maxSize) {
                uni.showToast({
                  title: `文件大小限制为${maxSize / 1000 / 1024}Mb`,
                  icon: 'none'
                })
                return
              }
              imgUrl = res.tempFilePaths[0]
              if (isFromAnalysis) {
                upLoadFileRequest({
                  url: baseUrl + '/api/resume/uploadresumeanalyzed',
                  filePath: imgUrl,
                  showLoad: true,
                  name: 'file'
                }).then((res) => {
                  console.log(res)
                  if (res?.statusCode != 200) {
                    uni.showToast({
                      title: '上传失败，请重试',
                      icon: 'none'
                    })
                    return
                  }
                  const { code, data, message } = JSON.parse(res?.data)
                  if (code == 1) {
                    uni.navigateTo({
                      url: `/mypages/registerBlue/baseInfo`
                    })
                  } else {
                    uni.showToast({
                      title: message,
                      icon: 'none'
                    })
                    popupRef.close()
                  }
                })
              } else {
                cropRef.open(imgUrl)
              }
            }
          }
        }
      },
      fail: () => {
        // uni.showToast({
        //   title: '上传失败，请重试',
        //   icon: 'none'
        // })
        uni.getSetting({
          success: (res) => {
            let authStatus = res.authSetting['scope.camera']
            if(!authStatus){
              uni.showModal({
                title: '授权失败',
                content: '需要从您的相机或相册获取图片，请在设置界面打开相关权限',
                success: (res) => {
                  if(res.confirm){
                    uni.openSetting()
                  }
                }
              })
            }
          }
        })
        close()
      },
      complete: () => {
        try {
          uni.hideLoading()
        } catch (err) {}
      }
    })
  }
  const open = () => {
    popupRef?.open('bottom')
  }
  const close = () => {
    popupRef?.close()
  }

  const cancel = () => {
    popupRef.close()
  }
  const comfirm = (url: string) => {
    uni.showLoading({
      title: '图片解析中...',
      mask: true
    })
    
    setTimeout(() => {
      upLoadFileRequest({
      url: baseUrl + '/api/oss/ocrupdatefile',
      filePath: url,
      name: 'file',
      showLoad: true
    })
      .then((res) => {
        
        if (res?.statusCode == 200) {
          const { code, data, message } = JSON.parse(res?.data)
          if (code == 1) {
            emit('success', data)
          } else {
            uni.showToast({
              title: message,
              icon: 'none'
            })
          }
        } else {
          throw new Error('')
        }
      })
      .catch((err) => {
        uni.showToast({
          title: '请重试',
          icon: 'none'
        })
      })
      .finally(() => {
        uni.hideLoading()
        cancel()
      })
    }, 100);
  }
  defineExpose({
    open,
    close
  })
</script>

<template>
  <uni-popup ref="popupRef">
    <view class="popup-content">
      <view class="select_option">
        <view class="item" @tap="camera('camera')"><text>拍照</text></view>
        <view class="line"></view>
        <view class="item" @tap="camera('album')"><text>从相册中选择</text></view>
        <view class="line"></view>
        <view class="item close" @tap="close"><text>取消</text></view>
      </view>
    </view>
  </uni-popup>
  <img-crop ref="cropRef" @cancel="cancel" @confirm="comfirm" />
</template>

<style lang="scss" scoped>
  .popup-content {
    padding: 20rpx;
    width: 100%;
    box-sizing: border-box;

    .select_option {
      width: 100%;
      max-height: 432rpx;
      background: #fff;
      opacity: 1;
      border-radius: 20rpx;
      .line {
        height: 1rpx;
        width: 100%;
        background: #eeeeee;
      }
      .item {
        height: 108rpx;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        text {
          font-size: 32rpx;
          font-family: PingFang SC;
          font-weight: 400;
          color: #333333;
          opacity: 1;
        }
      }
      .close {
        text {
          color: #bbbbbb;
        }
      }
    }
  }
</style>
