<script setup lang="ts">
 
  import { toRefs, watch } from 'vue'

  const crop = $ref<any>(null)
  const emit = defineEmits<{
    (e: 'confirm',value:string):void
    (e: 'cancel'):void
  }>()
  let imgUrl = $ref('')

  const open = (img: string) => {
    imgUrl = img
    crop?.open('right')
  }
  const cancel = () => {
    emit('cancel')
    crop?.close()
  }

  const confirm = (res: any) => {
    if (res.detail.tempFilePath) {
      emit('confirm', res.detail.tempFilePath)
      crop?.close()
    }
  }

  defineExpose({ open })
</script>

<template>
  <uni-popup ref="crop" background-color="#fff">
    <view class="imgView">
      <!-- <image :src="imgUrl" /> -->
      <ling-imgcropper
        :src="imgUrl"
        :cropWidth="200"
        :cropHeight="200"
        @cancel="cancel"
        @confirm="confirm"
      ></ling-imgcropper>
    </view>
  </uni-popup>
</template>

<style lang="scss" scoped>
  .imgView {
    width: 100vw;
    height: 100%;
  }
</style>
