<template>
  <view class="competitiveness">
    <view class="nav_header">
      <uni-nav-bar
        title=""
        left-icon="left"
        :fixed="true"
        :border="false"
        :status-bar="true"
        :background-color="'rgb(255, 255, 255, 0)'"
        :color="'#fff'"
        @clickLeft="back"
      ></uni-nav-bar>
      <view class="bottom_info">
        <view class="left">
          <text class="h1"> 竞争力分析 </text>
          <text class="h2">
            {{ countText }}
          </text>
          
        </view>
        <view class="right" @click="renew">
          <text>立即续费</text>
        </view>
      </view>
    </view>
    <view class="header">
      <HorizontalSelectTab
        v-model="active"
        :option="option"
        :flex="true"
        @change="change"
      ></HorizontalSelectTab>
    </view>
    <scrollCom
      v-if="list"
      ref="scrollRef"
      :refresher-enabled="true"
      :refresher-threshold="35"
      :height="`calc(100vh - 100rpx - 250rpx - ${statusBarHeight}px)`"
      @scrolltolowers="scrolltolower"
      @refresherrefreshs="refresherrefresh"
    >
      <view v-for="item in list" :key="item.positionID" class="record_item"
        ><view
          v-if="item.hasOwnProperty('statusType')"
          class="title"
          @click="goDetail(item.statusType || 0, item.deliveryID || item.deliveryDelLogID)"
        >
          <view class="title_left">
            <text :class="{ h: !deliveryStatus(item.statusType + '', 'flag') }">{{
              item.status
            }}</text>
            <text
              style="margin-left: 10rpx"
              :class="{ h: !deliveryStatus(item.statusType + '', 'flag') }"
              >{{ item.statusTime }}</text
            >
          </view>

          <i class="iconfont icon-icon_more"></i>
        </view>
        <view class="line"></view>
        <view class="content" @click.self="goPositon(item)">
          <view class="row_1">
            <text class="position">{{ item.positionName }}</text>

            <text v-if="active == 2" class="paypackge">{{
              item.paypackage || item.payPackage
            }}</text>
            <text v-else class="time">{{ item.deliverTime || item.publishTime }}</text>
          </view>
          <view class="row_2">
            <text>{{ item.enterpriseName }}</text>
          </view>
          <view v-if="active == 2" class="tips">
            <view
              v-for="(tips, index) in [item.workPlace, item.workAge, item.degreeName].filter(
                Boolean
              )"
              :key="index"
              class="row"
            >
              <text>{{ tips }}</text>
            </view>
          </view>
          <view class="row_3">
            <text v-if="active == 2" class="deliverTime">{{
              item.deliverTime || item.publishTime
            }}</text>
            <text v-else>{{ item.paypackage || item.payPackage }}</text>

            <button
              type="default"
              :class="{
                isActiveClass: item.isCompetion,
               
              }"
              @click.stop="toppingFun(item)"
            >
              {{ item.isCompetion ? '已分析，查看结果' : '竞争力分析' }}
            </button>
          </view>
        </view>
      </view>
    </scrollCom>
    <view v-if="competitiveness?.competionAnalysisFlag == 0" class="bottom_button" @click="renew">
      <image
        src="https://image.gxrc.com/gxrcsite/wxMiniApp/2022/competitiveness/<EMAIL>"
      ></image>
      <text>竞争力分析已到期，点击立即续费</text>
    </view>
    <popup-resume-top-box
      :show="showPopupResumeTopBox"
      content-text="竞争力分析"
      :position-name="activeItem?.positionName"
      :left-count="leftCount"
      @cancel="closeCompetitivenessAnalysisBox"
      @confirm="() => { gotoCompetitivenessAnalysis(activeItem?.positionGuid || '', { positionName: activeItem?.positionName }); handleAnalysisSuccess(); }"
    ></popup-resume-top-box>
  </view>
</template>
<script lang="ts" setup>
  import { ref, reactive, onBeforeMount, nextTick, Ref, computed } from 'vue'
  import HorizontalSelectTab from '@/pages/message/components/HorizontalSelectTab.vue'
  import { HorizontalSelectTabOption } from '@/pages/message/type/index'
  import scrollCom from '@/pages/message/components/scrollCom.vue'
  import { My } from '@/services/my/My'
  import { Position } from '@/services/home/<USER>'
  import type { ScrollView } from '@/pages/message/type/index'
  import { deliveryStatus } from '@/pages/message/collocation/index'
  import { useNavigate } from '@/hooks/page/useNavigate'
  import { useAppStore } from '@/store/modules/app'
  import { Competionservice } from '@/services/my/Competionservice'
  import { useCompetitionAnalysis } from '@/hooks/page/useCompetitionAnalysis'

  import type {
    MyDeliveryListParams,
    AppMyDeliverItemModel,
    MyServiceDetailDto,
    SeekerBusinessInfoViewModel,
    BussDistrict,
    AnalysisPositionDto
  } from '@/services/my/data-contracts'

  import type { AppPositionRecommendItemModel } from '@/services/home/<USER>'

  import { isArray } from '@vue/shared'
import { onShow } from '@dcloudio/uni-app'

  const store = useAppStore()
  const districtId = computed(() => store.districtId)
  const statusBarHeight = computed(() => store.statusBarHeight)

  const { navigateTo } = useNavigate()

  // 竞争力分析Hook
  const {
    showPopupResumeTopBox,
    leftCount,
    executeCompetitionAnalysis,
    closeCompetitivenessAnalysisBox,
    gotoCompetitivenessAnalysis
  } = useCompetitionAnalysis()

  const active = ref(0)
  const option = reactive([
    {
      id: 0,
      item: '使用过的职位',
      isDot: false,
      number: 0
    },
    {
      id: 1,
      item: '投递的职位',
      isDot: false,
      number: 0
    },
    {
      id: 2,
      item: '职位推荐',
      isDot: false,
      number: 0
    }
  ])
  let clonePost: MyDeliveryListParams | null = null
  const myallUrl = import.meta.env.VITE_MYMALL_URL

  const countText = computed(()=>{
    const text =  `已使用${competitiveness.value?.allUsedCount }次/剩余${ competitiveness.value?.leftCount }次`
    let finalText = ''
    switch(competitiveness.value?.competionAnalysisFlag){
      case 0:
      case 1:
        finalText = competitiveness.value?.competionAnalysisDescription || ''
        break
      case -1:
        finalText = text
        break
      case 2:
        finalText = `${competitiveness.value?.competionAnalysisDescription}\r\n${text}（有效期内使用不扣次数）`
        break
      default:
        finalText = text
    }
    
    return finalText
  })

  onBeforeMount(() => {
    clonePost = Object.assign({}, post)
    nextTick(() => {
      initList()
    })
    
  })
  onShow(()=>{
    getInfo()
  })
  const change = (e: HorizontalSelectTabOption) => {
    list.value = []
    post.page = clonePost?.page || 1
    post.pagesize = clonePost?.pagesize || 20
    initList()
  }

  const scrollRef: Ref<ScrollView | null> = ref(null)
  type all = AnalysisPositionDto & AppMyDeliverItemModel & AppPositionRecommendItemModel
  const list: Ref<all[]> = ref([])
  const scrolltolower = async () => {
    const page = post.page + 1
    let data = await getList({ page: page, pagesize: post.pagesize })
    console.log(22,data);
    if (isArray(data)) {
      //如果是职位推荐 注意返回的数据类型
      if (data && data.length > 0) {
        list.value.push(...data)
        post.page = page
        scrollRef.value?.AddCallBack(true)
      } else {
        scrollRef.value?.AddCallBack(false)
      }
    } else if (data.items) {
      //如果是使用过的职位 和投递的职位
      if (data.items && data.items.length > 0) {
        list.value.push(...data.items)
        post.page = page
        scrollRef.value?.AddCallBack(true)
      } else {
        scrollRef.value?.AddCallBack(false)
      }
    }
  }

  const refresherrefresh = async () => {
    scrollRef.value?.hide(false)
    post.state = active.value
    post.page = clonePost?.page || 1
    post.pagesize = clonePost?.pagesize || 20
    await initList()
    nextTick(() => {
      scrollRef.value?.closeRefresh()
      scrollRef.value?.hide(true)
    })
  }

  const getList = (obj: MyDeliveryListParams) => {
    if (active.value == 0) {
      return My.myMyanalysislistList({ page: obj.page, pagesize: obj.pagesize })
    } else if (active.value == 1) {
      return My.myDeliveryList({ state: 0, ordertype: 0, page: obj.page, pagesize: obj.pagesize })
    } else {
      return Position.RecommendListList({
        districtId: districtId.value as BussDistrict,
        positionCareeID: 0,
        page: obj.page,
        pageSize: obj.pagesize
      })
    }
  }

  const competitiveness: Ref<null | SeekerBusinessInfoViewModel> = ref(null)
  const getInfo = () => {
    My.myServicesList({ type: 100001 }).then((data) => {
      console.log(data)
      let res = data.data as MyServiceDetailDto

      if (res.businessInfoList) {
        console.log(res.businessInfoList)

        let items = res.businessInfoList.find((item) => item.businessType == 100001)

        if (items) {
          competitiveness.value = items
        }
      }
    })
  }

  const post = reactive({
    page: 1,
    pagesize: 20,
    state: 0
  })

  async function initList() {
    //初始化列表
    scrollRef.value?.loading()
    let data = await getList(post)
    data = data?.data || data
    console.log('---------data')
    console.log(data)

    if (isArray(data)) {
      //如果是职位推荐 注意返回的数据类型
      list.value = data || []
      if (data && data.length < post.pagesize) {
        scrollRef.value?.AddCallBack(false)
      } else {
        scrollRef.value?.AddCallBack(true)
      }
    } else if (data.items) {
      //如果是使用过的职位 和投递的职位
      list.value = data.items || []
      if (data.items && data.items.length < post.pagesize) {
        scrollRef.value?.AddCallBack(false)
      } else {
        scrollRef.value?.AddCallBack(true)
      }
    }
  }

  const goDetail = (type: number, id?: number) => {
    if (id) navigateTo(`/pages/message/deliveryDetails?id=${id}&type=${type}`)
  }

  const goPositon = (item: object) => {
    navigateTo(`/pages/job/position?id=${item.positionGuid}&trackingGuid=${item.trackingGuid}`)
  }

  const back = () => {
    uni.navigateBack({
      delta: 1,
      fail() {
        uni.switchTab({ url: '/pages/index/index' })
      }
    })
  }

  const activeItem: Ref<AppMyDeliverItemModel | null> = ref(null)
  const toppingFun = async (item: AppMyDeliverItemModel) => {
    
    
    activeItem.value = item
    
    // 如果已经分析过，直接跳转到结果页面
    if (item.isCompetion) {
      const url = `${myallUrl}/MyMall/CompetionAnalysis/CompetionAnlsRslt?positionGuid=${item.positionGuid}`
      uni.navigateTo({
        url: `/pages/web-view/index?src=${encodeURIComponent(url)}&login=true`
      })
      return
    }
    
    // 使用Hook处理竞争力分析逻辑
    await executeCompetitionAnalysis(item.positionGuid || '', {
      positionName: item.positionName
    })
  }

  // Hook回调，更新成功后的处理
  const handleAnalysisSuccess = () => {
    if (activeItem.value) {
      activeItem.value.isCompetion = true
      getInfo()
    }
  }

  const renew = () => {
    uni.navigateTo({
      url: `/pages/web-view/index?src=${encodeURIComponent(
        `${myallUrl}/MyMall/CompetionAnalysis/Index`
      )}&login=true`
    })
  }
</script>
<style lang="scss" scoped>
  .competitiveness {
    .bottom_button {
      position: fixed;
      width: 690rpx;
      height: 88rpx;
      left: 50%;
      top: 90%;
      transform: translate(-50%, 0);
      background: url('https://image.gxrc.com/gxrcsite/wxMiniApp/2022/competitiveness/<EMAIL>');
      display: flex;
      justify-content: center;
      padding: 0 76rpx;
      align-items: center;
      box-sizing: border-box;

      border-radius: 18rpx;
      image {
        width: 50rpx;
        height: 38rpx;
      }
      text {
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 400;
        color: #ffffff;
        opacity: 1;
        margin-left: 8rpx;
      }
    }
    background: #f7f9fc;
    .nav_header {
      width: 100%;
      height: 350rpx;
      background-image: url('https://image.gxrc.com/gxrcsite/wxMiniApp/2022/my/<EMAIL>');
      background-size: 100% 100%;
      .bottom_info {
        height: 118rpx;
        padding: 0 30rpx;
        margin-top: 14rpx;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        .left {
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          .h1 {
            font-size: 48rpx;

            font-family: PingFang SC;
            font-weight: Regular;
            color: #ffffff;
            opacity: 1;
          }
          .h2 {
            font-size: 26rpx;
            font-family: PingFang SC;
            font-weight: 400;
            color: #ffffff;
            opacity: 1;
            white-space: nowrap;
          }
        }
        .right {
          width: 168rpx;
          height: 60rpx;
          background: #fff;
          opacity: 1;
          border-radius: 40rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          text {
            font-size: 26rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #243fe6;
            opacity: 1;
          }
        }
      }
    }
    .header {
      border-bottom: 2rpx solid #f2f2f2;
    }
    .record_item {
      margin: 24rpx 30rpx;
      background: #fff;
      padding: 0 32rpx;
      box-sizing: border-box;
      .content {
        padding: 32rpx 0;
        box-sizing: border-box;
        .row_3 {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .deliverTime {
            font-size: 22rpx;
            font-family: PingFang SC;
            font-weight: 400;
            color: #666666;
            opacity: 1;
          }
          text {
            font-size: 32rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #fe5c5b;
            opacity: 1;
          }
          button {
            height: 58rpx;
            opacity: 1;
            border-radius: 30rpx;

            margin: 0;
            padding: 0;

            font-size: 24rpx;
            font-family: PingFang SC;
            font-weight: 400;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 24rpx;
            opacity: 1;
            line-height: normal;

            border: none;
            background: #457ccf;
            color: #fff;
          }
          button::after {
            border: none;
          }
          .isActiveClass {
            border: 2rpx solid #457ccf;
            background: #fff;
            color: #457ccf;
          }
          .disable {
            background: #f4f4f4;
            color: #bbbbbb;
            border: none;
          }
        }
        .tips {
          display: flex;
          justify-content: flex-start;
          flex-wrap: wrap;
          margin-top: 15rpx;
          .row {
            padding: 8rpx 16rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 50rpx;
            box-sizing: border-box;
            margin-bottom: 10rpx;

            border: 2rpx solid #f2f2f2;

            background: #fff;
            margin-right: 12rpx;
            text {
              font-size: 22rpx;
              font-family: PingFang SC;
              font-weight: 400;
              color: #6e7a83;
              opacity: 1;
            }
          }
        }
        .row_2 {
          margin-top: 8rpx;
          text {
            font-size: 26rpx;
            font-family: PingFang SC;
            font-weight: 400;
            color: #666666;
            opacity: 1;
          }
        }
        .row_1 {
          display: flex;
          box-sizing: border-box;
          justify-content: space-between;
          align-items: center;
          .position {
            font-size: 32rpx;
            font-family: PingFang SC;
            font-weight: 600;
            color: #333333;
            opacity: 1;
          }
          .time {
            font-size: 22rpx;
            font-family: PingFang SC;
            font-weight: 400;
            color: #666666;
            opacity: 1;
          }
          .paypackge {
            font-size: 32rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #fe5c5b;
            opacity: 1;
          }
        }
      }
      .line {
        border: 2rpx solid #f7f7f7;
        opacity: 1;
      }
      .title {
        padding: 24rpx 0;
        display: flex;
        box-sizing: border-box;
        justify-content: space-between;
        align-items: center;
        .title_left {
          display: flex;
          align-items: center;
          text {
            font-size: 24rpx;
            font-family: PingFang SC;
            font-weight: 400;
            color: #457ccf;
            opacity: 1;
          }
        }
        .icon-icon_more {
          color: #cfcfcf;
        }

        .h {
          color: #bbbbbb !important;
        }
      }
    }
  }
</style>
