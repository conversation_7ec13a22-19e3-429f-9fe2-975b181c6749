<template>
  <view class="addressedit">
    <uni-nav-bar
      title=""
      background-color="#f7f9fc"
      left-icon="left"
      :fixed="true"
      @clickLeft="back"
      :border="false"
      :statusBar="true"
    />
    <view class="title">编辑收货地址</view>
    <view class="form">
      <uni-forms
        ref="form"
        v-model="address"
        :rules="rules"
        :border="true"
        label-position="top"
        err-show-type="toast"
        label-width="150"
      >
        <uni-forms-item label="收件人姓名" name="receiverName">
          <uni-easyinput
            v-model="address.receiverName"
            :inputBorder="false"
            :clearable="false"
            placeholder="请输入姓名"
            confirm-type="next"
            :maxlength="20"
            :trim="true"
          />
        </uni-forms-item>
        <uni-forms-item label="手机号码" name="receiverPhone">
          <uni-easyinput
            v-model="address.receiverPhone"
            :inputBorder="false"
            :clearable="false"
            placeholder="请输入手机号码"
            confirm-type="next"
            :maxlength="11"
            :trim="true"
          />
        </uni-forms-item>
        <uni-forms-item label="所在地区" name="locationId">
          <uni-data-picker
            v-model="address.locationId"
            placeholder="请选择省/市"
            popup-title="请选择"
            :border="false"
            :localdata="cityJson"
            :map="{ text: 'name', value: 'id' }"
            @change="change"
          />
        </uni-forms-item>
        <uni-forms-item label="详细地址" name="detailedAddress">
          <uni-easyinput
            v-model="address.detailedAddress"
            :inputBorder="false"
            :clearable="false"
            placeholder="请输入详细地址"
            confirm-type="next"
            :maxlength="40"
            :trim="true"
          />
        </uni-forms-item>
        <uni-forms-item class="default" label="设置为默认地址"  label-position="left">
          <switch
            color="#608BF8"
            class="switch"
            :checked="address.isDefault"
            @change="changeDefault"
          />
        </uni-forms-item>
      </uni-forms>
    </view>

    <save-button
      :left-button="(address.id ?? 0) > 0"
      left-text="删除"
      @left-click="del"
      @right-click="save"
    />
  </view>
</template>

<script setup lang="ts">
  import saveButton from '@/mypages/my/resume/components/save-button.vue'
  import { JobseekerAddressDto } from '@/services/my/data-contracts'
  import { Myaddress } from '@/services/my/Myaddress'
  import { useOptionsStore } from '@/store/modules/options'
  import { handlerError } from '@/utils'
  import { useResumeOperation } from '@/hooks/resume/useResumeOperation'
  import { onLoad } from '@dcloudio/uni-app'

  let address = $ref<JobseekerAddressDto>({
    id: 0,
    receiverName: '',
    receiverPhone: '',
    locationId: 0,
    areaStr: '',
    detailedAddress: '',
    isDefault: 0
  })

  const form = $ref<any>(null)

  const rules = $ref<Record<string, any>>({
    receiverName: {
      rules: [
        {
          required: true,
          errorMessage: '请输入收件人姓名'
        }
      ]
    },
    receiverPhone: {
      rules: [
        {
          required: true,
          errorMessage: '请输入收件人手机号码'
        },
        {
          pattern: /^1[3456789]\d{9}$/,
          errorMessage: '请输入正确的手机号码'
        }
      ]
    },
    locationId: {
      rules: [
        {
          pattern: /^([1-9][0-9]*)$/,
          errorMessage: '请选择所在地区'
        }
      ]
    },
    detailedAddress: {
      rules: [
        {
          required: true,
          errorMessage: '请输入详细地址'
        }
      ]
    }
  })

  const height = uni.getSystemInfoSync().windowHeight + 'px'

  const store = useOptionsStore()

  const { saveItem, deleteItem } = useResumeOperation()
  onLoad(async (options) => {
    await store.getAreaCity()
    const { Id } = options
    const id = Number(Id)
    if (id) {
      const result = await Myaddress.myaddressGetaddressDetail(id).catch((err) => err)
      if (result.code === 0) {
        handlerError(result)
        return
      }
      address = result.data
    }
  })

  const cityJson = $computed(() => store.areaCity)

  const change = (data: any) => {
    console.log(data)
    const { detail } = data
    const { value } = detail
    if (value.length === 3) {
      address.locationId = value[2].value
      address.areaStr = value.map((item: any) => item.text).join('/')
    }
  }

  const changeDefault = (data: any) => {
    const { detail } = data
    address.isDefault = Number(detail.value)
  }

  const save = () => {
    form.validate(async (err: any, res: any) => {
      if (!err) {
        const fn = () => Myaddress.myaddressEdit(address)
        await saveItem(fn, {})
      }
    })
  }

  const del = async () => {
    const fn = () => Myaddress.myaddressDelete(address.id ?? 0)
    await deleteItem(fn, (address.id ?? 0) > 0, '确定要删除该收货地址吗？', {})
  }

  const back = () => {
    uni.navigateBack({
      delta: 1,
      fail: function (err) {
        uni.switchTab({
          url: '/mypages/my/address/index'
        })
      }
    })
  }
</script>

<style lang="scss" scoped>
  .addressedit {
    background: #f7f9fc;
    min-height: v-bind(height);
    .title {
      margin: 22px 0 0 15px;
      font-size: 24px;
      font-weight: 600;
    }
    .form {
      background: #fff;
      margin: 24px 15px 100px 15px;

      ::v-deep .uni-forms-item__label {
        padding-left: 16px;
      }
      ::v-deep .uni-easyinput__content-input {
        padding-left: 16px !important;
      }

      
      ::v-deep .uni-data-tree .uni-data-tree-input .selected-area {
        padding-left: 12px;
      }
      ::v-deep .uni-data-tree .uni-data-tree-input .placeholder {
        color: #bbbbbb;
        font-size: 15px;
      }
      ::v-deep .input-value{
        font-size: 15px;
      }
      ::v-deep .uni-data-tree .uni-data-tree-input .input-arrow{
        // width: 7px;
        // height: 7px;
        border-right: 1px solid #999;
        border-left: 0;
        // border-bottom: 1px solid #999;
      }
      ::v-deep .uni-data-tree .uni-data-tree-input .selected-area .selected-list {
        padding-left: 0;
      }

      ::v-deep .default .uni-forms-item__label .label-text{
        font-size: 14px !important;
        font-weight: bold;
      }
    }
    .switch {
      float: right;
      transform: scale(0.65);
      margin-top: 3px;
    }
  }
</style>
