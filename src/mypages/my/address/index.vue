<template>
  <view class="addressview">
    <uni-nav-bar
      :title="title"
      background-color="#f7f9fc"
      left-icon="left"
      :fixed="true"
      @clickLeft="back"
      :border="false"
      :statusBar="true"
    />
    <view class="addressview-content">
      <view
        class="addressview-content-item"
        v-for="(item, index) in addressList.adlist"
        :key="item.id"
        @tap="edit(item.id ?? 0)"
      >
        <view>
          <view class="header">
            <view class="title">
              <text>{{ item.receiverName }}</text>
            </view>
            <view class="phone">
              <text>{{ item.receiverPhone }}</text>
            </view>
            <view class="tag" v-if="item.isDefault">默认</view>
          </view>
          <view class="address">
            <view style="width:85%"><text>{{ item.loactionStr }}</text></view>
            <view><i class="iconfont icon-bianji1 icon"></i></view>
          </view>
        </view>
        <!-- <view class="right">
          
        </view> -->
        <view class="line" v-if="index != (addressList.adlist?.length ?? 0) - 1" />
      </view>
    </view>
    <save-button v-if="isAdd" right-text="添加新地址" @right-click="add" />
  </view>
</template>

<script setup lang="ts">
  import { Myaddress } from '@/services/my/Myaddress'
  import { onLoad } from '@dcloudio/uni-app'
  import { handlerError } from '@/utils'
  import { MyAddressDto } from '@/services/my/data-contracts'
  import saveButton from '@/mypages/my/resume/components/save-button.vue'

  let addressList = $ref<MyAddressDto>({})
  let title = $ref<string>('我的收货地址')
  let isAdd = $ref(false)
  const height = uni.getSystemInfoSync().windowHeight + 'px'

  onLoad(async () => {
    const result = await Myaddress.myaddressAddresslistList().catch((err) => err)
    if (result.code === 0) {
      handlerError(result)
      return
    }
    addressList = result.data
    title = `我的收货地址(${addressList.adlist?.length}/5)`
    isAdd = addressList.isCanAddMore ?? false
  })

  const add = () => {
    uni.navigateTo({
      url: '/mypages/my/address/edit'
    })
  }

  const edit = (id: number) => {
    uni.navigateTo({
      url: '/mypages/my/address/edit?Id=' + id
    })
  }

  const back = () => {
    uni.navigateBack({
      delta: 1,
      fail: function (err) {
        uni.switchTab({
          url: '/pages/my/index'
        })
      }
    })
  }
</script>

<style lang="scss" scoped>
  .addressview {
    background: #f7f9fc;
    min-height: v-bind(height);
    &-content {
      margin: 12px 15px;
      &-item {
        background: #fff;
        padding: 16px;

        .header {
          display: flex;
          //align-content: center;
          align-items: center;
          .title {
            font-size: 18px;
            font-weight: 600;
          }
          .phone {
            padding-left: 8px;
            font-size: 14px;
            color: #bbbbbb;
          }
          .tag {
            font-size: 12px;
            color: #228efd;
            padding: 2px 10px;
            background: #eef5ff;
            margin-left: 16px;
          }
        }
        .address {
          display: flex;
          width: 100%;
          justify-content: space-between;
          padding-top: 12px;
          font-size: 14px;
          color: #666666;
          word-wrap: break-word;
          word-break: break-all;
          .icon{
            color: #CCCCCC;
          }
        }
        .right {
          float: right;
          margin-top: -25px;
        }
        .line {
          border-bottom: 1px solid #eeeeee;
          padding: 16px;
        }
      }
    }
  }
</style>
