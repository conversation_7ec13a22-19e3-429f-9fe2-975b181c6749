<template>
  <view class="collection_all">
    <view style="background: #fff" class="tabbar">
      <uni-segmented-control
        :current="current"
        :values="items"
        :style-type="'text'"
        :active-color="'#457CCF'"
        @clickItem="onClickItem"
      />
    </view>
    <scrollCom
      ref="scrollRef"
      :refresher-enabled="true"
      :refresher-threshold="35"
      :height="`calc(100vh - ${parentHeight} - 100rpx)`"
      :contentnomore="contentnomore"
      @scrolltolowers="scrolltolower"
      @refresherrefreshs="refresherrefresh"
    >
      <view
        v-for="(item, index) in list"
        :key="index"
        class="items"
        @longtap.stop="longtap(item)"
        @touchstart="touchstartE()"
        @touchend="touchendE()"
      >
        <view v-if="item.isTop" class="tips">已置顶</view>
        <positionItem
          v-if="current == 0"
          :items="{
            title: item.positionName || '',
            title2: item.enterpriseName || '',
            salary: item.payPackage || '',
            block: [item.workDistrict || '', item.workAge || '', item.degree || ''],
            image: item.logoUrl || '',
            tips: [
              item.enterpriseEmployeeNumberName || '',
              item.enterprisePropertyName || '',
              item.enterpriseIndustryName || ''
            ]
          }"
          @clickTop="clickTop(item)"
          @clickBottom="clickBottom(item)"
        ></positionItem>
        <view
          v-else
          class="item_child"
          @click="clickBottom(item)"
          @touchstart="touchstartE()"
          @touchend="touchendE()"
        >
          <enterpriseItem
            :items="{
              title2: item.enterpriseName || '',
              image: item.logoUrl || '',
              tips: [
                item.enterpriseEmployeeNumberName || '',
                item.enterprisePropertyName || '',
                item.enterpriseIndustryName || ''
              ]
            }"
            :border-radius="'0'"
            :image-width="'92rpx'"
            :font-size="'32rpx'"
          ></enterpriseItem>
          <view class="right_box">
            <view class="line"></view>
            <view class="nav">
              <view class="text"
                >在招职位<text>{{ item.positionCount }}</text
                >个</view
              >
              <view class="icon"><i class="iconfont icon-icon_more"></i></view>
            </view>
          </view>
        </view>
      </view>
      <template #bottom>
        <view v-if="list.length <= 0">
          <noData></noData>
        </view>
        <recommendationTitle v-if="positionList.length > 0"></recommendationTitle>
        <position-card
          v-for="item in positionList"
          :key="item.positionGuid"
          :option="{
            postionName: item.positionName || '',
            label: {
              jp: item.emergencyRrecruitmentFlag,
              bys: item.isReceiveGraduate,
              dz: item.isAgentRecruit
            },
            salary: item.payPackage || '',
            infos: [item.workPlace, item.workAge, item.degreeName, item.enterpriseIndustryName],
            // tips: '近日已有13人沟通该职位',
            picture: item.logoUrl || '',
            enterpriseName: item.enterpriseName || '',
            nature: item.enterpriseProperty || '',
            scale: item.enterpriseEmployeeNumber || '',
            activeText: item.activationTags[0],
            positionGuid: item.positionGuid,
            showClose: true
          }"
          @close="openpopupRejectjobrecommendation(item.positionID)"
        ></position-card>
      </template>
    </scrollCom>

    <uni-popup ref="popup" type="center" background-color="#fff">
      <view class="popup_button">
        <view class="row"
          ><view @click="topping">
            <text>{{ activeItem?.isTop ? '取消置顶' : '置顶' }}</text></view
          ></view
        >
        <view class="line"></view>
        <view class="row"
          ><view @click="cancel"> <text style="color: #f65f58">取消收藏</text></view></view
        >
      </view>
    </uni-popup>

    <popupRejectjobrecommendation
      @success="handleSuccess"
      ref="popupRejectjobrecommendationRef"
    ></popupRejectjobrecommendation>
  </view>
</template>
<script lang="ts" setup>
  import { computed, nextTick, onBeforeMount, reactive, ref, Ref } from 'vue'
  import type { ScrollView } from '@/pages/message/type/index'
  import scrollCom from '@/pages/message/components/scrollCom.vue'
  import { My } from '@/services/my/My'
  import positionItem from '@/pages/message/components/positionItem.vue'
  import enterpriseItem from '@/pages/message/components/enterpriseItem.vue'
  import type { AppFavoriteListItemModel } from '@/services/my/data-contracts'
  import { onLoad } from '@dcloudio/uni-app'
  import type { UniPopup } from '@/pages/message/type'
  import { useNavigate } from '@/hooks/page/useNavigate'
  import noData from '@/pages/message/components/noData.vue'
  import recommendationTitle from '@/pages/message/components/recommendationTitle.vue'

  import { Interaction } from '@/services/my/Interaction'
  import { IndexPositionListItem } from '@/services/my/data-contracts'
  import positionCard from '@/pages/message/components/positionCard.vue'
  // #ifdef  MP-ALIPAY
  import { reportCmPV } from '@/utils/cloudMonitorHelper.js'
  // #endif
  import popupRejectjobrecommendation from '@/pages/message/components/popupRejectjobrecommendation.vue'

  const popupRejectjobrecommendationRef = ref<InstanceType<
    typeof popupRejectjobrecommendation
  > | null>(null)

  const openpopupRejectjobrecommendation = (positionId: number) => {
    popupRejectjobrecommendationRef.value?.open(positionId)
  }

  const contentnomore = ref('只显示近一年数据')
  const handleSuccess = (positionId: number) => {
    positionList.value = positionList.value.filter((item) => item.positionID !== positionId)
  }

  const positionList = ref<IndexPositionListItem[]>([])
  const getPositions = async () => {
    const data = await Interaction.apiInteractionPosrecommendGet()

    positionList.value = data
    console.log(positionList.value)
  }

  const { navigateTo } = useNavigate()
  type MyMycollectionListParamsClone = {
    page?: number
    pagesize?: number
    state?: number
  }

  let clonePost: MyMycollectionListParamsClone | null = null

  onLoad((option) => {
    //当作组件引入时不触发
    // #ifdef MP-ALIPAY
    reportCmPV({ title: '我的收藏', query: option })
    // #endif
  })

  const touchT = ref(0) // 触屏开始时间
  const touchE = ref(0) // 触屏结束时间
  const touchstartE = () => {
    touchT.value = new Date().getTime()
  }
  const touchendE = () => {
    touchE.value = new Date().getTime()
  }

  onBeforeMount(() => {
    clonePost = Object.assign({}, post)
    nextTick(() => {
      initList()
    })
  })

  type Props = {
    parentHeight: string
  }
  const props = withDefaults(defineProps<Props>(), {
    parentHeight: '0rpx'
  })

  const scrollRef: Ref<ScrollView | null> = ref(null)
  const list: Ref<AppFavoriteListItemModel[]> = ref([])

  const current = ref(0)
  const items = ref(['职位', '公司'])
  const onClickItem = (e: Record<string, number>) => {
    positionList.value = []
    if (current.value !== e.currentIndex) {
      list.value = []
      current.value = e.currentIndex
      post.state = current.value
      post.page = clonePost?.page || 1
      post.pagesize = clonePost?.pagesize || 20

      initList()
    }
  }

  const scrolltolower = async () => {
    const page = post.page + 1
    let data = await getList({ page: page, pagesize: post.pagesize, state: post.state })
    if (data.items && data.items.length > 0) {
      list.value.push(...data.items)
      post.page = page
      scrollRef.value?.AddCallBack(true)
    } else {
      scrollRef.value?.AddCallBack(false)
      getPositions()
    }
  }

  const refresherrefresh = async () => {
    scrollRef.value?.hide(false)

    post.page = clonePost?.page || 1
    post.pagesize = clonePost?.pagesize || 20
    await initList()
    nextTick(() => {
      scrollRef.value?.closeRefresh()
    })
  }

  const getList = (obj: MyMycollectionListParamsClone) => {
    if (obj.state == 0) {
      return My.myMycollectionList({ page: obj.page, pagesize: obj.pagesize })
    } else {
      return My.myMycompanycollectionList({ page: obj.page, pagesize: obj.pagesize })
    }
  }

  const post = reactive({
    page: 1,
    pagesize: 20,
    state: 0
  })

  async function initList() {
    //初始化列表
    uni.showLoading({
      title: '加载中'
    })
    scrollRef.value?.loading()
    let data = await getList(post)
    list.value = data.items || []

    if (data.items && data.items.length < post.pagesize) {
      if (data.items.length == 0) {
        contentnomore.value = ' '
      } else {
        contentnomore.value = ' '
        scrollRef.value?.hide(false)
      }
      getPositions()
    } else {
      scrollRef.value?.AddCallBack(true)
    }
    uni.hideLoading()
  }

  const popup: Ref<UniPopup | null> = ref(null)
  const topping = () => {
    if (activeItem.value?.collectionId) {
      if (current.value == 0) {
        My.mySettopcollection({ collectionid: activeItem.value?.collectionId })
          .then(() => {
            uni.showToast({
              title: '置顶成功',
              icon: 'none'
            })
            initList()
          })
          .catch((e) => {
            uni.showToast({
              title: '置顶失败',
              icon: 'none'
            })
            console.log(e)
          })
          .finally(() => {
            uni.hideLoading()
            popup.value?.close()
          })
      } else {
        My.mySettopCompanycollection({ collectionid: activeItem.value?.collectionId })
          .then(() => {
            uni.showToast({
              title: '置顶成功',
              icon: 'none'
            })
            initList()
          })
          .catch((e) => {
            uni.showToast({
              title: '置顶失败',
              icon: 'none'
            })
            console.log(e)
          })
          .finally(() => {
            uni.hideLoading()
            popup.value?.close()
          })
      }
    } else {
      uni.showToast({
        title: '数据有误',
        icon: 'none'
      })
      popup.value?.close()
    }
  }
  const cancel = () => {
    uni.showLoading({
      title: '加载中'
    })
    if (activeItem.value?.collectionId) {
      if (current.value == 0) {
        //取消收藏职位
        My.myCollection({ collectionids: [activeItem.value?.collectionId] })
          .then(() => {
            uni.showToast({
              title: '取消收藏成功',
              icon: 'none'
            })
            initList()
          })
          .catch((e) => {
            uni.showToast({
              title: '取消收藏失败',
              icon: 'none'
            })
            console.log(e)
          })
          .finally(() => {
            uni.hideLoading()
            popup.value?.close()
          })
      } else {
        //取消收藏公司
        My.myCompanycollection({ collectionids: [activeItem.value?.collectionId] })
          .then(() => {
            uni.showToast({
              title: '取消收藏成功',
              icon: 'none'
            })
            initList()
          })
          .catch((e) => {
            uni.showToast({
              title: '取消收藏失败',
              icon: 'none'
            })
            console.log(e)
          })
          .finally(() => {
            uni.hideLoading()
            popup.value?.close()
          })
      }
    } else {
      uni.showToast({
        title: '数据有误',
        icon: 'none'
      })
      popup.value?.close()
    }
  }
  const activeItem: Ref<AppFavoriteListItemModel | null> = ref(null)
  const longtap = (item: AppFavoriteListItemModel) => {
    activeItem.value = item
    popup.value?.open()
  }

  const parentHeight = computed(() => props.parentHeight)

  const clickTop = (item: AppFavoriteListItemModel) => {
    if (touchE.value - touchT.value < 350) {
      if (current.value == 0) {
        navigateTo(`/pages/job/position?id=${item.positionGuid}`)
      } else {
        navigateTo(`/pages/job/company?id=${item.enterPriseGuid}`)
      }
    }
  }
  const clickBottom = (item: AppFavoriteListItemModel) => {
    console.log(item)
    if (touchE.value - touchT.value < 350) {
      if (current.value == 0) {
        navigateTo(`/pages/job/position?id=${item.positionGuid}`)
      } else {
        navigateTo(`/pages/job/company?id=${item.enterPriseGuid}`)
      }
    }
  }
</script>
<style lang="scss" scoped>
  .collection_all {
    background: #f5f7fb;
    overflow: hidden;
    .tabbar {
      height: 100rpx;

      border-bottom: 1rpx solid #eeeeee;
      ::v-deep .segmented-control {
        height: 100rpx;
      }
      ::v-deep .segmented-control__item--text {
        padding: 25rpx 0;
        font-size: 34rpx;
        font-weight: bold;
      }
    }

    .popup_button {
      border-radius: 8rpx;
      padding: 0;
      box-sizing: border-box;
      width: 80vw;
      .row {
        height: 104rpx;
        width: 100%;
        view {
          display: flex;
          justify-content: center;
          align-content: center;
          text {
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 400;
            color: #666666;
            line-height: 104rpx;
            opacity: 1;
          }
        }
      }
      .line {
        height: 2rpx;
        background: #f2f2f2;
      }
    }
    .items {
      border-bottom: #f7f9fc solid 16rpx;
      background: #fff;
      position: relative;
      .tips {
        position: absolute;
        left: 0;
        top: 0;
        font-size: 24rpx;
        color: #fff;
        background: #457ccf;
        padding: 5rpx 10rpx;
        border-top-right-radius: 15rpx;
        border-bottom-right-radius: 15rpx;
      }
      .item_child {
        padding: 40rpx 30rpx 24rpx 30rpx;
        background: #fff;
        box-sizing: border-box;
        .right_box {
          margin-left: 96rpx;
          .line {
            height: 2rpx;
            margin: 40rpx 0 24rpx 0;
            background: #f2f2f2;
          }
          .nav {
            display: flex;
            justify-content: space-between;
            .text {
              font-size: 24rpx;
              font-family: PingFang SC;
              font-weight: 400;
              color: #999999;
              opacity: 1;
              text {
                color: #457ccf;
              }
            }
            .icon {
              .icon-icon_more {
                font-size: 22rpx;
                color: #999999;
              }
            }
          }
        }
      }
    }
  }
</style>
