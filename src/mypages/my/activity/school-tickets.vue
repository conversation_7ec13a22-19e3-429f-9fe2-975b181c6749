<script setup lang="ts">
  import { computed, ref, watch, watchEffect } from 'vue'
  import { useAppStore } from '@/store/modules/app'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import { MeetingMy } from '@/services/my/Meetingmy'
  import { ApiError } from '@/utils/ApiError'
  import choosePopup from './component/choosePopup.vue'
  import { canvasFormateStrForTickets } from '@/utils'

  const isLogin = ref(false)

  const isTakeTicket = ref(false)
  const chooseRef = ref<any>(null)
  //生成的临时图片
  const imgUrl = ref('')
  const phone = ref('')
  const qrImg = ref('https://image.gxrc.com/gxrcsite/global/app_code.png')
  const defaultBg = ref('')
  const bottomBg = ref('https://image.gxrc.com/gxrcsite/wxMiniApp/2024/<EMAIL>')
  const ticketsType = ref(0)
  const isReg = ref(false)
  const meetingId = ref(0)
  let canvas: Record<string, any> = {}
  let ctx = $ref<CanvasRenderingContext2D>()
  let renderWidth = $ref<number>()
  let renderHeight = $ref<number>()
  let dx = $ref<number>()
  let dpr = $ref<number>()
  const info = uni.getWindowInfo()

  const height = info.screenHeight
  const width = info.screenWidth
  const safeBottom = info.safeAreaInsets?.bottom + 'px'
  const canvasHeight = `${height}px`
  const canvasWidth = `${width}px`
  const refTop = ref('30%')
  refTop.value = height > 667 ? '30%' : '26%'
  //canvas x坐标的比例
  const ratio = width > 375 ? width / 375 : width <= 320 ? 0.73 : 1
  const heightRatio = height / 812

  const buttonBgColor = ref('')

  const title = ref('')
  const time = ref('')
  const isSubmit = ref(false)
  onLoad(async (options) => {
    if(!options?.meetingId){
      uni.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        uni.switchTab({
          url:'/pages/index/index',
        })
      }, 1000);
      return
    }
    let token = options?.token || ''
    meetingId.value = options?.meetingId || 0
    ticketsType.value = options?.type || 0
    buttonBgColor.value = ticketsType.value == 0 ? '#24C48E' : '#5B8DF6'
    // defaultBg.value =
    //   ticketsType.value == 0
    //     ? defaultBg.value
    //     : 'https://image.gxrc.com/gxrcsite/wxMiniApp/2024/<EMAIL>'
    bottomBg.value =
      ticketsType.value == 0
        ? bottomBg.value
        : 'https://image.gxrc.com/gxrcsite/wxMiniApp/2024/<EMAIL>'
    await getTicket(token)
  })

  watchEffect(() => {
    refTop.value =
      height > 736
        ? ticketsType.value == 0
          ? '34%'
          : '36%'
        : ticketsType.value == 0
        ? '30%'
        : '33%'
  })
  
  const getTicket = async (token: string) => {
    uni.showLoading({
      title: '加载中...'
    })
    const {code:c,data: d} = await MeetingMy.meeingInfoForTickets(meetingId.value).catch((err) => err as ApiError)
    
    if(c == 1){
      title.value = d.meetingTitle
      time.value = `${d.meetingDate}·${d.meetingAddress}`
      defaultBg.value = d.bgImg
    }
    const { code, data } = await MeetingMy.getTicket({ token,meetingType:1,meetingId: meetingId.value }).catch((err) => err as ApiError)

    if (code == 1) {
      isTakeTicket.value = data.isTakeTicket
      isLogin.value = true
      if (isTakeTicket.value) {
        qrImg.value = data.qrCode
        phone.value = data.phone
        isReg.value = data.step != 'noresume'
      }
    }
    uni.hideLoading()
  }

  const success = async () => {
    uni.reLaunch({
      url: '/mypages/my/activity/school-tickets?type=' + ticketsType.value + '&meetingId=' + meetingId.value
    })
  }
  
  const collection = async()=>{
    if(isSubmit.value) return
    isSubmit.value = true
    uni.showLoading({title: '领取中',mask:true})
    const { code, data, message } = await MeetingMy.ticketcollection({
      isAutonomousRegion: 0,
      meetingType: 1,
      meetingId: meetingId.value
    }).catch((err) => err as ApiError)
    uni.hideLoading()
    isSubmit.value = false
    if (code == 1) {
      uni.showToast({
        title: '提交成功',
        icon: 'none'
      })
      success()
      return
    } else {
      uni.showToast({
        title: message || '提交失败',
        icon: 'none'
      })
      return
    }
    
  }
  const startDraw = async () => {
    if(isSubmit.value)return
    isSubmit.value = true
    if (!imgUrl.value) {
      uni.showLoading({ title: '正在生成' })
      wx.createSelectorQuery()
        .select('#canvasId')
        .fields({ node: true, size: true })
        .exec(async (res) => {
          // Canvas 对象

          canvas = res[0].node
          // Canvas 画布的实际绘制宽高
          renderWidth = res[0].width
          renderHeight = res[0].height
          // Canvas 绘制上下文
          ctx = canvas.getContext('2d')

          dpr = info.pixelRatio
          const screenWidth = info.screenWidth
          const imgWidth = renderWidth
          dx = screenWidth > imgWidth ? (screenWidth - imgWidth) / 2 : 0
          let rw = renderWidth * dpr
          rw = rw >= 1365 ? 1365 : rw
          canvas.width = rw
          canvas.height = renderHeight * dpr > 4096 ? 4096 : renderHeight * dpr
          ctx?.scale(dpr, dpr)

          await drawBg(defaultBg.value)
        })
      // renderWidth = 750
      // renderHeight = 1624
      // dpr = info.pixelRatio
      // const screenWidth = info.screenWidth
      // const imgWidth = renderWidth / 2
      // dx = screenWidth > imgWidth ? (screenWidth - imgWidth) / 2 : 0

      // canvas = wx.createOffscreenCanvas({type: '2d', width: renderWidth * dpr, height: renderHeight * dpr})
      // ctx = canvas.getContext('2d')
      // // ctx?.scale(dpr, dpr)
      // await drawBg('https://image.gxrc.com/gxrcsite/wxMiniApp/2022/<EMAIL>')
    } else {
      saveImg()
    }
    isSubmit.value = false
  }

  const drawBg = async (url: string) => {
    const imgWidth = renderWidth
    const imgHeight = renderHeight

    const image = await createImage(url)
    ctx?.drawImage(image, dx, 0, imgWidth, imgHeight)

    ctx.font = `bold ${width <= 414 ? 16 : 20}px sans-serif`
    ctx.fillStyle = '#333333'
    ctx?.save()

    // ctx?.fillText(
    //   title.value,
    //   52 * ratio,
    //   (ticketsType.value == 0 ? 280 : 320) * heightRatio
    // )
    canvasFormateStrForTickets(
      ctx,
      title.value,
      (width <= 375 ? 260 : 280) * ratio,
      2,
      52 * ratio,
      320 * heightRatio,
      26 * ratio
    )
    ctx.font = '12px sans-serif'
    ctx.fillStyle = '#666666'
    ctx?.save()

    canvasFormateStrForTickets(
      ctx,
      time.value,
      (width <= 375 ? 260 : 280) * ratio,
      2,
      52 * ratio,
      360 * heightRatio,
      16 * ratio
    )
    //ctx?.fillText(time.value, 52 * ratio, 360 * heightRatio)

    const qrcode = await createImage(qrImg.value)
    ctx?.drawImage(qrcode, (width - 140) / 2, 400 * heightRatio, 140, 140)
    ctx?.restore()

    const txtWidth = ctx?.measureText(phone.value).width

    drawRect(txtWidth, (width - txtWidth) / 2)

    ctx.font = '14px sans-serif'
    ctx.fillStyle = '#333333'
    ctx?.save()

    ctx?.fillText(phone.value, (width - txtWidth) / 2, (height <= 667 ? 610 : 570) * heightRatio)

    saveTmpImg()
  }

  const drawRect = (txtWidth: number, tx: number) => {
    // 设置长方形的位置和大小
    // const x = 115 * ratio
    const y = (height <= 667 ? 580 : 550) * heightRatio
    const width = txtWidth + 60
    const rheight = 34

    const x = tx - 30

    // 设置圆角的半径
    const cornerRadius = 8

    // 开始绘制路径
    ctx?.beginPath()

    // 移动到长方形的左上角
    ctx?.moveTo(x + cornerRadius, y)

    // 绘制右上角的圆角
    ctx?.arcTo(x + width, y, x + width, y + cornerRadius, cornerRadius)

    // 绘制右下角的圆角
    ctx?.arcTo(x + width, y + rheight, x + width - cornerRadius, y + rheight, cornerRadius)

    // 绘制左下角的圆角
    ctx?.arcTo(x, y + rheight, x, y + rheight - cornerRadius, cornerRadius)

    ctx?.arcTo(x, y, x + cornerRadius, y, cornerRadius)
    // 绘制左下角的直线到起始点，闭合路径
    ctx?.lineTo(x + cornerRadius, y)

    // 设置填充颜色并填充路径
    ctx.fillStyle = '#ffffff' 
    ctx?.fill()
    ctx.strokeStyle = '#EBEBEA' 
    ctx?.stroke()
  }
  const createImage = (url: string): Promise<CanvasImageSource> => {
    return new Promise((req, rej) => {
      const image = canvas.createImage()
      image.src = url
      image.onload = () => {
        req(image)
      }
      image.onerror = () => {
        image.src =''
        rej(image)
      }
    })
  }
  const saveTmpImg = () => {
    // let width = info.screenWidth
    // let height = info.screenHeight
    wx.canvasToTempFilePath({
      canvas: canvas,
      x: dx,
      y: 0,
      width: width,
      height: height,
      destWidth: width * dpr,
      destHeight: height * dpr,
      fileType: 'png',
      quality: 1,
      success(res) {
        imgUrl.value = res.tempFilePath
        setTimeout(() => {
          uni.hideLoading()
          saveImg()
        }, 10)
      },
      fail(err) {
        console.log(err)
      }
    })
  }

  const saveImg = () => {
    wx.saveImageToPhotosAlbum({
      filePath: imgUrl.value,
      success: function () {
        uni.showToast({
          title: '图片保存成功'
        })
      },
      fail: (err) => {
        if (
          err.errMsg === 'saveImageToPhotosAlbum:fail:auth denied' ||
          err.errMsg === 'saveImageToPhotosAlbum:fail auth deny' ||
          err.errMsg === 'saveImageToPhotosAlbum:fail authorize no response'
        ) {
          wx.showModal({
            title: '提示',
            content: '需要您授权保存相册',
            showCancel: false,
            success: (modalSuccess) => {
              wx.openSetting({
                success(settingdata) {
                  console.log('settingdata', settingdata)
                  if (settingdata.authSetting['scope.writePhotosAlbum']) {
                    uni.showModal({
                      title: '提示',
                      content: '获取权限成功,再次点击保存图片即可保存',
                      showCancel: false
                    })
                  } else {
                    uni.showModal({
                      title: '提示',
                      content: '获取权限失败，将无法保存到相册哦~',
                      showCancel: false
                    })
                  }
                },
                fail(failData) {
                  console.log('failData', failData)
                },
                complete(finishData) {
                  console.log('finishData', finishData)
                }
              })
            }
          })
        }
      }
    })
  }

  const back = () => {
    uni.navigateBack({
      delta: 1,
      fail(res) {
        uni.switchTab({
          url: '/pages/index/index'
        })
      }
    })
  }

  const gonext = () => {
    if (isReg.value) {
      uni.switchTab({ url: '/pages/index/index' })
    } else {
      uni.navigateTo({ url: '/mypages/registerBlue/baseInfo' })
    }
  }
</script>

<template>
  <!-- 防止被注入登录弹窗 -->
  <scroll-view>
    <view
      class="tickets"
      :style="`background: url(${defaultBg}) no-repeat center center;background-size: 100% auto;`"
    >
      <uni-nav-bar
        title=""
        left-icon="left"
        :fixed="true"
        :border="false"
        :status-bar="true"
        backgroundColor="transparent"
        @clickLeft="back"
      ></uni-nav-bar>
      <view class="content">
        <view class="header">
          <view class="title">{{ title }}</view>
          <view class="sub">{{ time }}</view>
        </view>
        <view class="imgContent" :class="{ flter: !isTakeTicket }">
          <image class="img" :src="qrImg" mode="widthFix" />
        </view>
        <view v-if="!isLogin" style="margin-top: 40rpx">
          <gxrc-login-button class="btn" :class-name="'activity-button'" @success="success"
            >立即登录</gxrc-login-button
          >
          <view class="tips">登录后显示{{ ticketsType == 0 ? '入场码': '礼品码' }}</view>
        </view>
        <view class="questions" v-else>
          <view v-if="isTakeTicket" class="list">
            <view class="rect">{{ phone }}</view>
            <view class="btn liqun"  @tap="startDraw">
              <uni-icons type="download" size="24" color="#fff" style="margin-top: 5rpx;"></uni-icons>
              <text style="padding-left: 10rpx;">保存到手机</text>
            </view>
            <!-- <button class="btn" @tap="startDraw"><uni-icons type="download" size="22" color="#fff"></uni-icons>保存到手机</button> -->
          </view>
          <view v-else style="margin-top: 40rpx">
            <button class="btn" @tap="collection">立即领取</button>
          </view>
        </view>
      </view>
      <image :src="bottomBg" class="bottomimg" mode="widthFix" v-if="isLogin && isTakeTicket" @tap="gonext" />
    </view>
    <choosePopup ref="chooseRef" />
    <canvas id="canvasId" class="canvas" type="2d" style="position: fixed; top: -9999rpx" />
  </scroll-view>
</template>

<style lang="scss" scoped>
  .tickets {
    position: relative;
    height: 100vh;
    width: 100vw;
    .content {
      position: absolute;
      top: v-bind(refTop);
      left: 50%;
      transform: translateX(-50%);
      width: 80vw;
      .header {
        margin: 0 0 30rpx 20rpx;
        .title {
          font-size: 40rpx;
          font-weight: bolder;
          margin-bottom: 16rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
        .sub {
          font-size: 28rpx;
          color: #666666;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
      }
      .imgContent {
        text-align: center;
        margin: 60rpx 0 0 0;

        .img {
          width: 280rpx;
          height: 280rpx;
        }
      }
      .imgContent.flter {
        background: rgba(255, 251, 245, 0.39);
        filter: blur(6px);
      }
      .tips {
        font-size: 26rpx;
        color: #666666;
        margin-top: 24rpx;
        text-align: center;
        // margin-left: 20rpx;
      }
      .btn {
        border-radius: 44rpx;
        width: 400rpx;
        color: #fff;
        background: v-bind(buttonBgColor);
        font-weight: bold;
        position: relative;
        font-size: 36rpx;
        // left: 10rpx;
        ::v-deep button::after {
          border: none;
        }
      }
      :deep(button) {
        background: v-bind(buttonBgColor);
      }
      button::after {
        border: none;
      }
    }
    .liqun{
      height: 88rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .questions {
      text-align: center;
      .list {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .rect {
        width: 300rpx;
        height: 68rpx;
        background: #fff;
        border: 2px solid #ebebea;
        border-radius: 8rpx;
        // margin-left: 110rpx;
        margin-bottom: 20rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28rpx;
      }
    }
    .bottomimg {
      position: absolute;
      bottom: v-bind(safeBottom);
      left: 50%;
      transform: translateX(-50%);
    }
  }
  .canvas {
    width: v-bind(canvasWidth);
    height: v-bind(canvasHeight);
  }
</style>
