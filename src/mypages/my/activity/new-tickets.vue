<script setup lang="ts">
  import { computed, nextTick, ref, watch } from 'vue'
  import { useAppStore } from '@/store/modules/app'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import { MeetingMy } from '@/services/my/Meetingmy'
  import { ApiError } from '@/utils/ApiError'
  import choosePopup from './component/choosePopup.vue'
  import LPainter from '../components/lime-painter/components/l-painter/l-painter.vue'

  const isLogin = ref(false)

  const isTakeTicket = ref(false)
  const chooseRef = ref<any>(null)
  //生成的临时图片
  const imgUrl = ref('')
  const phone = ref('')
  const qrImg = ref('https://image.gxrc.com/gxrcsite/global/app_code.png')
  const defaultBg = ref('https://image.gxrc.com/gxrcsite/wxMiniApp/2025/tickets/<EMAIL>')
  const bottomBg = ref('https://image.gxrc.com/gxrcsite/wxMiniApp/2022/<EMAIL>')
  const isAutonomousRegion = ref(0)
  const isReg = ref(false)
  let canvas: Record<string, any> = {}
  let ctx = $ref<CanvasRenderingContext2D>()
  let renderWidth = $ref<number>()
  let renderHeight = $ref<number>()
  let dx = $ref<number>()
  let dpr = $ref<number>()
  const info = uni.getWindowInfo()

  const height = info.screenHeight
  const width = info.screenWidth
  const canvasHeight = `${height}px`
  const canvasWidth = `${width}px`
  const refTop = ref('80rpx')
  refTop.value = height > 736 ? '80rpx' : '0'
  //canvas x坐标的比例
  const ratio = width > 375 ? width / 375 : width <= 320 ? 0.73 : 1
  const heightRatio = height / 812

  const painter = ref<any>(null)
  onLoad(async (options) => {
    let token = options?.token || ''
    await getTicket(token)
  })
  const poster = computed(() => {
    return {
      css: {
        width: '750rpx',
        height: '1624rpx',
        backgroundImage: `url(${defaultBg.value})`
      },
      views: [
        {
          type: 'view',
          css: {
            paddingTop: '325rpx',
            display: 'flex',
            justifyContent: 'center'
          },
          views: [
            {
              type: 'text',
              css: {
                textAlign: 'center',
                fontSize: '44rpx',
                fontWeight: 'bold',
                color: '#333333'
              },
              text: '2025春季全区人才交流大会'
            }
          ]
        },
        {
          type: 'view',
          css: {
            display: 'flex',
            justifyContent: 'center',
            marginTop: '40rpx'
          },
          views: [
            {
              type: 'text',
              css: {
                textAlign: 'center',
                fontSize: '34rpx',
                color: '#5E6F7F'
              },
              text: '2025年2月22日   9:00—16:00'
            }
          ]
        },{
          type: 'view',
          css: {
            display: 'flex',
            justifyContent: 'center',
            marginTop: '10rpx'
          },
          views: [
            {
              type: 'text',
              css: {
                textAlign: 'center',
                fontSize: '34rpx',
                color: '#5E6F7F'
              },
              text: '南宁国际会展中心D区'
            }
          ]
        },{
          type: 'view',
          css:{
            margin: '40rpx auto 0 auto',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            border: '8px solid #4DDFFD',
            borderRadius: '25rpx',
            width: '408rpx',
            height: '416rpx'
          },
          views:[
            {
              type: 'image',
              src: qrImg.value,
              css: {
                width: '364rpx',
                height: '364rpx'
              }
            }
          ]
        },,{
          type: 'view',
          css: {
            display: 'flex',
            justifyContent: 'center',
            marginTop: '40rpx'
          },
          views: [
            {
              type: 'text',
              css: {
                textAlign: 'center',
                fontSize: '32rpx',
                color: '#222222'
              },
              text: phone.value
            }
          ]
        }
      ]
    }
  })
  // watch(isReg, (newval) => {
  //   bottomBg.value = newval
  //     ? 'https://image.gxrc.com/gxrcsite/wxMiniApp/2022/<EMAIL>'
  //     : bottomBg.value
  // })
  const getTicket = async (token: string) => {
    uni.showLoading({
      title: '加载中...'
    })
    const { code, data } = await MeetingMy.getTicket({ token }).catch((err) => err as ApiError)

    if (code == 1) {
      isTakeTicket.value = data.isTakeTicket
      isLogin.value = true
      if (isTakeTicket.value) {
        qrImg.value = data.qrCode
        phone.value = data.phone
        isAutonomousRegion.value = data.isAutonomousRegion
        isReg.value = data.step != 'noresume'
        // if (data.isAutonomousRegion == 1) {
        //   defaultBg.value = 'https://image.gxrc.com/gxrcsite/wxMiniApp/2022/<EMAIL>'
        // }
        nextTick(() => {})
      }
    }
    uni.hideLoading()
  }

  const success = async () => {
    uni.reLaunch({
      url: '/mypages/my/activity/new-tickets'
    })
  }

  const savePhoto = () => {
    uni.showLoading({ title: '正在生成' })
    painter.value?.render(poster.value)
    painter.value?.canvasToTempFilePathSync({
      fileType: 'png',
      pathType: 'url',
      quality: 1,
      success: (res: any) => {
        imgUrl.value = res.tempFilePath
        uni.hideLoading()
        saveImg()
      },
      fail: (fail: any) => {
        console.log(fail)
        uni.hideLoading()
      }
    })
  }
  
  const createImage = (url: string): Promise<CanvasImageSource> => {
    return new Promise((req, rej) => {
      const image = canvas.createImage()
      image.src = url
      image.onload = () => {
        req(image)
      }
    })
  }
  

  const saveImg = () => {
    wx.saveImageToPhotosAlbum({
      filePath: imgUrl.value,
      success: function () {
        uni.showToast({
          title: '图片保存成功'
        })
      },
      fail: (err) => {
        if (
          err.errMsg === 'saveImageToPhotosAlbum:fail:auth denied' ||
          err.errMsg === 'saveImageToPhotosAlbum:fail auth deny' ||
          err.errMsg === 'saveImageToPhotosAlbum:fail authorize no response'
        ) {
          wx.showModal({
            title: '提示',
            content: '需要您授权保存相册',
            showCancel: false,
            success: (modalSuccess) => {
              wx.openSetting({
                success(settingdata) {
                  console.log('settingdata', settingdata)
                  if (settingdata.authSetting['scope.writePhotosAlbum']) {
                    uni.showModal({
                      title: '提示',
                      content: '获取权限成功,再次点击保存图片即可保存',
                      showCancel: false
                    })
                  } else {
                    uni.showModal({
                      title: '提示',
                      content: '获取权限失败，将无法保存到相册哦~',
                      showCancel: false
                    })
                  }
                },
                fail(failData) {
                  console.log('failData', failData)
                },
                complete(finishData) {
                  console.log('finishData', finishData)
                }
              })
            }
          })
        }
      }
    })
  }

  const back = () => {
    uni.switchTab({
      url: '/pages/index/index'
    })
  }

  const gonext = () => {
    if (isReg.value) {
      uni.switchTab({ url: '/pages/index/index' })
    } else {
      uni.navigateTo({ url: '/mypages/registerBlue/baseInfo' })
    }
  }
</script>

<template>
  <!-- 防止被注入登录弹窗 -->
  <scroll-view>
    <view
      class="tickets"
      :style="`background: url(${defaultBg}) no-repeat center center;background-size: 100% auto;`"
    >
      <uni-nav-bar
        title=""
        left-icon="left"
        :fixed="true"
        :border="false"
        :status-bar="true"
        backgroundColor="transparent"
        @clickLeft="back"
      ></uni-nav-bar>
      <view class="content">
        <view class="header">
          <view class="title">2025春季全区人才交流大会</view>

          <view>
            <view class="sub">2025年2月22日 9:00—16:00</view>
            <view class="sub">南宁国际会展中心D区</view>
          </view>
        </view>
        <view class="imgContent" >
          <view class="qr-box" :class="{ flter: !isTakeTicket }">
            <image class="img" :src="qrImg"  />
          </view>
        </view>
        <view v-if="!isLogin" style="margin-top: 40rpx">
          <gxrc-login-button class="btn" :class-name="'activity-button'" @success="success"
            >立即登录</gxrc-login-button
          >
          <view class="tips">登录后显示入场码</view>
        </view>
        <view class="questions" v-else>
          <view v-if="isTakeTicket" class="list">
            <view class="rect">{{ phone }}</view>
            <button class="btn" @tap="savePhoto">保存到手机</button>
          </view>
        </view>
      </view>
      <!-- <image :src="bottomBg" class="bottomimg" mode="widthFix" v-if="isLogin" @tap="gonext" /> -->
    </view>
    <!-- <choosePopup ref="chooseRef" /> -->
    <!-- <canvas id="canvasId" class="canvas" type="2d" style="position: fixed; top: -9999rpx" /> -->
    <l-painter hidden ref="painter" v-if="isTakeTicket" />
  </scroll-view>
</template>

<style lang="scss" scoped>
  .tickets {
    position: relative;
    height: 100vh;
    width: 100vw;
    .content {
      // position: absolute;
      // top: v-bind(refTop);
      // left: 50%;
      // transform: translateX(-50%);
      padding-top: v-bind(refTop);
      .header {
        margin: 60rpx 88rpx 0 88rpx;
        //white-space: nowrap;
        .title {
          font-size: 44rpx;
          font-weight: bolder;
          text-align: center;
          margin-bottom: 20rpx;
        }
        .sub {
          font-size: 34rpx;
          color: #5e6f7f;
          text-align: center;
        }
      }
      .imgContent {
        text-align: center;
        width: 408rpx;
        height: 416rpx;
        margin: 30rpx auto 20rpx auto;
        background: #ffffff;
        border-radius: 25rpx;
        border: 8px solid;
        border-image: linear-gradient(0deg, #b1b0f0, #4ddffd) 10 10;
        -webkit-mask-image: linear-gradient(0deg, #b1b0f0, #4ddffd);
        mask-image: linear-gradient(0deg, #b1b0f0, #4ddffd);
        overflow: hidden;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
        .qr-box {
         
        }
        .img {
          width: 364rpx;
          height: 364rpx;
        }
      }
      .qr-box.flter {
        background: rgba(255, 251, 245, 0.39);
        filter: blur(6px);
      }
      .tips {
        font-size: 26rpx;
        color: #666666;
        margin-top: 24rpx;
        text-align: center;
        // margin-left: 20rpx;
      }
      .btn {
        border-radius: 44rpx;
        width: 400rpx;
        color: #fff;
        background: linear-gradient(0deg, #1138df 0%, #4ddffd 100%);
        font-weight: bold;
        position: relative;
        // left: 10rpx;
        ::v-deep button::after {
          border: none;
        }
      }
      :deep(button) {
        background: linear-gradient(0deg, #1138df 0%, #4ddffd 100%);
      }
      button::after {
        border: none;
      }
    }
    .questions {
      text-align: center;
      .list {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .rect {
        // width: 300rpx;
        // height: 68rpx;
        // background: #fff;
        // border: 2px solid #ebebea;
        // border-radius: 8rpx;
        // margin-left: 110rpx;
        margin-bottom: 40rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 32rpx;
        color: #222222;
      }
    }
    .bottomimg {
      position: absolute;
      bottom: 40rpx;
      left: 50%;
      transform: translateX(-50%);
    }
  }
  .canvas {
    width: v-bind(canvasWidth);
    height: v-bind(canvasHeight);
  }
</style>
