<script setup lang="ts">
  import { ref } from 'vue'
  import { MeetingMy } from '@/services/my/Meetingmy'
  import { ApiError } from '@/utils/ApiError'

  const chooseRef = ref<any>(null)
  const isWorkOut = ref<number | undefined>()
  const showChoose = () => {
    chooseRef.value.open()
  }

  const submit = async (workout:number) => {
    isWorkOut.value = workout
    uni.showLoading()
    const { code, data, message } = await MeetingMy.ticketcollection({
      isAutonomousRegion: isWorkOut.value,
      meetingType: 0
    }).catch((err) => err as ApiError)
    uni.hideLoading()
    if (code == 1) {
      uni.showToast({
        title: '提交成功',
        icon: 'none'
      })
      uni.reLaunch({
        url: '/mypages/my/activity/tickets'
      })
      return
    } else {
      uni.showToast({
        title: message || '提交失败',
        icon: 'none'
      })
      return
    }
  }
  defineExpose({ showChoose })
</script>

<template>
  <uni-popup ref="chooseRef" :mask-click="false">
    <view class="chooseView">
      <view class="title">您是否为返乡就业人才？</view>
      <view class="sub">（外地读书、外地就业回乡人才）</view>
      <view class="btns">
        <button class="btn" @tap="submit(1)">是</button>
        <button class="btn mt" @tap="submit(0)">否</button>
      </view>
    </view>
  </uni-popup>
</template>

<style lang="scss" scoped>
  .chooseView {
    width: 560rpx;
    // height: 524rpx;
    background: url('https://image.gxrc.com/gxrcsite/wxMiniApp/2022/<EMAIL>') no-repeat center
      center;
    background-size: 100% 100%;
    padding: 96rpx 60rpx 64rpx 60rpx;
    .title {
      font-size: 40rpx;
      font-weight: bolder;
      text-align: center;
    }
    .sub {
      font-size: 28rpx;
      color: #666666;
      text-align: center;
    }
    .btns {
      margin-top: 88rpx;
      .btn {
        border: 2px solid #fda21d;
        color: #e78705;
        border-radius: 42rpx;
        background: #fff;
      }
      .mt {
        margin-top: 24rpx;
      }
      button::after {
        border: none;
      }
    }
  }
</style>
