<template>
  <view>
    <!--  #ifdef  MP-WEIXIN || MP-TOUTIAO -->
    <gxrc-inputonly :value="textValue" :placeholder="placeholder" @tap="show = true" />
    <!--  #endif -->

    <!--  #ifdef MP-ALIPAY -->
    <view @tap="show = true">
      <gxrc-inputonly :value="textValue" :placeholder="placeholder" />
    </view>
    <!--  #endif -->

    <!--  #ifdef  MP-WEIXIN -->
    <page-container v-if="show" :show="containerShow" position="right" @enter="" @afterleave="back">
      <certificate-select
        :value="modelValue"
        :active-index="activeIndex"
        @change="change"
        @back="back"
      />
    </page-container>
    <!--  #endif -->

    <!--  #ifdef MP-TOUTIAO || MP-ALIPAY -->
    <uni-popup ref="popup" background-color="#fff" @change="change1">
      <certificate-select
        :value="modelValue"
        :active-index="activeIndex"
        @change="change"
        @back="back"
      ></certificate-select>
    </uni-popup>
    <!--  #endif -->
  </view>
</template>

<script setup lang="ts">
  import { watch } from 'vue'
  import certificateSelect from './certificate-select.vue'

  interface props {
    text: string | undefined | null

    modelValue?: number | null

    placeholder?: string
  }

  const { text, modelValue, placeholder } = defineProps<props>()

  const emit = defineEmits<{
    (e: 'update:modelValue', value: number): void
    (e: 'change', value: boolean): void
  }>()

  let show = $ref(false)
  const popup = $ref(null)
  let containerShow = $ref(false)

  let textValue = $ref(text)

  watch(
    () => text,
    (newval) => {
      textValue = newval as string
    }
  )

  watch(
    () => show,
    (val) => {
      if (val) {
        // #ifdef MP-WEIXIN
        containerShow = true
        // #endif

        // #ifdef MP-TOUTIAO || MP-ALIPAY

        popup.open('top')
        // #endif
      }
    }
  )

  let activeIndex = $ref(1)

  const change = (item: any, index: number) => {
    back()
    textValue = item.title
    activeIndex = index
    emit('update:modelValue', item.id)
    emit('change', item.hasLevel)
  }

  const change1 = (e) => {
    if (!e.show) {
      show = false
    }
  }

  const back = () => {
    // #ifdef MP-WEIXIN

    containerShow = false
    // #endif

    // #ifdef MP-TOUTIAO|| MP-ALIPAY
    popup.close()
    // #endif
    setTimeout(function () {
      show = false
    }, 300)
  }
</script>

<style scoped></style>
