<template>
  <view class="certificate">
    <uni-nav-bar
      title="证书类别"
      left-icon="left"
      :fixed="true"
      :border="false"
      :statusBar="true"
      @clickLeft="back"
    />
    <view class="container">
      <view class="left flex">
        <template v-for="item in list" :key="item.id">
          <view
            class="item"
            :class="{ active: activeId === item.id }"
            @tap="activeId = item.id ?? 0"
            >{{ item.title }}</view
          >
        </template>
      </view>
      <scroll-view scroll-y class="scroll">
        <view class="right flex">
          <template v-for="item in list" :key="item.id">
            <template v-for="it in item.children" :key="it.id">
              <template v-if="activeId === it.pid">
                <view class="item" @tap="change(it)">
                  <text class="title">{{ it.title }}</text>
                  <uni-icons
                    v-if="selectId === it.id"
                    type="checkmarkempty"
                    color="#457CCF"
                    size="14"
                  />
                </view>
              </template>
            </template>
          </template>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
  import { DicNodeDto, DicDto } from '@/services/my/data-contracts'
  import { computed, onMounted } from 'vue'

  import { useOptionsStore } from '@/store/modules/options'

  interface Props {
    value?: number | null

    activeIndex?: number
  }

  interface certDic extends DicDto {
    children: DicDto[]
  }

  const { value, activeIndex } = defineProps<Props>()

  const emit = defineEmits<{
    (e: 'change', item: DicDto, index: number): void
    (e: 'back'): void
  }>()

  let certificateList = $ref<DicNodeDto[]>([])

  let parentList = $ref<DicDto[]>([])

  let list = $ref<certDic[]>()

  let activeId = $ref(activeIndex)

  let selectId = $ref(value)

  const store = useOptionsStore()

  const { windowHeight } = uni.getSystemInfoSync()
  const scrollHeight = computed(() => windowHeight + 'px')

  onMounted(async () => {
    await store.getCertificatelist()

    certificateList = store.certificate

    parentList = certificateList[0].nodeList ?? Array<DicDto>()

    list = parentList.map((item) => {
      const child = certificateList.find((it) => it.pid === item.id)
      return {
        ...item,
        children: child?.nodeList ?? Array<DicDto>()
      }
    })
  })

  const change = (item: DicDto) => {
    selectId = item.id as number
    emit('change', item, activeId)
  }

  const back = () => {
    emit('back')
  }
</script>

<style lang="scss" scoped>
  .certificate {
    .container {
      display: flex;
      flex-direction: row;
      .flex {
        display: flex;
        flex-direction: column;
      }
      .left {
        background: #f5f7fa;
        width: 300px;

        /*  #ifdef  MP-ALIPAY */
        max-width: 350rpx;
        min-width: 350rpx;
        width: 350rpx;
        /*  #endif  */
      }
      .right {
        padding: 0 0 40px 24px;
      }
      .item {
        padding: 20px;
        .title {
          padding-right: 25px;
        }
      }
      .active {
        background: #ffffff;
        border-left: 3px solid #457ccf;
      }
      .scroll {
        height: v-bind(scrollHeight);
      }
    }
  }
</style>
