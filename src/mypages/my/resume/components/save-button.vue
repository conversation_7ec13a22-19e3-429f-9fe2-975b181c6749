<template>
  <view class="form-footer">
    <button v-if="leftButton" class="add" @tap="tapLeft">{{ leftText }}</button>
    <button class="button" @tap="tapRight">{{ rightText }}</button>
  </view>
</template>
<script lang="ts" setup>
  interface Props {
    leftButton?: boolean

    leftText?: string

    rightText?: string
  }

  const { leftButton = false, leftText, rightText = '保存' } = defineProps<Props>()
  const emit = defineEmits<{
    (e: 'leftClick'): void
    (e: 'rightClick'): void
  }>()

  const tapLeft = () => emit('leftClick')
  const tapRight = () => emit('rightClick')
</script>

<style lang="scss" scoped>
  .form-footer {
    position: fixed;
    width: 100%;
    bottom: 0;
    background: $uni-bg-color;
    border: solid 1px $uni-bg-color-grey;
    z-index: 9;
    opacity: 1;
    /*  #ifdef MP-TOUTIAO */
    display: flex;
    /*  #endif  */
    .button {
      margin: 20px 20px;
      background: #608bf8;
      color: $uni-bg-color;
      padding-top: 2px;
      font-size: 16px;

      /*  #ifdef MP-TOUTIAO */
      flex: 1;
      /*  #endif  */
    }
    .add {
      float: left;
      font-size: 16px;
      margin: 20px 20px;
      border: 1px solid #608bf8;
      // border-radius: 4px;
      background: #f2f7ff;
      color: #608bf8;
      padding-left: 30px;
      padding-right: 30px;
    }
  }
</style>
