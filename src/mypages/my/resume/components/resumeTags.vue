<template>
  <view class="tags">
    <view class="head-title" @tap="navigateTo('/mypages/my/resume/tags?resumeId=' + resumeId)">
      <view class="aitem">
        <text class="itemtitle">优势标签({{ leng }}/10)</text>
      </view>
      <view class="edit aitem right">
        <i class="iconfont icon-bianji pd"></i>
        <text>编辑</text>
      </view>
    </view>
    <view class="tag_list">
      <uni-tag
        v-for="item in tagsInfo"
        :key="item"
        :text="item"
        custom-style="background-color: #F5F7FA; border: none; color: #515459;"
        style="margin: 5px 5px 0px 0px"
      ></uni-tag>
    </view>
  </view>
</template>

<script lang="ts" setup>
  import { computed, inject, onMounted, onUnmounted } from 'vue'
  import { useNavigate } from '@/hooks/page/useNavigate'
  interface tagsInfoType {
    tagsInfo?: Array<[]>
    resumeId?: number
  }
  const { tagsInfo, resumeId } = defineProps<tagsInfoType>()

  // #ifdef MP-WEIXIN || MP-ALIPAY
  const { navigateTo } = inject<any>('function')
  // #endif
  // #ifdef MP-TOUTIAO
  const { navigateTo } = useNavigate()
  // #endif
  const leng = computed(() => {
    return tagsInfo?.length
  })
  onMounted(() => {})

  onUnmounted(() => {
    uni.$off('uploadAvatar')
  })
</script>
<style lang="scss" scoped>
  .tags {
    padding-top: 36px;
    .photo {
      width: 64px;
      height: 64px;
      border-radius: 50%;
    }
    .title {
      justify-content: space-between;
    }
    &-row {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #333333;
      .name {
        font-size: 20px;
        font-weight: 600;
        width: 70%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .tag_list {
      padding: 10px 0;
      display: flex;
      flex-wrap: wrap;

      /*  #ifdef MP-ALIPAY */
      ::v-deep text {
        margin-right: 20rpx;
      }
      /*  #endif  */
    }
  }
</style>
