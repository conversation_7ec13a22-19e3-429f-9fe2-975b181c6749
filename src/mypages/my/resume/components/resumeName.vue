<template>
  <view class="resume-name">
    <uni-nav-bar
      title="修改名称"
      left-icon="left"
      :fixed="true"
      :border="false"
      :statusBar="true"
      @clickLeft="back"
    />
    <view style="padding: 0 15px">
      <!--  #ifdef  MP-WEIXIN  -->
      <uni-easyinput
        v-model="name"
        type="text"
        placeholder="请输入简历名称"
        :inputBorder="false"
        :focus="true"
        :trim="true"
        :maxlength="20"
        confirmType="完成"
      />
      <!--  #endif -->

      <!--  #ifdef MP-TOUTIAO || MP-ALIPAY -->
      <input
        v-model="name"
        :maxlength="20"
        :enableNative="false"
        type="text"
        placeholder="请输入简历名称"
      />
      <!--  #endif -->
      <view class="line" />
      <view class="count">{{ count }}/20</view>
    </view>
    <save-button @right-click="confirm" />
  </view>
</template>

<script setup lang="ts">
  import { Resume } from '@/services/my/Resume'
  import saveButton from './save-button.vue'

  interface Props {
    resumeId: number
    resumeName: string
  }

  const { resumeId, resumeName } = defineProps<Props>()
  console.log(resumeId)

  const emit = defineEmits<{
    (e: 'update', value: string): void
    (e: 'back'): void
  }>()

  const name = $ref(resumeName)

  const count = $computed(() => name.length ?? 0)

  const confirm = async () => {
    if (!name) {
      uni.showToast({
        title: '请输入简历名称',
        icon: 'none'
      })
      return
    }
    if (!resumeId) {
      uni.showToast({
        title: '请输入正确的简历',
        icon: 'none'
      })
      return
    }

    uni.showLoading({
      mask: true
    })

    const result = await Resume.resumeResumerename({ resumeId, name }).catch((err) => err)
    uni.hideLoading()
    const { code, message } = result

    if (code === 1) {
      emit('update', name)
    }
    uni.showToast({
      title: message,
      icon: 'none'
    })
    back()
  }

  const back = () => {
    // uni.navigateBack({
    //   delta: 1,
    //   fail() {
    //     uni.reLaunch({ url: '/mypages/my/resume/index?resumeId='+ resumeId })
    //   }
    // })
    emit('back')
  }
</script>

<style lang="scss" scoped>
  .resume-name {
    // padding: 0 15px;
    width: 100%;
    height: 100%;
    /*  #ifdef MP-TOUTIAO */

    min-height: 400rpx;
    /*  #endif  */
    /*  #ifdef  MP-ALIPAY */

    min-height: 550rpx;
    /*  #endif  */
    .count {
      float: right;
      font-size: 14px;
      color: #bbbbbb;
      padding-top: 10px;
    }
  }
  .line {
    border-top: 1px #f2f2f2 solid;
    margin-bottom: 0;
  }
</style>
