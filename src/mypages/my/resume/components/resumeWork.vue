<template>
  <view class="work mt32">
    <view class="head-title" @tap="navigateTo(`/mypages/my/resume/work?resumeId=${resumeId}`)">
      <view class="aitem">
        <text class="itemtitle">工作经历</text>
        <text v-if="!isFinish" v-show="talent == 2" class="tip red">待完善</text>
        <text v-show="talent == 2" class="tip">必填</text>
        <image src="https://image.gxrc.com/gxrcsite/wxMiniApp/2025/ai/<EMAIL>"  class="work-icon"></image>
      </view>
      <view class="edit aitem right">
        <i class="iconfont icon-tianjia pd"></i>
        <text>添加</text>
      </view>
    </view>
    <template v-if="work && work?.length > 0">
      <view
        v-for="item in work"
        :key="item.workId"
        class="work-item vertical-line"
        @tap="navigateTo(`/mypages/my/resume/work?resumeId=${resumeId}&workId=${item.workId}`)"
      >
        <view class="font12 aitem gray timeline">
          <i class="circle"></i>
          <text>{{ item.timeRang }}</text>
          <text
            v-if="item.longestTime && item.timeRang != '未填在职时间'"
            class="tip font12"
            style="font-size: 11px"
            >最长</text
          >
          <text v-if="item.jobSeekerAbroadExperience" class="tip higher">海外经验</text>
          <i class="iconfont icon-arrowRight14 arrowright"></i>
        </view>
        <view class="subtitle mt12">
          {{ item.entName?item.entName:'未填写公司名称' }}
        </view>
        <view
          v-if="
            item.enterprisePropertyName ||
            item.enterpriseEmployeeNumberName ||
            item.workIndustryName ||
            item.workPlace
          "
          class="aitem mt8 font12 gray"
        >
          <text v-if="item.enterprisePropertyName">{{ item.enterprisePropertyName }}</text>
          <view v-if="item.enterpriseEmployeeNumberName" class="split"></view>
          <text v-if="item.enterpriseEmployeeNumberName">{{
            item.enterpriseEmployeeNumberName
          }}</text>
          <view v-if="item.workIndustryName" class="split"></view>
          <text v-if="item.workIndustryName">{{ item.workIndustryName }}</text>
          <view v-if="item.workPlace" class="split"></view>
          <text v-if="item.workPlace">{{ item.workPlace }}</text>
        </view>
        <view class="mt24">
          <text class="subtitle">{{ item.positionName }}</text>
          <text v-if="item.hasHigher" class="tip higher">高级人才</text>
        </view>
        <view>
          <text v-if="item.department" class="tip tag">{{ item.department }}</text>
          <text v-if="item.workPropertyName" class="tip tag">{{ item.workPropertyName }}</text>
          <text v-if="item.positionLevelName" class="tip tag">{{ item.positionLevelName }}</text>
          <text v-for="i in item.keywordIds" class="tip tag" :key="i.keywordID">{{ i.keywordName }}</text>
        </view>
        <view class="mt12 description">
          <text>{{ item.positionDescription }}</text>
        </view>
        <view
          v-if="item.higherUp || item.underlingNum || item.leavingReason || item.jobPerformance"
          class="detail mt12"
        >
          <view v-if="item.higherUp" class="detail-line">
            <text class="gray pd">汇报对象</text>
            <text>{{ item.higherUp }}</text>
          </view>
          <view v-if="item.underlingNum || 0 > 0" class="mt8">
            <text class="gray pd">下属人数</text>
            <text>{{ item.underlingNum }}</text>
          </view>
          <view v-if="item.leavingReason" class="mt8">
            <text class="gray pd">离职原因</text>
            <text>{{ item.leavingReason }}</text>
          </view>
          <view v-if="item.jobPerformance" class="mt12 description">
            <text>{{ item.jobPerformance }}</text>
          </view>
        </view>
      </view>
    </template>
    <template v-else>
      <view class="mt12 gray font14"> 工作经历是简历的关键部分 </view>
    </template>
    <view class="line mt24"></view>
  </view>
</template>
<script lang="ts" setup>
  import { WorkPartDto } from '@/services/my/data-contracts'
  import { inject, toRefs, computed } from 'vue'
  import { useNavigate } from '@/hooks/page/useNavigate'

  // #ifdef MP-WEIXIN || MP-ALIPAY
  const { navigateTo } = inject<any>('function')
  // #endif
  // #ifdef MP-TOUTIAO
  const { navigateTo } = useNavigate()
  // #endif

  interface workPart {
    work?: WorkPartDto[] | null
    resumeId?: number
    isFinish?: number
    talent?: number
  }
  const { work, resumeId, isFinish, talent } = defineProps<workPart>()
</script>
<style lang="scss" scoped>
  .work-icon {
    width: 266rpx;
    margin-left: 16rpx;
    height: 36rpx;
  }
</style>
