<template>
  <view class="train mt32">
    <view class="head-title" @tap="navigateTo(`/mypages/my/resume/train?resumeId=${resumeId}`)">
      <view class="aitem">
        <text class="itemtitle">培训经历</text>
      </view>
      <view class="edit aitem right">
        <i class="iconfont icon-tianjia pd"></i>
        <text>添加</text>
      </view>
    </view>
    <template v-if="train && train?.length > 0">
      <view
        v-for="item in train"
        :key="item.trainId"
        class="vertical-line"
        @tap="navigateTo(`/mypages/my/resume/train?resumeId=${resumeId}&trainId=${item.trainId}`)"
      >
        <view class="font12 aitem gray timeline">
          <i class="circle"></i>
          <text>{{ item.trainBeginTime }}-{{ item.trainEndTime }}</text>
          <i class="iconfont icon-arrowRight14 arrowright"></i>
        </view>
        <view class="subtitle mt12">
          {{ item.trainCourse }}
        </view>
        <view class="aitem mt8 font12 gray">
          <text>{{ item.trainInstitution }}</text>
        </view>
        <view v-if="item.trainingDescription" class="mt12 description">
          <text>{{ item.trainingDescription }}</text>
        </view>
      </view>
    </template>
    <template v-else>
      <view class="mt12 gray font14">
        <text>培训经历将增加您的竞争力</text>
      </view>
    </template>
    <view class="line mt24"></view>
  </view>
</template>
<script lang="ts" setup>
  import { ResumeEditTrainDto } from '@/services/my/data-contracts'
  import { inject } from 'vue'
  import { useNavigate } from '@/hooks/page/useNavigate'

  // #ifdef MP-WEIXIN || MP-ALIPAY
  const { navigateTo } = inject<any>('function')
  // #endif
  // #ifdef MP-TOUTIAO
  const { navigateTo } = useNavigate()
  // #endif
  interface trainDto {
    train?: ResumeEditTrainDto[] | null
    resumeId: number
  }
  const { train, resumeId } = defineProps<trainDto>()
</script>
