<template>
  <view class="tags">
    <view class="head-title">
      <view class="aitem">
        <text class="itemtitle">优势标签</text>
      </view>
    </view>
    <view class="tag_list">
      <uni-tag
        v-for="item in tagsInfo"
        :key="item"
        :text="item"
        custom-style="background-color: #F5F7FA; border: none; color: #515459;"
        style="margin: 5px 5px 0px 0px"
      ></uni-tag>
    </view>
  </view>


</template>

<script lang="ts" setup>
  import { onUnmounted } from 'vue'
  interface tagsInfoType {
    tagsInfo?: tagsInfoType[] | null
    resumeId?: number
  }
  const { tagsInfo, resumeId } = defineProps<tagsInfoType>()

  onUnmounted(() => {
    uni.$off('uploadAvatar')
  })
</script>
<style lang="scss" scoped>
  .tags {
    padding-top: 36px;
    .photo {
      width: 64px;
      height: 64px;
      border-radius: 50%;
    }
    .title {
      justify-content: space-between;
    }
    &-row {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #333333;
      .name {
        font-size: 20px;
        font-weight: 600;
        width: 70%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .tag_list{
      padding: 10px 0;
      display: flex;
    flex-wrap: wrap;
    }
  }
</style>
