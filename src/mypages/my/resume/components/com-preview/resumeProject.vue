<template>
  <view class="project mt32">
    <view class="head-title">
      <view class="aitem">
        <text class="itemtitle">项目经历</text>
      </view>
    </view>
    <template v-if="project && project?.length > 0">
      <view v-for="item in project" :key="item.id" class="mt32 vertical-line">
        <view class="font12 aitem gray timeline">
          <i class="circle"></i>
          <text>{{ item.beginTime }}-{{ item.endTime }}</text>
        </view>
        <view class="subtitle mt12">
          {{ item.projectName }}
        </view>
        <view class="description mt12" v-if="item.projectDescription">
          <text>{{ item.projectDescription }}</text>
        </view>
      </view>
    </template>
    <template v-else>
      <view class="mt12 gray font14">
        <text>出色的项目经验为你在竞争中增加一个重要的砝码</text>
      </view>
    </template>
    <view class="line mt24"></view>
  </view>
</template>
<script lang="ts" setup>
  import { ResumeEditProjectDto } from '@/services/my/data-contracts'
  import { inject } from 'vue'
  interface projectDto {
    project?: ResumeEditProjectDto[] | null
    resumeId?: number
  }
  const { project } = defineProps<projectDto>()
</script>
