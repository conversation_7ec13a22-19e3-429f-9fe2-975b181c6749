<template>
  <view class="baseinfo">
    <view class="head-title">
      <view class="aitem">
        <text class="itemtitle">基本信息</text>
      </view>
    </view>
    <uni-row>
      <uni-col :span="6">
        <image class="photo" :src="baseInfo?.photo" mode="scaleToFill" @error="handlerErrorImg" />
      </uni-col>
      <uni-col :span="18">
        <view class="baseinfo-row title">
          <text class="name">{{ baseInfo?.name }}</text>
        </view>
        <view class="baseinfo-row mt12" style="flex-wrap: wrap">
          <text>{{ baseInfo?.sex ? '女' : '男' }}</text>
          <view class="split"></view>
          <text>{{ baseInfo?.age }}岁</text>
          <view class="split"></view>
          <text>{{ baseInfo?.workYear }}</text>
          <view class="split"></view>
          <text>{{ baseInfo?.residencyStr }}</text>
          <view class="split"></view>
          <text>{{ baseInfo?.talentDegreeName }}</text>
        </view>
        <!-- <view class="baseinfo-row mt12" v-if="baseInfo?.domicileName">
          <view class="split"></view>
          <text>{{ baseInfo?.domicileName }}籍贯</text>
        </view> -->

        <view class="baseinfo-row mt24">
          <i class="iconfont icon-phone9 pd"></i>
          <text>{{ baseInfo?.firstContact }}</text>
        </view>
        <view class="baseinfo-row mt12">
          <i class="iconfont icon-letter10 pd"></i>
          <text>{{ baseInfo?.email }}</text>
        </view>
      </uni-col>
    </uni-row>
  </view>
</template>

<script lang="ts" setup>
  import { ResumeBaseInfoOutPutDto } from '@/services/my/data-contracts'
  import { inject } from 'vue'
  interface baseInfoType {
    baseInfo?: ResumeBaseInfoOutPutDto
    resumeId?: number
  }
  const { baseInfo, resumeId } = defineProps<baseInfoType>()
  const handlerErrorImg = () => {
    if (baseInfo) {
      baseInfo.photo = '//image.gxrc.com/app/person/mrtx/<EMAIL>'
    }
  }
</script>
<style lang="scss" scoped>
  .baseinfo {
    .photo {
      width: 64px;
      height: 64px;
      border-radius: 50%;
    }
    .title {
      justify-content: space-between;
    }
    &-row {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #333333;
      .name {
        font-size: 20px;
        font-weight: 600;
        width: 70%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .head-title {
      padding: 20px 0 20px 0;
    }
    .baseinfo-row-2 {
      width: auto;
      align-items: center;
      font-size: 14px;
      color: #333333;
      .fl {
        float: left;
      }
      .split {
        margin: 10rpx 12rpx 0 12rpx;
      }
      text {
        padding-bottom: 10rpx;
      }
    }
  }
</style>
