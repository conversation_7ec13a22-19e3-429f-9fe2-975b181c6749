<template>
  <view class="career mt32">
    <view class="head-title">
      <view class="aitem">
        <text class="itemtitle">求职意向</text>
      </view>
    </view>
    <view class="font14 mt32">
      <view class="aitem" :class="{ red: !career?.workingState }">
        <i class="iconfont icon-qiuzhizhuangtai pd"></i>
        <text v-if="career?.workingState">{{ career?.workingState }}</text>
        <text v-else>求职状态未填写</text>
      </view>
      <view class="aitem mt24" :class="{ red: !expectWorkPlaceName }">
        <i class="iconfont icon-dizhi pd"></i>
        <text v-if="expectWorkPlaceName">{{ expectWorkPlaceName }}</text>
        <text v-else>期望工作城市未填写</text>
      </view>
      <view class="aitem mt24">
        <i class="iconfont icon-xinchou pd"></i>
        <text>{{ career?.expectSalary }}</text>
      </view>
      <view class="line mt32" v-if="career?.expectCareer1 || 0 > 0"></view>
      <uni-section
        :title="career?.expectCareer1Name"
        :subTitle="expectIndustry1Names"
        v-if="career?.expectCareer1 || 0 > 0"
      >
      </uni-section>
      <uni-section
        :title="career?.expectCareer2Name"
        :subTitle="expectIndustry2Names"
        v-if="career?.expectCareer2 || 0 > 0"
      >
      </uni-section>
      <uni-section
        :title="career?.expectCareer3Name"
        :subTitle="expectIndustry3Names"
        v-if="career?.expectCareer3 || 0 > 0"
      >
      </uni-section>
    </view>
    <view class="line mt24"></view>
  </view>
</template>
<script lang="ts">
  export default {
    options: { styleIsolation: 'shared' }
  }
</script>
<script lang="ts" setup>
  import { CareerPartDto } from '@/services/my/data-contracts'
  import { computed, inject } from 'vue'
  interface careerPart {
    career?: CareerPartDto
  }
  const { career } = defineProps<careerPart>()
  const expectWorkPlaceName = computed(() => {
    const workPlace = career?.expectWorkPlaceName?.filter((item) => item !== '')
    return workPlace?.join(',')
  })
  const expectIndustry1Names = computed(() => {
    const industry = career?.expectIndustry1Names?.filter((item) => item !== '')
    return industry?.join('/')
  })
  const expectIndustry2Names = computed(() => {
    const industry = career?.expectIndustry2Names?.filter((item) => item !== '')
    return industry?.join('/')
  })
  const expectIndustry3Names = computed(() => {
    const industry = career?.expectIndustry3Names?.filter((item) => item !== '')
    return industry?.join('/')
  })
</script>
<style lang="scss" scoped>
  .red {
    color: #fc5c5b;
  }
  .career {
    ::v-deep .uni-section-header {
      padding-left: 0 !important;
    }
    ::v-deep .uni-section {
      background-color: #fff;
      margin-top: 10px;
      position: relative;
    }
    .icon {
      position: absolute;
      right: 0px;
      top: 15px;
      color: #bbbbbb;
      font-size: 12px;
    }
  }
</style>
