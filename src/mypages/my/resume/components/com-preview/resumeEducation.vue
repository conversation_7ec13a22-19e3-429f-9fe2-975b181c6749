<template>
  <view class="education mt32">
    <view class="head-title">
      <view class="aitem">
        <text class="itemtitle">教育经历</text>
      </view>
    </view>
    <template v-if="education && education?.length > 0">
      <view v-for="item in education" :key="item.id" class="education-item vertical-line">
        <view class="font12 aitem gray timeline">
          <i class="circle"></i>
          <text>{{ item.timeRang }}</text>
        </view>
        <view class="subtitle mt12">
          {{ item.school }}
        </view>
        <view class="aitem mt8 font12 gray">
          <text v-if="item.specialityName">{{ item.specialityName }}</text>
          <view class="split" v-if="item.specialityName"></view>
          <text>{{ item.specialityInputName }}</text>
          <view class="split" v-if="item.specialityInputName"></view>
          <text>{{ item.education }}</text>
          <view class="split" v-if="item.education"></view>
          <text>{{ item.fullTimeFlag ? '全日制' : '非全日制' }}</text>
        </view>
        <view class="mt12 description" v-if="item.specialityDescription">
          <text class="tt">专业描述：</text>
          <text>{{ item.specialityDescription }}</text>
        </view>
        <view class="detail mt12" v-if="item.practiceList?.length || 0 > 0">
          <template v-for="it in item.practiceList" :key="it.practiceID">
            <view class="subtitle" style="padding-bottom: 4px">
              <text>{{ it.practiceName }}</text>
            </view>
            <view class="font12 gray pdb">
              {{ it.parcticeTimeSpan }}
            </view>
            <view class="description pdb" v-if="it.practiceDescription">
              <text class="font12">{{ it.practiceDescription }}</text>
            </view>
          </template>
        </view>
      </view>
    </template>
    <template v-else>
      <view class="mt12 gray font14">
        <text>教育经历是企业选人的关键</text>
      </view>
    </template>
    <view class="line mt24"></view>
  </view>
</template>
<script lang="ts" setup>
  import { EducationPartDto } from '@/services/my/data-contracts'
  import { inject } from 'vue'
  interface educationPart {
    education?: EducationPartDto[] | null
    resumeId: number
  }
  const { education, resumeId } = defineProps<educationPart>()
</script>
