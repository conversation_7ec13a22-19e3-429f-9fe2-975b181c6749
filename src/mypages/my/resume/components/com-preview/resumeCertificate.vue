<template>
  <view class="certificate mt32">
    <view class="head-title">
      <view class="aitem">
        <text class="itemtitle">证书职称</text>
      </view>
    </view>
    <view class="mt12">
      <template v-if="certificate && certificate?.length > 0">
        <view v-for="item in certificate" :key="item.certId">
          <view class="mt20 font12 gray">
            {{ item.getTime }}
          </view>
          <view class="aitem pdt">
            <text class="subtitle">{{ item.certName }}</text>
          </view>
          <view class="aitem mt8 font12 gray">
            <text v-if="item.certTypeTitle">{{ item.certTypeTitle }}</text>
            <view class="split" v-if="item.certTypeLevelName"></view>
            <text>{{ item.certTypeLevelName }}</text>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="gray font14">
          <text>证书最能证明您的能力</text>
        </view>
      </template>
    </view>
    <view class="line mt24"></view>
  </view>
</template>
<script lang="ts" setup>
  import { CertificateDto } from '@/services/my/data-contracts'
  import { inject } from 'vue'

  interface certificateDto {
    certificate?: CertificateDto[] | null
    resumeId?: number
  }
  const { certificate, resumeId } = defineProps<certificateDto>()
</script>
