<template>
  <view class="header">
    <!--  #ifdef  MP-WEIXIN -->
    <view class="resumetemplate item" @tap="template">
      <i class="iconfont icon-qiuzhizhuangtai icon"></i>
      简历模板
    </view>
    <!--  #endif -->
    <view class="share item" @click="Noshare">
      <i class="iconfont icon-fenxiang"></i>
    </view>
    <button v-if="isExamine" class="btn" size="mini" open-type="share">分享</button>
  </view>
</template>
<script lang="ts" setup>
  import { Resume } from '@/services/my/Resume'
  interface Props {
    resumeId: number
    resumeName?: string | null
    isExamine: boolean
  }
  const { resumeId, resumeName, isExamine } = defineProps<Props>()

  let name = $ref(resumeName)

  const popup = $ref<any>(null)
  const publicpopup = $ref<any>(null)
  const show = $ref(false)

  const statusBarHeight = (uni.getSystemInfoSync().statusBarHeight ?? 25) + 44 + 'px'
  const Noshare = () => {
    uni.showToast({
      title: '您的简历还未审核，无法分享',
      icon: 'none'
    })
  }
  const template = () => {
    const baseurl =
      process.env.NODE_ENV === 'production' ? 'https://mymall.gxrc.com' : 'http://d6.gxrc.com'
    const url = encodeURIComponent(baseurl + '/MyMall/ResumeTemplate/TemplateList')
    uni.navigateTo({
      url: `/pages/web-view/index?src=${url}&login=true`
    })
  }
</script>

<style lang="scss" scoped>
  .header {
    display: flex;
    justify-content: flex-end;
    position: sticky;
    top: v-bind(statusBarHeight);
    z-index: 99;
    background: #ffffff;
    .icon {
      margin-right: 4px;
    }
    .item {
      display: flex;
      align-items: center;
      margin-right: 12px;
      padding: 10px 6px;
      background: rgba(255, 255, 255, 0.39);
      box-shadow: 0px 0px 8px #eff3f8;
      opacity: 1;
      border-radius: 16px;
      font-size: 14px;
      color: #666666;
    }
    .btn {
      border: none;
      position: absolute;
      right: 0;
      opacity: 0;
      top: 5rpx;
    }
    .share,
    .more {
      padding: 8px 12px;
      border-radius: 50%;
    }
  }
</style>
