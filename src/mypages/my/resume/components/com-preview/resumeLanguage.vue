<template>
  <view class="language mt32">
    <view class="head-title">
      <view class="aitem">
        <text class="itemtitle">语言技能</text>
      </view>
    </view>
    <view class="mt8">
      <template v-if="language && language?.length > 0">
        <view v-for="item in language" :key="item.listId">
          <view class="aitem mt24">
            <text class="subtitle">{{ item.langName }}</text>
          </view>
          <view class="aitem mt8 font12 gray">
            <text v-if="item.langLevel">综合能力{{ item.langLevel }}</text>
            <view class="split" v-if="item.lsLevel"></view>
            <text>听说能力{{ item.lsLevel }}</text>
            <view class="split" v-if="item.rwLevel"></view>
            <text>读写能力{{ item.rwLevel }}</text>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="mt4 gray font14">
          <text>出色的外语水平是重要的竞争力</text>
        </view>
      </template>
    </view>
    <view class="line mt24"></view>
  </view>
</template>
<script lang="ts" setup>
  import { LanguageDto } from '@/services/my/data-contracts'
  import { inject } from 'vue'
  interface languageDto {
    language?: LanguageDto[] | null
    resumeId: number
  }
  const { language, resumeId } = defineProps<languageDto>()
</script>
<style lang="scss">
  .mt4 {
    margin-top: 4px;
  }
</style>
