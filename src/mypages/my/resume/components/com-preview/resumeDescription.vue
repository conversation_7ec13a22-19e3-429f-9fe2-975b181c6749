<template>
  <view class="dec mt32">
    <view class="head-title">
      <view class="aitem">
        <text class="itemtitle">个人描述</text>
      </view>
    </view>
    <view class="mt12">
      <template v-if="description && description?.length > 0">
        <view v-for="item in description" :key="item.desId">
          <view class="aitem mt20">
            <i class="cir"></i>
            <text class="subtitle">{{ item.desName }}</text>
          </view>
          <view class="mt8 description">
            <text>{{ item.desContent }}</text>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="gray font14">
          <text>自我评价具有重要的意义</text>
        </view>
      </template>
    </view>
    <view class="line mt24"></view>
  </view>
</template>
<script lang="ts" setup>
  import { ResumeEditDesDto } from '@/services/my/data-contracts'
  import { inject } from 'vue'
  interface desDto {
    description?: ResumeEditDesDto[] | null
    resumeId: number
  }
  const { description } = defineProps<desDto>()
</script>
