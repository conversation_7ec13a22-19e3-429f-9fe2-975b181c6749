<template>
  <view class="train mt32">
    <view class="head-title">
      <view class="aitem">
        <text class="itemtitle">培训经历</text>
      </view>
    </view>
    <template v-if="train && train?.length > 0">
      <view v-for="item in train" :key="item.trainId" class="vertical-line">
        <view class="font12 aitem gray timeline">
          <i class="circle"></i>
          <text>{{ item.trainBeginTime }}-{{ item.trainEndTime }}</text>
        </view>
        <view class="subtitle mt12">
          {{ item.trainCourse }}
        </view>
        <view class="aitem mt8 font12 gray">
          <text>{{ item.trainInstitution }}</text>
        </view>
        <view class="mt12 description" v-if="item.trainingDescription">
          <text>{{ item.trainingDescription }}</text>
        </view>
      </view>
    </template>
    <template v-else>
      <view class="mt12 gray font14">
        <text>培训经历将增加您的竞争力</text>
      </view>
    </template>
    <view class="line mt24"></view>
  </view>
</template>
<script lang="ts" setup>
  import { ResumeEditTrainDto } from '@/services/my/data-contracts'
  import { inject } from 'vue'
  interface trainDto {
    train?: ResumeEditTrainDto[] | null
    resumeId: number
  }
  const { train, resumeId } = defineProps<trainDto>()
</script>
