<template>
  <view class="work mt32">
    <view class="head-title">
      <view class="aitem">
        <text class="itemtitle">工作经历</text>
      </view>
    </view>
    <template v-if="work && work?.length > 0">
      <view v-for="item in work" :key="item.workId" class="work-item vertical-line">
        <view class="font12 aitem gray timeline">
          <i class="circle"></i>
          <text>{{ item.timeRang }}</text>
          <text v-if="item.longestTime && item.timeRang != '未填在职时间'" class="tip font12"
            >最长</text
          >
          <text v-if="item.jobSeekerAbroadExperience" class="tip higher">海外经验</text>
        </view>
        <view class="subtitle mt12">
          {{ item.entName?item.entName:'未填写公司名称' }}
        </view>
        <view class="aitem mt8 font12 gray">
          <text>{{ item.enterprisePropertyName }}</text>
          <view
            v-if="item.enterpriseEmployeeNumberName && item.enterprisePropertyName"
            class="split"
          ></view>
          <text >{{ item.enterpriseEmployeeNumberName }}</text>
          <view v-if="item.workIndustryName" class="split"></view>
          <text>{{ item.workIndustryName }}</text>
          <view v-if="item.workPlace" class="split"></view>
          <text>{{ item.workPlace }}</text>
        </view>
        <view class="mt24">
          <text class="subtitle">{{ item.positionName }}</text>
          <text class="tip higher" v-if="item.hasHigher">高级人才</text>
        </view>
        <view>
          <text class="tip tag" v-if="item.department">{{ item.department }}</text>
          <text class="tip tag" v-if="item.workPropertyName">{{ item.workPropertyName }}</text>
          <text class="tip tag" v-if="item.positionLevelName">{{ item.positionLevelName }}</text>
          <text v-for="i in item.keywordIds" class="tip tag" :key="i.keywordID">{{ i.keywordName }}</text>
        </view>
        <view class="mt12 description">
          <text>{{ item.positionDescription }}</text>
        </view>
        <view
          class="detail mt12"
          v-if="item.higherUp || item.underlingNum || item.leavingReason || item.jobPerformance"
        >
          <view class="detail-line" v-if="item.higherUp">
            <text class="gray pd">汇报对象</text>
            <text>{{ item.higherUp }}</text>
          </view>
          <view class="mt8" v-if="item.underlingNum || 0 > 0">
            <text class="gray pd">下属人数</text>
            <text>{{ item.underlingNum }}</text>
          </view>
          <view class="mt8" v-if="item.leavingReason">
            <text class="gray pd">离职原因</text>
            <text>{{ item.leavingReason }}</text>
          </view>
          <view class="mt12" v-if="item.jobPerformance">
            <text>{{ item.jobPerformance }}</text>
          </view>
        </view>
      </view>
    </template>
    <template v-else>
      <view class="mt12 gray font14"> 工作经历是简历的关键部分 </view>
    </template>
    <view class="line mt24"></view>
  </view>
</template>
<script lang="ts" setup>
  import { WorkPartDto } from '@/services/my/data-contracts'
  import { inject } from 'vue'

  interface workPart {
    work?: WorkPartDto[] | null
    resumeId?: number
  }
  const { work, resumeId } = defineProps<workPart>()
</script>
