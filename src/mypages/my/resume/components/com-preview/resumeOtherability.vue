<template>
  <view class="otherAbility mt32">
    <view class="head-title">
      <view class="aitem">
        <text class="itemtitle">其他技能</text>
      </view>
    </view>
    <view class="mt32 font14">
      <view class="fl">
        <text class="gray pdr">驾照</text>
        <text>{{ otherAbility?.drivingLicense || '无' }}</text>
      </view>
      <view class="fr">
        <text class="gray pdr">电脑水平</text>
        <text>{{ otherAbility?.computerLevel || '无' }}</text>
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
  import { OtherAbilityDto } from '@/services/my/data-contracts'
  import { inject } from 'vue'

  interface otherAbility {
    otherAbility?: OtherAbilityDto
    resumeId: number
  }
  const { otherAbility, resumeId } = defineProps<otherAbility>()
</script>
