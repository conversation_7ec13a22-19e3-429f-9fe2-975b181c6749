<template>
  <view class="album mt32">
    <text class="itemtitle">个人形象</text>
    <scroll-view scroll-x class="mt24">
      <view class="album-list">
        <view class="album-list-item">
          <image
            class="img"
            src="https://image.gxrc.com/gxrcsite/wxMiniApp/2022/<EMAIL>"
            mode="scaleToFill"
          />
        </view>
        <view v-for="(item, index) in album" :key="index" class="album-list-item">
          <image class="img" :src="item" mode="scaleToFill" />
        </view>
      </view>
    </scroll-view>
  </view>
</template>
<script lang="ts" setup>
  import { inject } from 'vue'

  interface albumType {
    album?: string[] | null
  }
  const { album } = defineProps<albumType>()
</script>
<style lang="scss" scoped>
  .album {
    &-list {
      display: flex;
      overflow-x: auto;
      flex-flow: row nowrap;
      &-item {
        padding-right: 12px;
        .img {
          width: 80px;
          height: 80px;
        }
      }
    }
  }
</style>
