<template>
  <view class="otherAbility mt32">
    <view
      class="head-title"
      @tap="navigateTo(`/mypages/my/resume/otherSkill?resumeId=${resumeId}`)"
    >
      <view class="aitem">
        <text class="itemtitle">其他技能</text>
      </view>
      <view class="edit aitem right">
        <i class="iconfont icon-bianji pd"></i>
        <text>编辑</text>
      </view>
    </view>
    <view class="mt32 font14">
      <view class="fl">
        <text class="gray pdr">驾照</text>
        <text>{{ otherAbility?.drivingLicense || '无' }}</text>
      </view>
      <view class="fr">
        <text class="gray pdr">电脑水平</text>
        <text>{{ otherAbility?.computerLevel || '无' }}</text>
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
  import { OtherAbilityDto } from '@/services/my/data-contracts'
  import { inject } from 'vue'
  import { useNavigate } from '@/hooks/page/useNavigate'

  // #ifdef MP-WEIXIN || MP-ALIPAY
  const { navigateTo } = inject<any>('function')
  // #endif
  // #ifdef MP-TOUTIAO
  const { navigateTo } = useNavigate()
  // #endif

  interface otherAbility {
    otherAbility?: OtherAbilityDto
    resumeId: number
  }

  const { otherAbility, resumeId } = defineProps<otherAbility>()
</script>
