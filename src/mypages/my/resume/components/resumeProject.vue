<template>
  <view class="project mt32">
    <view class="head-title" @tap="navigateTo(`/mypages/my/resume/project?resumeId=${resumeId}`)">
      <view class="aitem">
        <text class="itemtitle">项目经历</text>
      </view>
      <view class="edit aitem right">
        <i class="iconfont icon-tianjia pd"></i>
        <text>添加</text>
      </view>
    </view>
    <template v-if="project && project?.length > 0">
      <view
        v-for="item in project"
        :key="item.id"
        class="mt32 vertical-line"
        @tap="navigateTo(`/mypages/my/resume/project?resumeId=${resumeId}&projectId=${item.id}`)"
      >
        <view class="font12 aitem gray timeline">
          <i class="circle"></i>
          <text>{{ item.beginTime }}-{{ item.endTime }}</text>
          <i class="iconfont icon-arrowRight14 arrowright"></i>
        </view>
        <view class="subtitle mt12">
          {{ item.projectName }}
        </view>
        <view v-if="item.projectDescription" class="description mt12">
          <text>{{ item.projectDescription }}</text>
        </view>
      </view>
    </template>
    <template v-else>
      <view class="mt12 gray font14">
        <text>出色的项目经验为你在竞争中增加一个重要的砝码</text>
      </view>
    </template>
    <view class="line mt24"></view>
  </view>
</template>
<script lang="ts" setup>
  import { ResumeEditProjectDto } from '@/services/my/data-contracts'
  import { inject } from 'vue'
  import { useNavigate } from '@/hooks/page/useNavigate'

  // #ifdef MP-WEIXIN || MP-ALIPAY
  const { navigateTo } = inject<any>('function')
  // #endif
  // #ifdef MP-TOUTIAO
  const { navigateTo } = useNavigate()
  // #endif
  interface projectDto {
    project?: ResumeEditProjectDto[] | null
    resumeId?: number
  }
  const { project } = defineProps<projectDto>()
</script>
