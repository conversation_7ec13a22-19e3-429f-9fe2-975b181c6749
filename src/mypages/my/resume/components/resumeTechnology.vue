<template>
  <view class="technology mt32">
    <view
      class="head-title"
      @tap="navigateTo(`/mypages/my/resume/technology?resumeId=${resumeId}`)"
    >
      <view class="aitem">
        <text class="itemtitle">技术能力</text>
      </view>
      <view class="edit aitem right">
        <i class="iconfont icon-tianjia pd"></i>
        <text>添加</text>
      </view>
    </view>
    <template v-if="technology && technology?.length > 0">
      <view
        v-for="item in technology"
        :key="item.techId"
        class="mt24"
        @tap="
          navigateTo(
            `/mypages/my/resume/technology?resumeId=${resumeId}&technologyId=${item.techId}`
          )
        "
      >
        <view class="aitem">
          <text class="subtitle">{{ item.techName }}</text>
          <i class="iconfont icon-arrowRight14 arrowright"></i>
        </view>
        <view class="aitem mt8 font12 gray">
          <text v-if="item.techLevel">{{ item.techLevel }}</text>
          <view v-if="item.techLevel" class="split"></view>
          <text>{{ item.monthUsed }}</text>
        </view>
      </view>
    </template>
    <template v-else>
      <view class="mt12 gray font14">
        <text>技能是工作能力的重要体现</text>
      </view>
    </template>
    <view class="line mt24"></view>
  </view>
</template>
<script lang="ts" setup>
  import { TechnologyDto } from '@/services/my/data-contracts'
  import { inject } from 'vue'
  import { useNavigate } from '@/hooks/page/useNavigate'

  // #ifdef MP-WEIXIN || MP-ALIPAY
  const { navigateTo } = inject<any>('function')
  // #endif
  // #ifdef MP-TOUTIAO
  const { navigateTo } = useNavigate()
  // #endif
  interface technologyDto {
    technology?: TechnologyDto[] | null
    resumeId: number
  }
  const { technology, resumeId } = defineProps<technologyDto>()
</script>
