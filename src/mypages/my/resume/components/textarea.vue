<template>
  <view class="componentview">
    <uni-nav-bar
      :title="title"
      left-icon="left"
      :fixed="true"
      :border="false"
      :status-bar="true"
      @clickLeft="back"
    />
    <view v-if="show" class="componentview-item">
      <!-- <scroll-view class="scroll" :style="{height:scrollHeight}"  scroll-x="false" scroll-y>
        <uni-easyinput
          v-model="text"
          type="textarea"
          :placeholder="placeholder"
          :maxlength="len"
          :inputBorder="false"
          :clearable="false"
          :autoHeight="true"
          :focus="true"
        />
      </scroll-view> -->
      <textarea
        ref="texta"
        v-model="text1"
        :placeholder="placeholder"
        placeholder-style="font-size:15px"
        :auto-focus="true"
        :adjust-position="false"
        :maxlength="len"
        :enableNative="false"
        @keyboardheightchange="keyboardChange"
        class="textarea"
      />
    </view>
    <view class="bottomAction">
      <view class="jisuan">
        <text :style="`color: ${isOver ? 'red' : '#608bf8'}`">{{ count }}</text
        >/{{ len }}
      </view>
      <!-- #ifdef MP-WEIXIN -->
      <view class="actions">
        <view class="assistant" v-if="props.resumePart && showAi" @tap="open"
          ><i class="iconfont icon-icon_ai1 icon" style="margin-top: -2px;"></i
          ><view
            ><text style="font-weight: bolder">AI</text
            ><text style="margin: 0 4rpx">润色</text>(DeepSeek)</view
          ></view
        >
        <view class="discern" @tap="discern">
          <i class="iconfont icon-icon_pic icon"></i><view>拍照识别</view></view
        >
      </view>
      <!-- #endif -->
    </view>

    <!-- <view class="componentview-footer">
      <button class="button" @tap="comfirm">保存</button>
    </view> -->
    <save-button @right-click="comfirm" />
    <!-- #ifdef MP-WEIXIN -->
    <uni-popup ref="popup" background-color="#fff" :mask-click="false" @change="change">
      <ai-assistant
        v-if="props.resumePart"
        :resume-part="props.resumePart"
        :text="text1"
        :show-popup="assistantShow"
        @close="close"
        @insert="insert"
      />
    </uni-popup>
    <select-resumeimg-popup v-if="showPopup" ref="resumeImgRef" @success="selectConfrim" />
    <!-- #endif -->
  </view>
</template>

<script lang="ts" setup>
  import { computed, toRefs, watch, ref, onMounted, Ref, unref } from 'vue'
  import saveButton from './save-button.vue'
  import AiAssistant from './AiAssistant.vue'
  import { CloneJobSeekerResumePart } from '@/services/my/data-contracts'
  import selectResumeimgPopup from '@/mypages/my/photo/components/selectResumeimgPopup.vue'
  import { useOptionsStore } from '@/store/modules/options'
  interface Prop {
    title?: string

    value?: Ref<string | null>

    placeholder?: string

    len?: number

    resumePart?: CloneJobSeekerResumePart

    showPopup?: boolean

    isFromRegister?: boolean
  }

  const store = useOptionsStore()
  const props = defineProps<Prop>()
  const { title, placeholder, showPopup } = toRefs(props)

  const values = computed(() => toRefs(props).value?.value)
  const len = computed(() => toRefs(props).len || 150)

  const emit = defineEmits<{
    (e: 'confirm', text: string): void
    (e: 'back'): void
  }>()

  const text1 = ref('')

  watch(
    () => values?.value,
    () => {
      text1.value = unref(values?.value)
    },
    {
      immediate: true,
      deep: true
    }
  )
  let height = $ref<number>(0)
  const texta = $ref<any>(null)

  const resumeImgRef = $ref<any>(null)

  const { windowHeight } = uni.getSystemInfoSync()

  let show = $ref(false)
  let assistantShow = $ref(false)
  const popup = $ref<any>(null)
  onMounted(() => {
    show = true
  })
  const count = computed(() => {
    const le = text1.value?.length || 0
    return le > len.value ? len : le
  })
  const isOver = computed(() => {
    return text1.value?.length > unref(len.value)
  })

  const openRegisterAI = computed(() => store.openRegisterAI)
  const openUpdateResumeAI = computed(() => store.openUpdateResumeAI)

  const showAi = computed(() => {
    return (
      (props.isFromRegister && openRegisterAI.value) ||
      (!props.isFromRegister && openUpdateResumeAI.value)
    )
  })
  const keyboardHeight = computed(() => {
    const h = height > 0 ? height + 'px' : '88px'
    return h
  })
  const inputHeight = computed(() => windowHeight - 235 + 'px')
  const scrollHeight = computed(() => windowHeight - 235 - height + (height > 0 ? 100 : -20) + 'px')

  const open = () => {
    popup.open('right')
  }
  const close = () => {
    popup.close()
  }
  const change = (e: any) => {
    console.log(e)
    assistantShow = e.show
  }

  const discern = () => {
    resumeImgRef?.open()
  }
  const back = () => {
    emit('back')
  }
  const comfirm = () => {
    if (text1.value?.length > unref(len.value)) {
      uni.showToast({ title: '最多输入' + unref(len.value) + '个字符', icon: 'none' })
      return
    }

    emit('confirm', text1.value || '')
  }

  const insert = (value: string) => {
    if (!text1.value) text1.value = ''
    text1.value += value
    close()
  }

  const keyboardChange = (event: any) => {
    if (event.type == 'keyboardheightchange') {
      height = event.detail?.height
    }
  }
  const selectConfrim = (text: string) => {
    if (!text1.value) text1.value = ''
    text1.value += text
  }
</script>
<style lang="scss" scoped>
  .componentview {
    padding-bottom: 1px;
    background: #ffffff;
    position: relative;
    height: 100vh;
    &-item {
      position: relative;
      padding: 20rpx 40rpx;
      box-sizing: border-box;
    }
    ::v-deep .uni-easyinput .uni-easyinput__content-textarea {
      min-height: v-bind(inputHeight);
      // height: v-bind(inputHeight);
    }

    .textarea {
      width: 100%;
      min-height: v-bind(scrollHeight);
      font-size: 15px;
    }

    .bottomAction {
      position: fixed;
      bottom: v-bind(keyboardHeight);
      // display: flex;
      // // #ifdef MP-WEIXIN
      // justify-content: space-between;
      // // #endif
      // // #ifdef MP-TOUTIAO || MP-ALIPAY
      // justify-content: flex-end;
      // // #endif
      // align-items: center;
      width: 100%;
      padding: 0 20px;
      box-sizing: border-box;
      z-index: 99;
      background: #fff;
      .actions {
        display: flex;
      }
      .assistant {
        margin-right: 20rpx;
        border: 2rpx solid rgba(36, 123, 255, 0.25);
        letter-spacing: 1rpx;
      }
      .assistant,
      .discern {
        background: #eef5ff;
        padding: 12rpx 24rpx;
        border-radius: 30rpx;
        color: #005eed;
        font-size: 26rpx;
        width: auto;
        z-index: 33;
        display: flex;
        align-items: center;
        line-height: 32rpx;
      }
      .discern {
        background: #f5f7fa;
        color: #666666;
      }
      .icon {
        font-size: 32rpx;
        padding-right: 4rpx;
        //margin-top: -2rpx;
      }
      .jisuan {
        display: flex;
        justify-content: flex-end;
        // margin-right: 20px;
        margin-bottom: 5px;
      }
    }
  }
</style>
