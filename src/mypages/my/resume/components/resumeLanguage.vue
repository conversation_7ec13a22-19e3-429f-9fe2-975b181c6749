<template>
  <view class="language mt32">
    <view class="head-title" @tap="add">
      <view class="aitem">
        <text class="itemtitle">语言技能</text>
      </view>
      <view v-if="language && language?.length < 3" class="edit aitem right">
        <i class="iconfont icon-tianjia pd"></i>
        <text>添加</text>
      </view>
    </view>
    <view class="mt8">
      <template v-if="language && language?.length > 0">
        <view
          v-for="item in language"
          :key="item.listId"
          @tap="navigateTo(`/mypages/my/resume/language?Id=${item.listId}&resumeId=${resumeId}`)"
        >
          <view class="aitem mt24">
            <text class="subtitle">{{ item.langName }}</text>
            <i class="iconfont icon-arrowRight14 arrowright"></i>
          </view>
          <view class="aitem mt8 font12 gray">
            <text v-if="item.langLevel">综合能力{{ item.langLevel }}</text>
            <view v-if="item.lsLevel" class="split"></view>
            <text>听说能力{{ item.lsLevel }}</text>
            <view v-if="item.rwLevel" class="split"></view>
            <text>读写能力{{ item.rwLevel }}</text>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="mt4 gray font14">
          <text>出色的外语水平是重要的竞争力</text>
        </view>
      </template>
    </view>
    <view class="line mt24"></view>
  </view>
</template>
<script lang="ts" setup>
  import { LanguageDto } from '@/services/my/data-contracts'
  import { inject } from 'vue'
  import { useNavigate } from '@/hooks/page/useNavigate'

  // #ifdef MP-WEIXIN || MP-ALIPAY
  const { navigateTo } = inject<any>('function')
  // #endif
  // #ifdef MP-TOUTIAO
  const { navigateTo } = useNavigate()
  // #endif
  interface languageDto {
    language?: LanguageDto[] | null
    resumeId: number
  }
  const { language, resumeId } = defineProps<languageDto>()

  const add = () => {
    if (language && language.length === 3) return

    const index = (language?.length ?? 0) + 1
    console.log(`/mypages/my/resume/language?Id=${index}&resumeId=${resumeId}`)
    navigateTo(`/mypages/my/resume/language?Id=${index}&resumeId=${resumeId}`)
  }
</script>
<style lang="scss">
  .mt4 {
    margin-top: 4px;
  }
</style>
