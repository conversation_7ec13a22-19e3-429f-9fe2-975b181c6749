<template>
    <view>
        <!-- <view class="otherswrite" @click="OthersWrite"><i class="iconfont icon-eye"></i>看看别人怎么写</view> -->
        <uni-popup ref="popupW" background-color="#fff" :is-mask-click="false">
            <view class="pop-main">
                <view @click="closeW" class="closeBtn"><i class="icon-close iconfont"></i></view>
                <view class="pop-templet">
                    <view class="cont">
                        <view class="tit">{{ arrList[index].modelName }}</view>
                        <pre>{{ arrList[index].model }}</pre>
                    </view>

                </view>
                <view class="bnt-box">
                    <button class="but shiyong" @click="userT"><i class="iconfont icon-fuzhi"></i>使用该模板</button>
                    <button class="but one" @click="Changeother"><i class="iconfont icon-huanyigeiconx"></i>换一个</button>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script setup lang="ts">
import { toRefs, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { Register } from '@/services/my/Register'

// interface Props {
//     modelValue?: string | null

//     title: string

//     placeholder?: string

//     len?: number
// }
let resumeid = $ref(0)
let index = $ref(0)

let arrList = $ref([
    {
        model: '模板获取失败',
        modelName: '提示',
        modelType: 0
    }
])

onLoad(async (option) => {
    const { id } = option
    resumeid = Number(id)
})
// const props = defineProps<Props>()



const emit = defineEmits<{
    (e: 'useTemplet'): void
}>()

let isHasdata = $ref(false)
const popupW = $ref(null)



const userT = () => {
    let data = arrList[index]

    emit('useTemplet', data)
    popupW.close()
}
const OthersWrite = async () => {
    if (isHasdata) {
        popupW.open('bottom')
        return false
    }
    let Date = await Register.registerdescribemodel({ resumeid: Number(resumeid), modeltype: 0 })

    if (Date.length > 0) {
        isHasdata = true
        arrList = Date
    } else {
        isHasdata = false
    }
    popupW.open('bottom')

}
const closeW = () => {
    popupW.close()
}
const Changeother = () => {
    index++
    if(index>arrList.length-1){
        index=0
    }
}
</script>

<style scoped lang="scss">
.otherswrite {
    display: flex;
    color: #457CCF;
    font-size: 24rpx;
    padding: 0 20rpx;
    .icon-eye{
        font-size: 24rpx;
        padding: 2rpx 3rpx 0 0;
    }
}

.pop-main {
    position: relative;
    padding: 20rpx;

    .closeBtn {
        position: absolute;
        right: 20rpx;
        top: 20rpx;

        .iconfont {
            font-size: 30rpx;
            color: #333333;
        }

    }

    .bnt-box {
        display: flex;
        
      button::after{
        border: none;
      }

        .but {
            height: 60rpx;
            text-align: center;
            font-size: 26rpx;
            line-height: 60rpx;
            border: none;
            display: flex;
        }

        .shiyong {
            width: 216rpx;
            background: #F5F7FA;
            color: #666666;

        }

        .one {
            width: 190rpx;
            background: #EEF5FF;
            color: #457CCF;
        }

    }
}

.pop-templet {
    height: 300rpx;

    .cont {
        line-height: 48rpx;
        color: #333333;

        .tit {
            font-size: 38rpx;
            font-weight: bold;
        }

        .p {
            font-size: 28rpx;
        }
    }

}</style>

