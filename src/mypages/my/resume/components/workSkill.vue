<template>
    <view class="industry-picker clearfix">
        <view class="picker-input" @tap.stop="selecity()">
            <!-- type ==1 这个组件用不上但是先留着 -->
            <text v-if="type == 1" :class="[industryName ? 'uni-ellipsis-1' : 'uni-ellipsis-1 pleaseChoose', textAlign]">{{
                industryName || '请选择' }}</text>

            <view v-if="type == 2" class="industry-ul">
                <view v-for="(item, index) in hideValue" :key="index" class="li">
                    <text v-if="item" class="span">{{ item.keywordName }}</text><i v-if="item"
                        class="iconfont icon-close iconx" @click.stop="delhideValue(index)"></i>
                </view>
                <view v-if="!hideValue || hideValue?.length == 0" class="pleaseChoose">请选择 </view>
            </view>
            <i class="iconfont icon-arrowRight14 icon" :style="type === 1 ? '' : 'bottom: 20rpx;'"></i>
        </view>
        <uni-popup ref="showRight" type="right" :is-mask-click="true" class="pop-industry-box">
            <view class="bg-white h100">
                <uni-nav-bar left-text="工作技能" left-width="200rpx" height="44px" :fixed="true" :status-bar="true"
                    left-icon="left" :border="false" @clickLeft="closeDrawer" />
                <view class="selected-wrap bg-white">
                    <view class="count">已选( <label class="bule">{{ selectedCount }}</label>/{{ maxCount }})</view>
                    <view class="selected-items clearfix">
                        <scroll-view class="scroll-view" scroll-x="true">
                            <view class="content">
                                <view v-for="val in activeGroup" :key="val.keywordID" class="item" @click="deleteCity(val)">
                                    {{ val.keywordName }}
                                    <i class="iconclose">X</i>
                                </view>
                            </view>
                        </scroll-view>
                    </view>
                </view>
                <view class="sel-main clearfix bg-white" :style="`width:` + width + 'px'">
                    <view class="box-city clearfix">
                        <!-- 第一组 -->
                        <view class="sel-industry-box sel-industry-boxA">
                            <scroll-view :style="{ height: scrollHeight1 + 'px' }" scroll-y="true" :show-scrollbar="false">
                                <view class="ul">
                                    <view v-for="(item, index) in firstArr" :key="index" clickable :class="[
                                        'li clearfix',
                                        { on: item.selected }
                                    ]" @click="cityChooseA(item, index)">
                                        <view class="sdl">{{ item.typeName }}</view>
                                    </view>
                                    <!-- -------------------- -->
                                    <view  clickable :class="[
                                        'li clearfix',
                                        { on: zdyselected }
                                    ]" @click="cityChooseB">
                                        <view class="sdl">自定义</view>
                                    </view>
                                </view>
                            </scroll-view>
                        </view>
                    </view>
                    <!-- 第二组 -->
                    <view class="sel-industry-box sel-industry-boxC">
                        <view class="city-boxC">
                            <scroll-view :style="{ height: scrollHeight1 + 'px' }" scroll-y="true" @scroll="scrollB"
                                :scroll-into-view="scrollIntoView">
                                <view class="ul">
                                    <view v-for="(item, index) in firstArr" :key="index" class="box">
                                        <view class="title" :id="'item_' + index">{{ item.typeName }}</view>
                                        <view class="li-box">
                                            <view v-for="(items, indexs) in item.antistops" :key="indexs" clickable
                                                :class="['li clearfix', { isseled: items.selected }]"
                                                @click="cityChooseC(items)">
                                                <view class="sdl">{{ items.keywordName }}</view>
                                            </view>
                                        </view>
                                    </view>
                                    <!-- 自定义 -->
                                    <view class="box ">
                                        <view class="title" >自定义</view>
                                        <view class="zdy" id="zdy">
                                            <view class="zdy-btn" @click="openInput">+ 自定义</view>
                                        </view>
                                        <view class="li-box">
                                            <view v-for="(items, indexs) in  userDefined" :key="indexs" clickable
                                                :class="['li clearfix', { isseled: items.selected }]"
                                                @click="delChooseC(items,indexs)">
                                                <view class="sdl">{{ items.keywordName }}</view>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </scroll-view>
                        </view>
                    </view>
                </view>
                <view class="btn-box" @click="bindComfirm">
                    <button class="login-wrap-button">保存</button>
                </view>
            </view>
            <!-- 分割线··············· -->
            <uni-popup ref="popup" background-color="#fff" :is-shadow="false">
                <view class="pop-zdy">
                    <view class="tit clearfix">输入技能关键词 <i class="iconfont icon-close" @click="closepopup"></i></view>
                    <view class="inp">
                        <view class="sdl">
                            <uni-easyinput v-model="tagsName" placeholder="请输入10个字以内的标签" :maxlength="10" :trim="true"/>
                        </view>
                        <view class="sdr">
                            <button type="primary" plain="true" class="btn" @click="add(tagsName)">添加</button>
                        </view>
                    </view>
                </view>
            </uni-popup>



        </uni-popup>
    </view>
</template>
<script lang="ts">
export default {
    options: { styleIsolation: 'shared' }
}
</script>
<script lang="ts" setup>
import { computed, watch, ref, nextTick, getCurrentInstance } from 'vue'
import { Options } from '@/services/my/Options'
interface dataList {
    hideValue: []
    title?: string
    maxCount?: number
    textAlign?: string
    type?: number
    positiontypeid?: number
}
let allIndustry = $ref<Array<number | string>>()
let industryName = $ref<string>('')
let firstArr = $ref<Array<number | string>>()
let activeGroup = $ref<Array[]>([])
let navTop = uni.getMenuButtonBoundingClientRect().top
let selectedH = $ref<number>(0)
let tagsName = $ref('')
let zdyselected= $ref<boolean>(false)
const {
    hideValue = [],
    maxCount = 1,
    title = '请选择',
    textAlign = 'right',
    type = 2,
    positiontypeid = 0
} = defineProps<dataList>()
let userDefined: any[] = []
const instance = getCurrentInstance()
const scrollHeight1 = computed(() => {
    return uni.getSystemInfoSync().windowHeight - 180 - selectedH - navTop
})
const scrollIntoView = ref('')
const width = computed(() => {
    return uni.getSystemInfoSync().windowWidth || '320'
})
const showRight = $ref<any>(null)
const popup = $ref<any>(null)

const emit = defineEmits<{
    (e: ['change', 'delItem', 'open', 'close']): void
}>()
const selectedCount = computed(() => {
    return activeGroup.length || 0
})
const canSelectMore = computed(() => {
    return maxCount <= selectedCount.value
})
const singleMode = computed(() => {
    return maxCount === 1
})
watch(
    () => selectedCount.value,
    () => {
        nextTick(() => {
            hideInspect()
        })
    }
)
watch(
    () => hideValue,
    () => {
        nextTick(() => {
            industryName = ''
            hideValue.forEach((item: any) => {
                industryName = industryName + item.keywordName + '/'
            })
        })
    }
)
const getDate = async () => {
    activeGroup = []
    firstArr = []
    let datas = []
    userDefined = []
    const Data: Array = await Options.positionantistops({ positiontypeid: positiontypeid })
    datas = Data.data
    allIndustry = datas
    datas.map((item: any, index: any) => {
        item.selected = false
        if (index == 0) {
            item.selected = true
        }
    })
    firstArr = datas
    // let zdiArr={
    //     typeName:'自定义',
    //     antistops:[]
    // }
    // firstArr.push(zdiArr)
    activeGroup = hideValue
    // console.log("我获取了数据", activeGroup)
    if (hideValue) {
      
        hideValue.forEach((item: any) => {
            firstArr.forEach((p: any) => {
                p.antistops.forEach((j: any) => {
                    // console.log("找元素")
                    if (j.keywordID == item.keywordID) {
                        j.selected = true
                    }
                })
            })
            if(item.keywordID==0){
                item.selected=true 
                userDefined.push(item)
            }
        })
    }
}
const selecity = () => {
    if (!positiontypeid) {
        uni.showToast({
            title: '请先选择职位类型',
            duration: 2000,
            icon: 'none'
        })
        return false

    }



    getDate()
    showRight.open()
    emit('open')
}
const bindComfirm = () => {
    if (activeGroup.length < 1) {
        uni.showToast({
            title: '请至少选择一个',
            duration: 2000,
            icon: 'none'
        })
        return
    }
    // console.log("保存",activeGroup)
    emit('change', activeGroup)
    showRight.close()
    emit('close')
}


const closeDrawer = () => {
    showRight.close()
    emit('close')
}

const cityChooseA = (city: any, index: number) => {
    //第一行点击
    allIndustry.forEach((item: any) => {
        item.selected = false
    })
    zdyselected = false
    city.selected = true
    nextTick(() => {
        scrollIntoView.value = `item_${index}`
    })
}
  const cityChooseB = () => {
    zdyselected=true
    allIndustry.forEach((item: any) => {
        item.selected = false
    })
    // console.log("点击了自定义")
    nextTick(() => {
        scrollIntoView.value = `zdy`
    }) 
}
const onSingleMode = (city: any) => {
    //单选模式
    allIndustry.forEach((item: any) => {
        item.selected = false
    })
    firstArr.forEach((item: any) => {
        item.selected = false
    })
    city.selected = true
    activeGroup = [city]
    bindComfirm()
}
const onMultipleMode = (city: any) => {
    //多选模式
    const isChecked = city.selected
    if (isChecked) {
        //被选过了
        deleteCity(city)
        return false
    }
    //如果没有被选过检测下面的子集和往上的父级
    if (!isChecked) {
        activeGroup.forEach((i: any) => {
            if (
                i.keywordID == city.keywordID
            ) {
                deleteCity(i)
            }
        })
        if (canSelectMore.value) {
            //如果没选过而且超限了
            const messageText = `最多只能选择${maxCount}个选项`
            uni.showToast({
                title: messageText,
                duration: 2000,
                icon: 'none'
            })
        } else {
            addCity(city)
        }
    }
}
const addCity = (city: any) => {
    console.log("选择了")
    city.selected = true
    activeGroup.push(city)
}
const cityChooseC = (city: any) => {
    if (!city) return
    if (singleMode.value) {
        onSingleMode(city)
    } else {
        onMultipleMode(city)
    }
}
const deleteCity = (item: any) => {
    item.selected = false
    activeGroup = activeGroup.filter((i: any) => i.keywordName !== item.keywordName)

    userDefined = userDefined.filter((i: any) => i.keywordName !== item.keywordName)
    firstArr.forEach((p: any) => {
        p.antistops.forEach((j: any) => {
            // console.log("找元素")
            if (j.keywordID == item.keywordID) {
                j.selected = false
            }
        })
    })


}
const delChooseC =(item: any,index:number)=>{
    activeGroup = activeGroup.filter((i: any) => i.keywordName !== item.keywordName)
    userDefined.splice(index, 1)
}

const delhideValue = (index: number) => {
    emit('delhideValue', index)
}
const hideInspect = () => {
    nextTick(() => {
        const query = uni.createSelectorQuery().in(instance)
        query
            .select('.selected-items')
            .boundingClientRect((res) => {
                selectedH = parseInt(res?.height)
            })
            .exec()
    })
    return false
}
const scrollB = (e: Record<string, any>) => {
    // console.log("我在滚动", e.detail)
}
const openInput = () => {
    popup.open('bottom')
}
const add = async (tagName: string) => {
    if (tagName.length == 0 || tagName == null) {
        msg('自定义标签不能为空')
        return false
    }

    let prevent = 1
    if (userDefined.length >= 10 && prevent == 1) {
        prevent = 2
        msg('自定义标签不能超过10个')
        return false
    }
    if (canSelectMore.value) {
            //如果没选过而且超限了
            const messageText = `最多只能选择${maxCount}个选项`
            prevent = 2
            msg(messageText)
            return false
        } 

    if (tagName.length > 20) {
        prevent = 2
        msg('标签长度不能超过20个字符')
        return false
    }
    userDefined.forEach((i: any) => {
        if (i.keywordName == tagName) {
            prevent = 2
            msg('该技能已存在')
            return false
        }
    })

    firstArr.forEach((p: any) => {
        p.antistops.forEach((j: any) => {
            if (j.keywordName == tagName) {
                prevent = 2
                msg('该技能已存在以上选项中')
               return false
            }
        })
    })

    if (prevent != 2) {
        let arr = {
            keywordID: 0,
            keywordName: tagName,
            selected:true
        }
        userDefined.push(arr)
        activeGroup.push(arr)
        prevent = 1
        console.log("添加成功了",userDefined)
        tagsName=''
        popup.close('bottom')
    }
}
const closepopup=()=>{
    popup.close('bottom')
}
const msg = (txt: string) => {
    uni.showToast({
        title: txt,
        icon: 'none',
        duration: 2000
    })
}
</script>
  
<style lang="scss" scoped>
$blue: #5e8df5;

.h100 {
    height: 100%;
}

.industry-picker {
    .picker-input {
        flex: 1;
        width: 100%;
        display: flex;
        box-sizing: border-box;
        min-height: 36px;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        position: relative;
        font-size: 28rpx;

        .left {
            position: absolute;
            left: 0;
            text-align: left;
        }

        .right {
            position: absolute;
            right: 38rpx;
            text-align: right;
        }

        .uni-ellipsis-1 {
            width: 100%;
            font-size: 15px;
        }
    }

    .icon {
        position: absolute;
        right: 0;
        font-size: 24rpx;
        color: #808080;
    }

    .industry-ul {
        padding-bottom: 10rpx;
        width: 100%;

        .li {
            float: left;
            margin: 12rpx 10rpx 0 0;
        }

        .span {
            background: #eef5ff;
            float: left;
            color: #457ccf;
            font-size: 28rpx;
            padding: 0rpx 0rpx 0 20rpx;
            height: 54rpx;
            line-height: 54rpx;
        }

        .iconx {
            font-size: 18rpx;
            padding: 0rpx 15rpx 0 20rpx;
            height: 54rpx;
            line-height: 54rpx;
            background: #eef5ff;
            float: left;
            color: #457ccf;
        }
    }
}

.bg-white {
    background: #fff;
}

.pop-industry-box {
    background: #fff;
    width: 100%;

    .btn-box {
        background: #fff;
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        border-top: 1px solid #eee;

        .login-wrap-button {
            border: none;
            background: #5e8df5;
            color: #fff;
            margin: 10px;
            height: 40px;
            line-height: 40px;
            font-size: 16px;
        }
    }

    ::v-deep .uni-navbar__content_view {
        height: 40px;
        line-height: 40px;
    }

    ::v-deep .uni-navbar-btn-text {
        text {
            font-size: 14px !important;
        }
    }
}

/*  #ifdef MP-ALIPAY */
.btn-box {
    background: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    border-top: 1px solid #eee;

    .login-wrap-button {
        border: none;
        background: #5e8df5;
        color: #fff;
        margin: 10px;
        height: 40px;
        line-height: 40px;
        font-size: 16px;
    }
}

::v-deep .uni-navbar__content_view {
    height: 40px;
    line-height: 40px;
}

::v-deep .uni-navbar-btn-text {
    text {
        font-size: 14px !important;
    }
}

/*  #endif  */
.selected-wrap {
    padding: 15rpx 0 30rpx 0;
    display: flex;
    border-top: 1px solid #eeeeee;
    border-bottom: 1px solid #eeeeee;

    .count {
        font-size: 13px;
        color: #666;
        width: 170rpx;
        height: 60rpx;
        line-height: 60rpx;
        padding: 8px 0 0 30rpx;
    }

    .blue {
        color: #228efd;
    }

    .selected-items {
        padding: 8px 0 0px 0;
        flex: 1;
        width: 550rpx;

        .content {
            display: flex;
            flex-direction: row;
            white-space: nowrap;
            /* 防止换行 */
        }

        .item {
            color: #457CCF;
            font-size: 13px;
            padding: 0rpx 10px;
            margin: 0px 5px 0 0;
            display: flex;
            background: #EEF5FF;
            border-radius: 3px;
            height: 60rpx;
            line-height: 60rpx;

            .iconclose {
                font-size: 12px;
                padding-left: 5px;
                color: #457CCF;
            }
        }
    }
}

.sel-main {
    .box-city {
        float: left;
        margin-right: -410rpx;
        position: relative;
    }

    .sel-industry-box {
        float: left;

        .isseled {
            color: $blue;

        }
    }

    .sel-industry-boxA {
        width: 240rpx;
        background: #F5F7FA;

        .on {
            color: $blue;
            background: #ffffff;
        }

        .li {
            padding-left: 30rpx;
            line-height: 104rpx;

        }
    }

    .sel-industry-boxC {
        width: 100%;
        background: #ffffff;

        .city-boxC {
            margin-left: 240rpx;

            .box {
                padding-left: 40rpx;

                .title {
                    font-size: 32rpx;
                    color: #333333;
                    padding: 48rpx 0 30rpx 0;
                    font-weight: bold;
                }

                .li {
                    background: #F5F7FA;
                    height: 64rpx;
                    line-height: 64rpx;
                    padding: 0rpx 20rpx;
                    color: #515459;
                    font-size: 26rpx;
                    flex: 0 0 auto;
                    margin: 20rpx 20rpx 0 0;
                    border-radius: 4rpx;
                    border: 2rpx solid #F5F7FA;
                }

                .isseled {
                    color: #457CCF;
                    border: 2rpx solid #457CCF;
                    background: #EEF5FF;

                }
            }

            .li-box {
                display: flex;
                flex-wrap: wrap;
                justify-content: flex-start;
                align-items: flex-start;
            }

            .zdy {
                .zdy-btn {
                    height: 64rpx;
                    width: 160rpx;
                    line-height: 64rpx;
                    text-align: center;
                    font-size: 26rpx;
                    margin: 20rpx 20rpx 0 0;
                    border-radius: 4rpx;
                    color: #457CCF;
                    border: 2rpx solid #457CCF;
                    background: #EEF5FF;
                }
            }
        }

    }
}

.uni-ellipsis-1 {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.search-bar {
    padding: 10px;

    .search-skip {
        background: #f5f7fa;
        border-radius: 20px;
        height: 35px;
        line-height: 35px;
        color: #999;
        font-size: 13px;
        padding: 0 15px;
    }

    .iconfont {
        font-size: 14px;
        padding-right: 2px;
        float: left;
    }

    .txt {}
}

.popup-search-box {
    height: 100%;

    .search-cell {
        padding: 10px 20px;

        &-box {
            border-bottom: 1px solid #457ccf;
            padding: 10px 0;
            font-size: 14px;
        }

        .uni-input {
            border: none;
            background: #fff;
        }
    }

    .search-cell-group {
        padding: 40rpx;

        .item {
            border-bottom: 1px solid #f2f2f2;
            text-align: left;
            font-size: 28rpx;
            padding: 14rpx 0;
            line-height: 40rpx;
        }

        .title {
            font-size: 28rpx;
        }

        .subhead {
            font-size: 24rpx;
            color: #bbb;
            padding: 10rpx 0 0 0;
        }
    }

    .Nodata {
        text-align: center;
        font-size: 28rpx;
    }
}

.pleaseChoose {
    color: #999;
    // font-size: 26rpx;
    font-weight: 200;
}
.pop-zdy{
    
    .tit{
        font-size: 30rpx;
        padding: 20rpx 30rpx;
        font-weight: bold;
        .iconfont{
            font-size: 28rpx;
            float: right;
            color: #666666;
        }
    }
    .inp {
        display: flex;
        padding: 0 30rpx 20rpx;
        margin-bottom: 20px;
        
        ::v-deep .uni-easyinput__content-input{
            height: 80rpx;
            line-height: 80rpx;
            font-size: 28rpx;
        }
        button {
            width: 160rpx;
            padding: 0 0;
            height: 80rpx;
            line-height: 80rpx;
            border: none;
            color: #fff;
            background: #457CCF;
            margin-left: 10rpx;
            font-size: 28rpx;
        }
        .sdl{
            flex: 1;
            height: 60rpx;
        }
      }
}
</style>
  