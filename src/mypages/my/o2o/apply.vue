<template>
  <view class="o2oapply">
    <scroll-com
      v-if="list"
      ref="scrollRef"
      :refresher-enabled="true"
      :refresher-threshold="35"
      :height="height"
      @scrolltolowers="scrolltolower"
      @refresherrefreshs="refresherrefresh"
    >
      <view
        v-for="item in list"
        :key="item.deliverId"
        class="apply-item"
        @tap="goto(item?.meetingId || 0, item.isBeOverdue)"
      >
        <view class="positionName">{{ item.positionName }}</view>
        <view class="entName">{{ item.enterpriseName }}</view>
        <view class="con">
          <view class="time">投递时间: {{ item.deliverOn }}</view>
          <view class="time">招聘会名: {{ item.meetingName }}</view>
        </view>
      </view>
    </scroll-com>
  </view>
</template>

<script setup lang="ts">
  import scrollCom from '@/pages/message/components/scrollCom.vue'
  import type { ScrollView } from '@/pages/message/type/index'
  import { My } from '@/services/my/My'
  import { onLoad } from '@dcloudio/uni-app'
  import { O2oApplyDto } from '@/services/my/data-contracts'
  import { nextTick } from 'vue'
  // #ifdef  MP-ALIPAY
  import { reportCmPV } from '@/utils/cloudMonitorHelper.js'
  // #endif
  const query = $ref({
    page: 1,
    pagesize: 10
  })

  let total = $ref(0)

  let list = $ref<O2oApplyDto[]>([])
  const scrollRef = $ref<ScrollView | null>(null)

  const height = uni.getSystemInfoSync().windowHeight + 'px'
  onLoad(async (option) => {
    // #ifdef MP-ALIPAY
    reportCmPV({ title: '我的招聘会', query: option })
    // #endif
    await initList()
  })

  const scrolltolower = async () => {
    const page = query.page + 1
    if (page > total) {
      scrollRef?.AddCallBack(false)
      return
    }
    let data = await getList({ page: page, pagesize: query.pagesize })
    if (data.items && data.items.length > 0) {
      list.push(...data.items)
      query.page = page
      scrollRef?.AddCallBack(true)
    } else {
      scrollRef?.AddCallBack(false)
    }
  }

  const refresherrefresh = async () => {
    scrollRef?.hide(false)

    query.page = 1
    query.pagesize = 10
    await initList()
    nextTick(() => {
      scrollRef?.closeRefresh()
      scrollRef?.hide(true)
    })
  }

  const initList = async () => {
    scrollRef?.loading()
    const data = await getList()

    list = data.items || []
    total = data.totalPages || 0
    if (data.items && data.items.length < query.pagesize) {
      scrollRef?.AddCallBack(false)
    } else {
      scrollRef?.AddCallBack(true)
    }
  }

  const getList = async (obj = query) => {
    return await My.myO2oApplyList(obj)
  }

  const goto = (id: number, isover = false) => {
    if (isover) {
      uni.showToast({
        title: '该场招聘会已经过期',
        icon: 'none'
      })
      return
    }
    const url = `/pageSchool/school/jobFairList/jobFairDetail?id=${id}&type=1&&isNetWork=true`
    uni.navigateTo({
      url: url
    })
  }
</script>

<style lang="scss" scoped>
  .o2oapply {
    background: #f7f9fc;
    .apply-item {
      background: #fff;
      margin: 15px;
      padding: 16px 20px;
      .positionName {
        font-size: 16px;
        font-weight: 600;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .entName {
        font-size: 14px;
        color: #666666;
        padding: 4px 0 16px 0;
      }
      .con {
        background: #f5f7fa;
        padding: 11px 10px;
        .time {
          font-size: 13px;
          color: #666666;
          padding: 4px 0;
        }
      }
    }
  }
</style>
