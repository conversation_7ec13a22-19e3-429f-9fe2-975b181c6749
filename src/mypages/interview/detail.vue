<template>
    <view class="interview-detail interview-detail-blue"
     :class="details.attendState==2||details.attendState==3||details.attendState==4||details.attendState==5||details.attendState==8?'interview-detail-gray':''">
        <Topbar :type="'sydw'" />
      <view class="head-title"><text>面试邀请</text></view>
      
      <view class="content">
      <view class="interview-info-bg">
      <view class="ent-info">
        <view class="pic">
            <image :src="details.imageUrl" />
          </view>
          <view class="info">
            <view class="ent-name">
            <text>{{ details.enterpriseName }}</text>
          </view>
          <view class="yaoqing">
            <text>{{ details.enterpriseRemark }}</text>
          </view>

          </view>
  
          </view>


          <view class="interview-info">

          <uni-row class="demo-uni-row" width="730">
            <navigator :url="`/pages/job/position?id=${details.positionGuid}`" class="search-navigator" hover-class="none">
					<uni-col :span="6">
						<view class="label"><text>{{details.interviewPositionTitle}}</text></view>
					</uni-col>
					<uni-col :span="15">
						<view class="neirong"><text>{{ details.interviewPositionName }}</text></view>
					</uni-col>
					<uni-col :span="3">
						<i class="iconfont icon-icon_more"></i>
					</uni-col>
        </navigator>
				</uni-row>

                <uni-row class="demo-uni-row" width="730">
					<uni-col :span="6">
						<view class="label"><text>{{ details.interviewTimeTitle }}</text></view>
					</uni-col>
					<uni-col :span="18">
						<view class="neirong"><text>{{ details.interviewTimeRange }}</text></view>
					</uni-col>
				</uni-row>

                <uni-row class="demo-uni-row" width="730">
					<uni-col :span="6">
						<view class="label"><text>{{ details.enteContactTitle }}</text></view>
					</uni-col>
					<uni-col :span="15">
						<view class="neirong"><text>{{ details.enteContactMan }} {{ details.enteContactPhone }}</text></view>
					</uni-col>
					<uni-col :span="3">
						<view class="shouji-wrap" @click="openCallPhone"><view class="shouji-border"><i class="iconfont icon-shoujihao"></i></view></view>
					</uni-col>
				</uni-row>

                <uni-row class="demo-uni-row" width="730" v-if="details.interviewType==1">
					<uni-col :span="6">
						<view class="label"><text>{{ details.interviewAddrTitle }}</text></view>
					</uni-col>
					<uni-col :span="18">
						<view class="neirong"><text>{{ details.interviewAddr }}</text></view>
					</uni-col>
          <view class="clear"></view>
          <view v-if="details.latitude" class="map">
            <view class="address">
              <staticMap
                :title="details.enterpriseName"
                :title2="details.interviewAddr"
                :latitude="details.latitude"
                :longitude="details.longitude"
              ></staticMap>
            </view>
          </view>
				</uni-row>

        <uni-row class="demo-uni-row" width="730">
					<uni-col :span="6">
						<view class="label"><text>{{ details.inviteRemarkTitle }}</text></view>
					</uni-col>
					<uni-col :span="18">
						<view class="neirong"><text>{{ inviteRemarks }}</text><text class="zhankai" @click="openInviteRemarks" v-if="inviteRemarksTextShow">{{ inviteRemarksText }}</text></view>
					</uni-col>
				</uni-row>

      </view>

      </view>

          <view class="reason" v-if="details.attendState==5">
            <text>企业主动取消，取消原因：{{ details.cancellReason }}</text>
          </view>
          <view class="reason" v-else-if="details.attendState==4">
            <text>我主动取消，取消原因：{{ details.cancellReason }}</text>
          </view>
          <view class="reason" v-else-if="details.attendState==2">
            <text>拒绝原因：{{ details.refuseContent }}</text>
          </view>

          <view class="result" v-if="details.attendState==1||details.attendState==7">
          <view class="mianshi qiandao" v-if="details.interviewType==1">
            <view class="pic">
              <image src="https://image.gxrc.com/gxrcsite/wxMiniApp/2022/interview/<EMAIL>" />
          </view>
          <view class="info">
              <view class="h3">
            <text>面试签到</text>
          </view>
          <view class="p">
            <text>抵达面试场地后，签到通知面试官</text>
          </view>
          </view>
          <view class="btn-wrap">
            <view class="btn btn-yqd" v-if="details.isCheckIn==1">
            <text>已签到</text>
          </view>
            <view class="btn" @click="interviewCheckin" v-else>
            <text>签到</text>
          </view>
          </view>
          </view>

          <view class="mianshi qingkuai">
            <view class="pic">
              <image src="https://image.gxrc.com/gxrcsite/wxMiniApp/2022/interview/<EMAIL>" />
          </view>
            <view class="info">
              <view class="h3">
            <text>面试情况</text>
          </view>
          <view class="p">
            <text>完成面试后可向面试官获取面试结果</text>
          </view>
          </view>
          <view class="btn-wrap">
             <view class="btn btn-yqd" v-if="details.askAnswer==1">
            <text>已询问</text>
          </view>
            <view class="btn" @click="inquiryInterviewAnswer" v-else>
            <text>询问结果</text>
          </view>
          </view>
          </view>

          <view class="cancel" v-if="details.attendState!=7">
            <text>如果您有什么特殊情况，可在这里 </text><text class="quxiao" @click="cancelInterview">取消面试</text>
          </view>
        </view>

        <view class="btn-result-wrap">
          <view class="btn-result-flex" v-if="details.attendState==0">
  <view class="btn-result btn-jjyq" @click="refuseInterview">
            <text>拒绝邀请</text>
          </view>
          <view class="btn-result btn-jsyq" @click="agreeInterview">
            <text>接受邀请</text>
          </view>
          </view>
          <view class="btn-result btn-yjs" v-else>
            <text>{{details.attendStateStr}}</text>
          </view>
          </view>
        </view>
    </view>

    <popupRefuseInvitationCom
    ref="popupRefuseInvitation"
    @submit="RefuseSubmit"
  ></popupRefuseInvitationCom>

  <uni-popup ref="popupcallPhone" type="dialog">
    <uni-popup-dialog
      ref="inputClose"
      cancel-text="取消"
      :before-close="true"
      confirm-text="确定"
      :type="'info'"
      title="提示"
      :content="`联系企业：${details.enteContactMan} ${details.enteContactPhone}`"
      value=""
      @close="closeDialogcallPhone"
      @confirm="dialogcallPhone"
    ></uni-popup-dialog>
  </uni-popup>

  </template>
  
  <script setup lang="ts">
    import { Ref, nextTick, reactive, ref } from 'vue'
    import {
      onLoad,
      onUnload,
      onShow,
      onLaunch,
      onPageScroll,
      onShareAppMessage,
      onReady,
      onHide
    } from '@dcloudio/uni-app'
  import { handlerError } from '@/utils'
  import Topbar from './components/Topbar.vue'
  import { InterviewDetailsModel} from '@/services/im/data-contracts'
import { Im } from '@/services/im/Im'
  import staticMap from '@/components/staticMap/staticMap.vue'
  import popupRefuseInvitationCom from '@/pages/message/components/popupRefuseInvitation.vue'
  import type { UniPopup, popupRefuseInvitationRef } from '@/pages/message/type'
  import { positioningCity, getSetting } from '@/pages/index/hooks/location'
  import { useAppStore } from '@/store/modules/app'
  const store = useAppStore()

  let parameter = $ref({
    "platform": 5,
    "askToken": "",
    "enterId":"",
    "isRefuse":true
  }) 
  let details = $ref({})
  let interviewStr=$ref('面试邀请')
  let inviteRemarksLength=$ref(34)
  let inviteRemarks=$ref('')
  let inviteRemarksText=$ref('展开')
  let inviteRemarksTextShow=$ref(false)
  let location = $ref({
    longitude:0,
    latitude:0
  })
   
    onLoad(async (options) => {
    if (!options.askToken) {
      handlerError('参数错误')
      return
    }
    parameter.askToken = options.askToken

    await getInterviewData()

  })

  const getInterviewData = async () => {
    details = await Im.imInterviewdetails({
      "platform": parameter.platform,
      "askToken": parameter.askToken,
    })
    
    parameter.enterId=details.enterpriseYxId

    if(details.inviteRemarks.length>inviteRemarksLength){
      inviteRemarks=details.inviteRemarks.substr(0,inviteRemarksLength)+'...'
      inviteRemarksTextShow=true
    }else{
      inviteRemarks=details.inviteRemarks
      inviteRemarksTextShow=false
    }
    

  switch(details.attendState) {
    case 0:  
        interviewStr='等待对方接受'
        break;

    case 1:  
        interviewStr='求职者已接受'
        break;

    case 2:  
        interviewStr='求职者已拒绝'
        break;

    case 3:  
        interviewStr='求职者超时未接受'
        break;

     case 4:  
        interviewStr='求职者取消'
        break;

    case 5:  
        interviewStr='企业取消'
        break;

    case 7:  
        interviewStr='已参加面试'
        break;

    case 8:  
        interviewStr='未参加面试'
        break;

    case -1:  
        interviewStr='全部状态'
        break;

    default:
      interviewStr='面试邀请'
}
  }


  const popupcallPhone: Ref<null | UniPopup> = ref(null)
  const openCallPhone = () => {
    popupcallPhone.value?.open()
  }
  const closeDialogcallPhone = () => {
    popupcallPhone.value?.close()
  }
  const dialogcallPhone = () => {
    details.enteContactPhone &&
      uni.makePhoneCall({
        phoneNumber: details.enteContactPhone,
        // 成功回调
        success: (res) => {
          console.log('调用成功!')
          closeDialogcallPhone()
        },
        // 失败回调
        fail: (res) => {
          console.log('调用失败!')
        }
      })
  }

  const interviewCheckin = async () => {
    let { locationResult } = await positioningCity(true)
    let locationTemp = locationResult.location.split(',')
    location.latitude=locationTemp[0]
    location.longitude=locationTemp[1]

    interviewCheckinEvent()
  }

  const interviewCheckinEvent= async () => {
    uni.showLoading({
      title: '加载中'
    })
    let latitude=0,longitude=0
    if (location.longitude != 0) {
      longitude = location.longitude
    }
    if (location.latitude != 0) {
      latitude = location.latitude
    }
    
    Im.imInterviewcheckin({
  "platform": parameter.platform,
  "askToken": parameter.askToken,
  "latitude":latitude,
  "longitude": longitude
}).then((data) => {
        getInterviewData()
        uni.$emit('updateImstate', 'replyStatus')
      })
      .finally(() => {
        uni.hideLoading()
      })
  }
  const agreeInterview = async () => {
    uni.showLoading({
      title: '加载中'
    })
    Im.imAgreeinterview({
      "platform": parameter.platform,
      "enterId":  parameter.enterId || '',
      "askToken": parameter.askToken || ''
    })
      .then((data) => {
        getInterviewData()
        uni.$emit('updateImstate', 'replyStatus')
      })
      .finally(() => {
        uni.hideLoading()
      })
  }
 
  const popupRefuseInvitation: Ref<popupRefuseInvitationRef | null> = ref(null)
  const RefuseSubmit = (id: number) => {
    uni.showLoading({
      title: '加载中'
    })
    if(parameter.isRefuse){
      Im.imRefuseinterview({
      "platform": parameter.platform,
      "enterId": parameter.enterId || '',
      "askToken": parameter.askToken || '',
      "refuseReason": Number(id)
    })
      .then((data) => {
        getInterviewData()
        uni.$emit('updateImstate', 'replyStatus')
      })
      .catch((e) => {
        console.log(e)
      })
      .finally(() => {
        uni.hideLoading()
        popupRefuseInvitation.value?.close()
      })
    }else{
      Im.imCancelinterview({
      "platform": parameter.platform,
      "askToken": parameter.askToken,
      "enterId": parameter.enterId,
      "refuseReason": Number(id),
      }).then((data) => {
        getInterviewData()
        uni.$emit('updateImstate', 'replyStatus')
      })
      .catch((e) => {
        console.log(e)
      })
      .finally(() => {
        uni.hideLoading()
        popupRefuseInvitation.value?.close()
      })
    }
     
  }

  const refuseInterview =() => {
    parameter.isRefuse=true
    popupRefuseInvitation.value?.open()
  }


  const cancelInterview = async () => {
    parameter.isRefuse=false
    popupRefuseInvitation.value?.open()
}


const inquiryInterviewAnswer = async () => {
  uni.showLoading({
      title: '加载中'
    })
await Im.imInquiryinterviewanswer({
"platform": parameter.platform,
"askToken": parameter.askToken,
}).then((data) => {
        getInterviewData()
        uni.$emit('updateImstate', 'replyStatus')
      })
      .catch((e) => {
        console.log(e)
      })
      .finally(() => {
        uni.hideLoading()
      })
}
const openInviteRemarks = () => {
  if(inviteRemarksText=='展开'){
    inviteRemarks=details.inviteRemarks
    inviteRemarksText='收起'
  }else{
    inviteRemarks=details.inviteRemarks.substr(0,inviteRemarksLength)+'...'
    inviteRemarksText='展开'
  }
}

  </script>
  
  <style lang="scss" scoped>
  .clear{clear:both}
  .interview-detail-blue{background-image: url(https://image.gxrc.com/gxrcsite/wxMiniApp/2022/interview/yyms_bg.png);}
  .interview-detail-gray{background-image: url(https://image.gxrc.com/gxrcsite/wxMiniApp/2022/interview/msyq_n_bg.png);}
    .interview-detail {min-height: 100vh;
background-color: #F8F8F8;background-repeat:repeat-x;
      .head-title {
        font-weight: bold;
color: #FFFFFF;
line-height: 60rpx;
font-size: 32rpx;padding:0 0 30rpx 20rpx;
      }
      .content{padding-bottom: 160rpx;}
      .interview-info-bg{background: #FFFFFF;margin: 0 20rpx;padding: 30rpx;
border-radius: 20rpx;
      .ent-info {display: flex;
        .pic{flex:1;
            image{width:96rpx;height:96rpx;border-radius: 10rpx;padding:6rpx;
border: 2rpx solid #EEEEEE;background: #fff;}
}
        .info{flex:3;
        .ent-name{
font-size: 32rpx;font-weight: bold;padding-bottom: 10rpx;
color: #333333;}
.yaoqing{font-size: 26rpx;
color: #666666;}
        }
      }
      .interview-info{
        ::v-deep .uni-row{
            padding: 30rpx 0;
border-bottom: 2rpx solid #EEEEEE;
line-height: 40rpx;
        .label{
font-size: 26rpx;
color: #666666;}
.neirong{
font-size: 26rpx;
color: #333;
}
.zhankai{color: #457CCF;padding-left: 10rpx;}
.shouji-wrap{position: relative;
  .shouji-border{width:70rpx;height:70rpx;line-height:70rpx;border: 2rpx solid #EFEFEF;border-radius: 50%;text-align:center;position: absolute;top:-17rpx;right:0;
    .iconfont{font-size:40rpx;color: #457CCF;}
  }
}
        }
        ::v-deep .uni-row:last-child{border-bottom: 0;}
        ::v-deep .uni-col-3{text-align: right;}
      }
.map{padding-top: 30rpx;
  .address {
        width:100%;
        height: 230rpx;
        position: relative;
        // font-size: 28rpx;
        // color: #ffffff;
      }
}
    }
.reason{text-align: center;padding: 30rpx;
font-size: 26rpx;
color: #666;}

.result{
  .mianshi{display: flex;background: #fff;margin:30rpx 20rpx 0;padding: 20rpx;
border-radius: 20rpx;
.pic{flex:1;
  image{width:86rpx;height:86rpx;}
}
.info{flex:4;
.h3{padding-bottom:8rpx;
color: #333333;
font-size: 32rpx;font-weight: bold;}
.p{color: #999;
font-size: 24rpx;}}
.btn-wrap{flex:1;padding-top: 8rpx;
  .btn{
    width: 152rpx;
height: 56rpx;
line-height: 56rpx;
font-size: 26rpx;
background: #FFFFFF;
border-radius: 28rpx;
border: 2rpx solid #457CCF;text-align: center;
color: #457CCF;
  }
  .btn-yqd{border: 2rpx solid #EAF1FF;
background: #EAF1FF;
color: #457CCF;}
}
  }
  .cancel{text-align: center;padding-top: 30rpx;
font-size: 24rpx;
color: #666;
.quxiao{
  color: #457CCF;
}
}

}

.btn-result-wrap{width: 100%;position: fixed;left: 0;bottom: 0;
background: #FFFFFF;padding:20rpx 20rpx 30rpx;box-sizing:border-box;
.btn-result{
  height: 88rpx;line-height: 88rpx;text-align: center;
background: #E9ECF3;
color: #999999;
font-size: 32rpx;
border-radius: 10rpx;
}
.btn-yjs{}
.btn-result-flex{
  display:flex ;
}
.btn-jjyq{flex:1;}
.btn-jsyq{flex:2;margin-left: 20rpx;
background: #457CCF;color: #fff;}
}
    }
  </style>
  