<template>
  <view class="interviewContent">
    <view class="header">
      <HorizontalSelectTab
        v-model="active"
        :option="option"
        :flex="true"
        @change="change"
      ></HorizontalSelectTab>
    </view>
    <scrollCom
      v-if="list"
      ref="scrollRef"
      :refresher-enabled="true"
      :refresher-threshold="35"
      :height="`calc(100vh - 100rpx)`"
      @scrolltolowers="scrolltolower"
      @refresherrefreshs="refresherrefresh"
    >
      <view v-for="item in list" :key="item.askToken" class="record-item" :class="item.attendStateStr=='已过期'?'record-item-ygq':''">
        <navigator :url="`/pages/interview/detail?askToken=${item.askToken}`" class="search-navigator" hover-class="none">
        <view class="title">
          <view class="title-left">
            <text
              >{{ item.interviewTime }}</text>
          </view>
          <view class="title-right" :class="item.attendStateStr=='待接受'?'djs':item.attendStateStr=='已拒绝'||item.attendStateStr=='已取消'?'yjj':''">
            <text
              >{{ item.attendStateStr }}</text>
          </view>
        </view>
        <view
          class="content">
          <view class="row-left">
            <image :src="item.imageUrl" />
          </view>

          <view class="row-right">
          <view class="row-top">
            <view class="ent-name">
            <text>{{ item.enterpriseName }}</text>
          </view>
          <view class="interview-type">
            <text>{{ item.interviewType }}</text>
          </view>
          </view>
          <view class="row-bottom">
            <view class="position-salary">
            <text>{{ item.positionAndSalary }}</text>
          </view>
            <view class="interview-time">
            <text>{{ item.interviewInvitationTime }}</text>
          </view>
          </view>
        </view>
        </view>
      </navigator>
      </view>
    </scrollCom>
  </view>
</template>

<script setup lang="ts">
  import HorizontalSelectTab from '@/pages/message/components/HorizontalSelectTab.vue'
  import { Ref, nextTick, reactive, ref } from 'vue'
  import {
    onLoad,
    onUnload,
    onShow,
    onLaunch,
    onPageScroll,
    onShareAppMessage,
    onReady,
    onHide
  } from '@dcloudio/uni-app'
  import scrollCom from '@/pages/message/components/scrollCom.vue'
  import type { ScrollView } from '@/pages/message/type'
  import { InterviewRecordItemModel, MyInterviewRecord } from '@/services/my/data-contracts'
import { My } from '@/services/my/My'
  const active = ref(0)
  const option = reactive([
    {
      id: 0,
      item: '全部',
      isDot: false,
      number: 0
    },
    {
      id: 1,
      item: '待接受',
      isDot: false,
      number: 0
    },
    {
      id: 2,
      item: '已接受',
      isDot: false,
      number: 0
    },
    {
      id: 3,
      item: '已拒绝',
      isDot: false,
      number: 0
    }
  ])
  onLoad(async (options) => {
    await initList()
  })
  const scrollRef: Ref<ScrollView | null> = ref(null)
  const list: Ref<InterviewRecordItemModel[]> = ref([])
  const post = reactive({
    page: 1,
    pagesize: 20,
    state: 0
  })
  const change = (data:any) => {
    post.state=data.id
    initList()
  }
  const scrolltolower = async () => {
    const page = post.page + 1
    let data = await getList({ page: page, pagesize: post.pagesize, state: post.state })
    if (data.items && data.items.length > 0) {
      list.value.push(...data.items)
      post.page = page
      scrollRef.value?.AddCallBack(true)
    } else {
      scrollRef.value?.AddCallBack(false)
    }
  }
  const refresherrefresh = async () => {
    scrollRef.value?.hide(false)
    post.state = active.value
    post.page = 1
    post.pagesize =20
    await initList()
    nextTick(() => {
      scrollRef.value?.closeRefresh()
      scrollRef.value?.hide(true)
    })
  }
  async function initList() {
    //初始化列表
    scrollRef.value?.loading()
    let data = await getList(post)
    list.value = data.items || []

    if (data.items && data.items.length < post.pagesize) {
      scrollRef.value?.AddCallBack(false)
    } else {
      scrollRef.value?.AddCallBack(true)
    }
  }
  const getList = (obj: MyInterviewRecord) => {
    return My.myInterviewrecord(obj)
  }
</script>

<style lang="scss" scoped>
  .interviewContent {
    background: #f7f9fc;
    .header {
      border-bottom: 2rpx solid #f2f2f2;
    }
    .record-item {
      margin: 24rpx 30rpx;
      background: #fff;
      box-sizing: border-box;
border-radius: 8rpx;
      .title{display: flex;
line-height: 44rpx;
      padding: 20rpx 32rpx;
border: 2rpx solid #F7F7F7;
        .title-left{flex:3;
font-size: 28rpx;}
        .title-right{flex:1;font-size: 28rpx;text-align:right;
color: #457CCF;}
.yjs{
color: #457CCF;}
.djs{color: #EBB55A;}
.yjj{color: #BBBBBB;}
      }
      .content{display: flex;line-height: 44rpx;
      padding: 30rpx 32rpx;
        .row-left{flex:1;
box-sizing:border-box;
          image{width: 88rpx;height:88rpx;border-radius: 10rpx;padding:6rpx;
border: 2rpx solid #EEEEEE;}
        }
        .row-right{flex:4;
          .row-top,.row-bottom{display: flex;}
          .ent-name,.position-salary{flex:3;}
          .interview-type,.interview-time{flex:1;}
          .row-top{
            .ent-name{
color: #333333;font-size: 30rpx;padding-bottom: 4rpx;}
.interview-type{
color: #333333;font-size: 24rpx;padding-bottom: 4rpx;text-align:right;}
          }
          .row-bottom{
            .position-salary{
color: #999;font-size: 24rpx;}
.interview-time{
color: #999;font-size: 24rpx;text-align:right;}
          }
        }
      
      }
    }
    .record-item-ygq{color: #BBBBBB;
      .title{
        .title-left{color: #BBBBBB;}
        .title-right{color: #BBBBBB;}
      }
      .content{
        .row-right{
          .row-top{
            .ent-name{color: #BBBBBB;}
.interview-type{color: #BBBBBB;}
          }
          .row-bottom{
            .position-salary{color: #BBBBBB;}
.interview-time{color: #BBBBBB;}
          }
        }
      
      }
    }
  }
</style>
