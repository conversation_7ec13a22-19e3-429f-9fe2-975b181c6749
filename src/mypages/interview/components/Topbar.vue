<template>
  <!--  #ifdef MP-WEIXIN -->
  <view
    class="topbar"
    :style="`padding-top:${headerPadTop * 2}rpx;`"
  >
    <view class="goback" @click="goback()">
      <view class="iconfont icon-arrowLeft14"></view>
    </view>
  </view>
    <!--  #endif -->
</template>
<script lang="ts" setup>
  import { onLoad, onUnload } from '@dcloudio/uni-app'
  import { onMounted } from 'vue'

  let state = $ref('')
  let headerPadTop = $ref(0)

  onLoad((options) => {
    headerPadTop = uni.getSystemInfoSync().statusBarHeight
  })
  onMounted(() => {
    headerPadTop = uni.getSystemInfoSync().statusBarHeight
  })
 
  const goback = () => {
    uni.navigateBack()
  }
</script>
<style lang="scss" scoped>
  .topbar {
    width: 100%;
    height: 178rpx;
    padding: 24px 24rpx 0 0rpx;
    padding-right: 20rpx;
    box-sizing: border-box;
    .goback {color: #fff;
      float: left;
      width: 80rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      .iconfont {
        font-size: 36rpx;
      }
    }
  }
</style>
