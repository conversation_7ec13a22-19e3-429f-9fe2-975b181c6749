# uniapp Vue3 微信小程序启动页面控制系统

## 概述

本系统用于解决当启动页面为pageEnterprise分包下的页面时，避免执行App.vue中的特定方法（如登录状态监听、消息未读状态更新等）的问题。

## 核心功能

### 1. 启动页面检测

系统提供了多种方式来检测当前启动页面是否为pageEnterprise分包页面：

- **通过页面栈检测**: 使用`getCurrentPages()`获取当前页面路由
- **通过启动参数检测**: 分析onLaunch的option参数
- **组合检测**: 优先使用启动参数，备用页面栈检测

### 2. 条件初始化

根据启动页面类型，系统会选择不同的初始化策略：

- **标准初始化**: 适用于主应用页面，包含完整的IM连接、消息状态等
- **企业模块初始化**: 适用于pageEnterprise分包页面，跳过不必要的功能

### 3. 延迟初始化

对于企业模块，提供延迟初始化功能，在用户真正需要时才加载相关功能。

## 文件结构

```
src/
├── utils/
│   ├── appInit.ts              # 主应用初始化和页面检测
│   └── enterpriseInit.ts       # 企业模块专用初始化
├── App.vue                     # 修改后的应用入口
└── pageEnterprise/             # 企业分包
    └── talent/index.vue        # 示例页面（已集成延迟初始化）
```

## 使用方法

### 1. 检测启动页面类型

```typescript
import { shouldSkipAppInitialization, isEnterpriseSubpackagePage } from '@/utils/appInit'

// 在App.vue的onLaunch中使用
const isEnterpriseStartup = shouldSkipAppInitialization(option)

// 直接检测当前页面
const isEnterprisePage = isEnterpriseSubpackagePage()
```

### 2. 条件初始化

```typescript
// App.vue中的实现
if (isEnterpriseStartup) {
  // 使用企业模块专用初始化
  await initEnterpriseApp(option)
} else {
  // 使用标准初始化流程
  // ... 标准初始化代码
}
```

### 3. 延迟初始化功能

```typescript
import { lazyInitEnterpriseFeatures } from '@/utils/enterpriseInit'

// 在企业模块页面中使用
const { initIM, initMessageStatus } = lazyInitEnterpriseFeatures()

// 在需要时调用
const handleNeedIM = () => {
  initIM() // 初始化IM连接
}

const handleNeedMessage = () => {
  initMessageStatus() // 初始化消息状态
}
```

## 配置说明

### 1. 企业分包页面识别

系统通过以下规则识别企业分包页面：

```typescript
// 页面路径以 'pageEnterprise/' 开头
currentRoute.startsWith('pageEnterprise/')
```

### 2. 跳过的初始化功能

当检测到企业模块启动时，以下功能会被跳过：

- IM SDK连接 (`imSdkStore.connect()`)
- 消息未读状态更新 (`unreadStatusStore.updateNoticeUnread()`)
- 标准的登录状态监听
- TabBar徽章更新

### 3. 保留的初始化功能

无论何种启动方式，以下功能都会保留：

- 基础系统信息获取
- 导航栏设置
- 应用更新检查
- 城市切换逻辑
- OpenAI开关获取

## 最佳实践

### 1. 企业模块页面开发

```vue
<script setup>
import { lazyInitEnterpriseFeatures } from '@/utils/enterpriseInit'

// 获取延迟初始化功能
const { initIM, initMessageStatus } = lazyInitEnterpriseFeatures()

// 在用户交互时按需初始化
const handleChatClick = () => {
  initIM() // 只在用户点击聊天时初始化IM
}
</script>
```

### 2. 性能优化

- 企业模块页面启动时跳过不必要的初始化，提升启动速度
- 使用延迟初始化，按需加载功能模块
- 避免在企业模块中执行主应用的状态监听

### 3. 调试和监控

系统提供了详细的日志输出：

```javascript
console.log('检测到pageEnterprise分包页面:', currentRoute)
console.log('企业模块启动，使用专用初始化逻辑')
console.log('用户需要IM功能，开始初始化...')
```

## 注意事项

1. **兼容性**: 确保在不同小程序平台上的兼容性
2. **分包加载**: 考虑分包预加载对启动检测的影响
3. **页面栈**: onLaunch时页面栈可能为空，需要多种检测方式
4. **状态同步**: 延迟初始化的功能需要注意状态同步问题

## 扩展功能

### 1. 添加新的分包检测

```typescript
// 在appInit.ts中扩展
export function isOtherSubpackagePage(): boolean {
  // 检测其他分包页面的逻辑
}
```

### 2. 自定义初始化策略

```typescript
// 在enterpriseInit.ts中添加
export async function initCustomModule(option: any) {
  // 自定义模块的初始化逻辑
}
```

## 测试验证

建议在以下场景进行测试：

1. 直接启动企业模块页面
2. 从主应用跳转到企业模块
3. 企业模块内部页面跳转
4. 不同网络环境下的启动
5. 冷启动和热启动场景
