<template>
  <view class="address">
    <view class="box">
      <view class="info">
        <view class="name">
          <text>{{ title }}</text>
        </view>
        <view class="addr">
          <image
            src="https://image.gxrc.com/gxrcsite/wxMiniApp/2022/<EMAIL>"
            mode="scaleToFill"
            class="img"
          />
          <view class="addrinfo">{{ title2 }}</view>
        </view>
      </view>
      <view v-if="true" class="clickmap" @tap="road">查看路线</view>
    </view>
  </view>
</template>
<script setup lang="ts">
  import { computed, getCurrentInstance } from 'vue'
  import { bdMapToTxMap } from '@/utils/location'

  interface Props {
    title: string
    title2: string
    latitude: string
    longitude: string
  }
  const props = withDefaults(defineProps<Props>(), {
    title: '',
    title2: ''
  })
  getCurrentInstance
  const instance = getCurrentInstance()
  const title = computed(() => props.title)
  const title2 = computed(() => props.title2)

  const road = () => {
    let { lat, lng } = bdMapToTxMap(Number(props.latitude), Number(props.longitude))

    uni.openLocation({
      latitude: lat,
      longitude: lng,
      name: title2.value,
      address: title.value,
      success: function () {
        console.log('success')
      },
      fail: function (err) {
        console.log(err)
      }
    })
    // uni.setClipboardData({
    //   data: title2.value,
    //   success: function (res) {
    //     uni.showToast({
    //       title: '复制成功'
    //     })
    //   }
    // })
  }
</script>
<style lang="scss" scoped>
  .address {
    background: url('https://image.gxrc.com/gxrcsite/wxMiniApp/2022/<EMAIL>');
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    position: relative;
    // font-size: 28rpx;
    // color: #ffffff;
    .box {
      position: absolute;
      background: #fff;
      height: 80%;
      width: 80%;
      top: 40rpx;
      left: 48rpx;
      border-radius: 4px;
      display: flex;
      flex-direction: row;
      transform: translate(-50%, -50%);
      top: 50%;
      left: 50%;
      .clickmap {
        width: 142rpx;
        color: #457ccf;
        font-size: 28rpx;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .info {
        display: flex;
        flex-direction: column;
        color: #333;
        flex: 1;
        overflow: hidden;
        justify-content: center;
        border-right: 1px solid #eeeeee;
        .name {
          padding: 0 32rpx 12rpx 32rpx;
          font-size: 28rpx;
          font-weight: bold;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          width: 100%;
          box-sizing: border-box;
          text {
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }
        }
        .addr {
          padding: 0 32rpx;
          display: flex;
          align-items: center;

          .img {
            width: 28rpx;
            height: 28rpx;
            margin-right: 2px;
            flex-shrink: 0;
          }
          .addrinfo {
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            font-size: 24rpx;
            color: #999999;
          }
        }
      }
    }
  }
</style>
