<template>
  <view class="gxrc-inputonly">
    <!--  #ifdef  MP-WEIXIN || MP-TOUTIAO -->
    <input
      :value="value"
      :placeholder="placeholder"
      placeholder-class="input-placeholder"
      :disabled="true"
      class="input"
    />
    <!--  #endif -->
    <!--  #ifdef MP-ALIPAY -->
    <view :class="['input',value? '' :'input-placeholder']" v-html="value || placeholder"></view>
    <!--  #endif -->
    <i class="iconfont icon-arrowRight14 icon"></i>
  </view>
</template>
<script lang="ts" setup>
  import { Ref } from 'vue';


  interface Prop {
    value?: Ref<string | number | boolean>
    placeholder?: string 
  }

  const { value, placeholder } = defineProps<Prop>()
</script>

<style lang="scss">
  .gxrc-inputonly {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 36px;
    /*  #ifdef MP-ALIPAY */
    color: #323233;
    /*  #endif  */
    
    .input {
      width: 80%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-size: 15px;
      /*  #ifdef MP-ALIPAY */
      color: #323233;
      /*  #endif  */
    }
    .input-placeholder {
      // color: #bbbbbb;
      // font-size: 14px;
      color: #999;
      font-size: 15px;
      font-weight: 200;
    }
    .icon {
      font-size: 12px;
      color: $uni-text-color-placeholder;
    }
  }
</style>
