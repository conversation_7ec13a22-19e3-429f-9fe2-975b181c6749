<template>
  <view class="share-btn fenxiang" @click="openShare">
    <i class="icon iconfont icon-fenxiang"></i>
  </view>
  <uni-popup ref="shareBox" background-color="#fff" type="bottom">
    <view class="pop-box">
      <view class="title">分享到</view>
      <view class="btn">
        <button open-type="share" plain class="btn-share weixin">
          <image class="img" src="https://image.gxrc.com/gxrcsite/wxMiniApp/2022/static/resume/<EMAIL>"></image>
          <view class="txt">微信</view>
        </button>
        <button plain class="btn-share haibao" @click="sharePoster">
          <image class="img" src="https://image.gxrc.com/gxrcsite/wxMiniApp/2022/static/resume/<EMAIL>"></image>
          <view class="txt">分享海报</view>
        </button>
        <button v-if="copyTxt" plain class="btn-share haibao" @click="copyDoc">
          <image class="img" src="https://image.gxrc.com/gxrcsite/wxMiniApp/2022/static/resume/<EMAIL>"></image>
          <view class="txt">复制文本</view>
        </button>
      </view>
    </view>
  </uni-popup>
</template>
<script lang="ts" setup>
  import { watch } from 'vue'
  const emit = defineEmits<{
    (e: 'showPosterPop'): void
    (e: 'copyDoc'): void
  }>()
  interface dataList {
    copyTxt?: boolean,
    close?:boolean
  }
  const { copyTxt = false,close=false } = defineProps<dataList>()
  const shareBox = $ref<any>(null)
  const openShare = () => {
    shareBox.open()
  }
  const sharePoster = () => {
    shareBox.close()
    emit('showPosterPop')
  }
   const copyDoc = () => {
    shareBox.close()
    emit('copyDoc')
  }
  
  watch(()=>close,(newval)=>{
    if(newval){
      shareBox.close()
    }
    
  })
</script>
<style lang="scss" scope>
  .pop-box {
    .title {
      color: #333333;
      font-size: 32rpx;
      font-weight: bold;
      text-align: center;
      padding: 32rpx 0;
    }
    .btn {
      display: flex;
      padding: 0 50rpx 50rpx 50rpx;
    }
    .img {
      width: 96rpx;
      height: 96rpx;
    }
    .txt {
      font-size: 28rpx;
      color: #666666;
    }
    .btn-share {
      border: none;
      line-height: 48rpx;
    }
 }

</style>
