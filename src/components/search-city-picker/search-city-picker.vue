<template>
  <view class="city-picker clearfix">
    <view class="picker-input clearfix" @tap="selecity()">
      <text :class="textAlign">{{
        cityName ? (cityName == '广西壮族自治区' ? '全广西' : cityName) : '请选择'
      }}</text>
      <i class="iconfont icon-arrowDown5"></i>
      <view class="vl">|</view>
    </view>
    <uni-popup ref="showRight" type="right" :is-mask-click="true" class="pop-city-box">
      <uni-nav-bar
        title=""
        left-icon="left"
        :fixed="true"
        :border="false"
        :status-bar="true"
        @clickLeft="closeDrawer"
      />
      <view class="sel-main clearfix" :style="{ width: scrollwidth + 'px' }">
        <!-- 第一组 -->
        <view class="sel-city-box sel-city-boxA">
          <scroll-view
            :style="{ height: scrollHeight + 'px' }"
            scroll-y="true"
            :show-scrollbar="false"
          >
            <view class="ul">
              <view
                v-for="(item, index) in firstArr"
                :key="index"
                clickable
                :class="['li clearfix', { on: item.keywordID == isIDA, isseled: item.selected }]"
                @click="cityChooseA(item)"
              >
                <view class="sdl">{{ item.text }}</view>
                <view class="sdr">
                  <i v-if="item.hasNext" class="iconfont icon-arrowRight14"></i>
                  <i v-if="item.selected && !item.hasNext" class="hook"
                    ><uni-icons type="checkmarkempty" color="#5e8df5" size="16"></uni-icons
                  ></i>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
        <!-- 第二组 -->
        <view class="sel-city-box sel-city-boxB">
          <view class="city-boxB">
            <view class="position clearfix">
              <text class="tt">当前定位</text>
              <view class="item-city">
                <i class="iconfont icon-position1"></i>
                <text> {{ positionCity.keywordName }}</text>
              </view>
            </view>
            <scroll-view
              :style="{ height: scrollHeight + 'px', background: '#F4F5F9' }"
              scroll-y="true"
            >
              <view class="ul">
                <view
                  v-for="(item, index) in secondArr"
                  :key="index"
                  clickable
                  :class="['li clearfix', { on: item.keywordID == isIDB, isseled: item.selected }]"
                  @click="cityChooseB(item)"
                  >{{ index == 0 && !noguangxi ? '全' + item.text : item.text }}
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script lang="ts" setup>
  import { computed } from 'vue'
  import { Options } from '@/services/my/Options'
  import { onLoad } from '@dcloudio/uni-app'
  import { positioningCity } from '@/pages/index/hooks/location'
  import { useAppStore } from '@/store/modules/app'
  const store = useAppStore()
  interface dataList {
    hideValue: []
    textAlign?: string
    maxCount?: number
    cityName?: string
    hasarrow?: boolean
    noguangxi?: boolean
  }
  let allCity = $ref<Array[] | undefined | null>([])
  let firstArr = $ref<Array[] | undefined | null>([])
  let secondArr = $ref<Array[] | undefined | null>([])
  let activeGroup = $ref<Array[]>([])
  let isIDA = $ref<any>(-1) //第一排高亮
  let isIDB = $ref<any>(-1) //第二排高亮
  let messageText = $ref<string>() //信息弹窗内容
  let positionCity = $ref({ keywordID: 0, keywordName: '南宁' })

  // const props = defineProps<dataList>()

  // #ifdef  MP-WEIXIN
  const {
    hideValue,
    maxCount = 1,
    textAlign = 'left',
    cityName = '请选择',
    hasarrow = true,
    noguangxi = false
  } = defineProps<dataList>()
  // #endif

  // #ifdef MP-TOUTIAO || MP-ALIPAY
  const props = defineProps<dataList>()

  const {
    hideValue,
    maxCount = 1,
    textAlign = 'left',
    hasarrow = true,
    noguangxi = false
  } = props
  const cityName = computed(() => (props.cityName ? props.cityName : '请选择'))
  // #endif
  let navTop = uni.getMenuButtonBoundingClientRect().top
  const scrollHeight = computed(() => {
    return uni.getSystemInfoSync().windowHeight - 40 - navTop
  })
  const scrollwidth = computed(() => {
    return uni.getSystemInfoSync().windowWidth || 320
  })
  const showRight = $ref<any>(null)
  const emit = defineEmits<{
    (e: 'change'): void

    (e: 'open'): void
    (e: 'close'): void
  }>()
  const selectedCount = computed(() => {
    return activeGroup.length
  })
  const canSelectMore = computed(() => {
    return maxCount <= selectedCount.value
  })
  const singleMode = computed(() => {
    return maxCount === 1
  })
  onLoad(async () => {
    // console.log('监听页面加载-zhi')
  })
  const getDate = async () => {
    let loca = store.positionCity
    if (loca.name) {
      positionCity = { keywordID: loca.id, keywordName: loca.name }
    } else {
      // 获取定位
      const { locationResult } = await positioningCity(false)
      positionCity = { keywordID: locationResult.id, keywordName: locationResult.name }
    }
    activeGroup = []
    let Data = {}
    const citys = uni.getStorageSync('cityData')
    if (citys.length > 0) {
      Data = citys
    } else {
      Data = await Options.optionsDistrictList()
      uni.setStorageSync('cityData', Data)
    }
    allCity = Data.map((item: any, index: any) => {
      item.text = item.keywordName
      item.selected = false
      item.index = index
      item.grandfather = '000'
      if (item.keywordID == 1) item.text = '广西'
      return item
    })
    Getgrand()

    hideValue.forEach((id: any) => {
      let item = allCity.find((p: any) => p.keywordID == id)
      if (!item) return
      item.selected = true
      addCity(item)
    })
    firstArr = allCity.filter((i: any) => i.parentID == -1)
    cityChooseA(firstArr[0])
  }
  const Getgrand = () => {
    let brr = []
    allCity.forEach((i: any) => {
      //  找第一级
      if (i.parentID === -1 && i.hasNext) {
        // 找到第二级
        const arr = allCity.filter((j: any) => j.parentID == i.keywordID && j.hasNext)
        if (arr.length > 0) {
          arr.forEach((a: any) => {
            // 找到第三级
            brr = allCity.filter((j: any) => j.parentID == a.keywordID)
            brr.forEach((k: any) => {
              k.grandfather = i.keywordID
            })
          })
        }
      }
    })
  }
  const selecity = () => {
    showRight.open()
    emit('open')
    getDate()
  }
  const bindComfirm = () => {
    if (activeGroup.length < 1) {
      uni.showToast({
        title: '请至少选择一个',
        duration: 2000,
        icon: 'none'
      })
      return
    }
    emit('change', activeGroup)
    showRight.close()
    emit('close')
  }
  const closeDrawer = () => {
    showRight.close()
    emit('close')
  }
  const getChildren = (pid: any, num: any) => {
    if (noguangxi) {
      //排除全广西
      return allCity.filter((i: any) => i.parentID == pid)
    } else {
      return allCity.filter((i: any) => i.parentID == pid || i.keywordID == pid)
    }
  }
  const cityChooseA = (city: any) => {
    //第一行点击
    if (isIDA == city.keywordID && city.hasNext) {
      return false
    }
    if (isIDA == city.keywordID && !city.hasNext) {
      onSelectCity(city)
      return false
    }
    isIDA = city.keywordID
    isIDB = -1
    secondArr = []
    if (!city.hasNext) {
      //如果没有子集直接选取
      onSelectCity(city)
      return false
    }
    secondArr = getChildren(city.keywordID, 1)
  }
  const cityChooseB = (item: any) => {
    //第二行点击
    onSelectCity(item)
  }
  const onSingleMode = (city: any) => {
    //单选模式
    allCity.forEach((item: any) => {
      item.selected = false
    })
    secondArr.forEach((item: any) => {
      item.selected = false
    })
    firstArr.forEach((item: any) => {
      item.selected = false
    })
    city.selected = true
    activeGroup = [city]
    bindComfirm()
  }
  const onMultipleMode = (city: any) => {
    //多选模式
    const isChecked = city.selected
    if (isChecked) {
      //被选过了
      deleteCity(city)
      return false
    }
    //如果没有被选过检测下面的子集和往上的父级
    if (!isChecked) {
      activeGroup.forEach((i: any) => {
        if (
          i.parentID == city.keywordID ||
          i.keywordID == city.parentID ||
          i.grandfather == city.keywordID ||
          i.keywordID == city.grandfather
        ) {
          deleteCity(i)
        }
      })
      if (canSelectMore.value) {
        //如果没选过而且超限了
        messageText = `最多只能选择${maxCount}个选项`
        uni.showToast({
          title: messageText,
          duration: 2000,
          icon: 'none'
        })
      } else {
        addCity(city)
      }
    }
  }
  const addCity = (city: any) => {
    city.selected = true
    activeGroup.push(city)
  }
  const onSelectCity = (city: any) => {
    if (!city) return
    if (singleMode.value) {
      onSingleMode(city)
    } else {
      onMultipleMode(city)
    }
  }
  const deleteCity = (item: any) => {
    item.selected = false
    secondArr.forEach((i: any) => {
      i.keywordID == item.keywordID ? (i.selected = false) : ''
    })
    activeGroup = activeGroup.filter((i: any) => i.keywordID !== item.keywordID)
  }
</script>

<style lang="scss" scoped>
  $blue: #5e8df5;
  .city-picker {
    .picker-input {
      width: 160rpx;
      box-sizing: border-box;
      min-height: 72rpx;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      position: relative;
      .left {
        float: left;
        width: 84rpx;
        text-align: center;
        overflow: hidden;
        height: 80rpx;
      }
      .vl {
        color: #d9d9d9;
        font-size: 24rpx;
        padding-right: 16rpx;
        float: right;
        padding-right: 15rpx;
      }
      .icon-arrowDown5 {
        color: #d9d9d9;
        font-size: 24rpx;
        float: left;
        padding-left: 10rpx;
      }
    }
    .icon {
      position: absolute;
      right: 0;
      font-size: 24rpx;
      color: #808080;
    }
  }
  .pop-city-box {
    width: 100%;
    z-index: 588;
    .btn-box {
      background: #fff;
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      border-top: 1px solid #eee;
      .login-wrap-button {
        border: none;
        background: #5e8df5;
        color: #fff;
        margin: 20rpx;
        height: 80rpx;
        line-height: 80rpx;
        font-size: 32rpx;
      }
    }
  }
  .selected-wrap {
    padding: 0px 30rpx;
    min-height: 110rpx;
    background: #fff;
    .count {
      font-size: 24rpx;
      color: #666;
      line-height: 44rpx;
    }
    .blue {
      color: #228efd;
    }
  }
  .selected-items {
    padding: 4rpx 0 20rpx 0;
    .item {
      color: #228efd;
      font-size: 24rpx;
      padding: 0px 20rpx;
      float: left;
      display: flex;
      border: 1px solid #228efd;
      border-radius: 6rpx;
      line-height: 44rpx;
      margin: 10rpx 10rpx 0rpx 0rpx;
    }
  }
  .sel-main {
    .sel-city-box {
      float: left;
      .isseled {
        color: $blue;
      }
    }
    .sel-city-boxA {
      width: 180rpx;
      margin-right: -180rpx;
      background: #ebedf6;
      position: relative;
      .li {
        padding: 0px 20rpx;
        color: #666;
        line-height: 90rpx;
        height: 90rpx;
        .sdl {
          float: left;
          font-size: 28rpx;
        }
        .sdr {
          float: right;
          .iconfont {
            font-size: 22rpx;
            line-height: 90rpx;
            height: 90rpx;
          }
        }
      }
      .on {
        background: #f4f5f9;
      }
    }
    .sel-city-boxB {
      width: 100%;
      background: #f4f5f9;
      .city-boxB {
        margin-left: 200rpx;
      }
      .li {
        float: left;
        width: 140rpx;
        padding: 0 10rpx;
        border: 1px solid #eee;
        border-radius: 8rpx;
        text-align: center;
        margin: 0 20rpx 20rpx 0;
      }
      .on {
        background: #fff;
      }
      .position {
        padding: 30rpx 0 50rpx 0;
        .tt {
          color: #333;
          float: left;
          line-height: 40rpx;
          font-size: 28rpx;
        }
        .icon-position1 {
          font-size: 24rpx;
        }
        .item-city {
          padding: 0 10rpx;
          border: 1px solid $blue;
          border-radius: 4rpx;
          text-align: center;
          margin: 0 20rpx 0rpx 30rpx;
          color: $blue;
          font-size: 24rpx;
          float: left;
          display: flex;
          line-height: 40rpx;
        }
      }
    }
  }
</style>
