<template>
  <view class="gxrc-picker">
    <view class="picker-input" @tap="bindTap">
      <text v-if="datavalue" class="pd">{{ datavalue }}</text>
      <text v-else class="placehold pd">{{ placeholder }}</text>
      <i v-if="showArrow" class="iconfont icon-arrowRight14 icon"></i>
    </view>
    <uni-popup ref="datePopup" type="bottom" background-color="#fff">
      <view class="picker-title">
        <text class="cancel font" @tap="bindCancel">取消</text>
        <text class="title">{{ title }}</text>
        <text class="confirm font" @tap="bindComfirm">确认</text>
      </view>

      <picker-view
        class="picker-view"
        :value="selectValue"
        :immediate-change="immediatechange"
        @change="bindChange"
      >
        <picker-view-column>
          <view v-for="(item, index) in years" :key="index" class="item">
            {{ item }}
          </view>
        </picker-view-column>
        <picker-view-column>
          <template v-if="showMonthAndDay">
            <view v-for="(item, index) in months" :key="index" class="item"> {{ item }}月 </view>
          </template>
        </picker-view-column>
      </picker-view>
    </uni-popup>
  </view>
</template>
<script lang="ts" setup>
  import { watch, nextTick, onMounted } from 'vue'

  interface Props {
    modelValue?: string | Date

    title?: string

    placeholder?: string

    showArrow?: boolean

    lastSelectDate?: string
  }

  const {
    modelValue,
    placeholder,
    title = '请选择',
    showArrow = true,
    lastSelectDate = ''
  } = defineProps<Props>()

  const emit = defineEmits<{
    (e: 'update:modelValue', value: number | string | boolean): void
  }>()
  
  // #ifdef MP-WEIXIN || MP-ALIPAY
  let immediatechange = true
  // #endif
  // #ifdef MP-TOUTIAO
  let immediatechange = false
  // #endif

  let datavalue = $ref<number | string | boolean | null>()

  let years = $ref<Array<string>>([])

  let months = $ref<number[]>([])

  let selectValue = $ref<Array<number | string>>()

  let changeValue = $ref<string>()

  let showMonthAndDay = $ref<boolean>(false)

  const datePopup = $ref<any>(null)
  const date = new Date()

  const year = date.getFullYear()

  const month = date.getMonth() + 1

  for (let i = 1; i <= 12; i++) {
    months.push(i)
  }

  const initYear = () => {
    years = []
    const lastDate = lastSelectDate ? new Date(lastSelectDate) : new Date()
    const lastYear = lastDate.getFullYear()
    for (let i = lastYear; i <= year; i++) {
      years.push(i + '年')
    }
    years.push('至今')
  }
  const initSelect = (value: any) => {
    nextTick(() => {
      if (value && value != '至今') {
        const date = new Date(value)
        const y = date.getFullYear() + '年'
        const m = date.getMonth() + 1
        const select = years.indexOf(y)
        selectValue = [select == -1 ? 0 : select, months.indexOf(m)]
        const yy = years[(selectValue as any)[0]].replace('年', '')
        const ms = months[(selectValue as any)[1]]
        const mm = ms < 10 ? `0${ms}` : ms
        changeValue = `${yy}-${mm}`
      } else {
        selectValue = [years.length - 1]
        changeValue = years[years.length - 1]
      }

      datavalue = value
    })
  }

  initYear()
  initSelect(modelValue)

  watch(
    () => modelValue,
    (newval) => {
      if (newval != '至今') {
        showMonthAndDay = true
      }
      initSelect(newval)
    }
  )

  watch(
    () => lastSelectDate,
    (newval) => {
      initYear()
      setTimeout(function () {
        initSelect(modelValue)
      }, 10)
    }
  )

  const bindTap = () => {
    datePopup.open()
  }

  const bindCancel = () => {
    datePopup.close()
  }

  const bindChange = (e: any) => {
    let value = e.detail.value
    value = value.map((item) => {
      return item || 0
    })
    const y = years[value[0]].replace('年', '')
    if (y.includes('至今')) {
      showMonthAndDay = false
      changeValue = y
    } else {
      showMonthAndDay = true
      if (y == year.toString()) {
        months = months.slice(0, month)
      } else {
        months = Array(12)
          .fill(0)
          .map((item, index) => index + 1)
      }
      const m = months[value[1]]
      const mm = m < 10 ? `0${m}` : m
      changeValue = `${y}-${mm}`
    }
  }

  const bindComfirm = () => {
    if (lastSelectDate) {
      const date = new Date(lastSelectDate)
      const current = new Date(changeValue)
      if (current < date) {
        uni.showToast({
          title: '结束时间不能小于开始时间',
          icon: 'none'
        })
        datePopup.close()
        return
      }
    }
    datavalue = changeValue
    emit('update:modelValue', datavalue)
    datePopup.close()
  }
</script>
<style lang="scss">
  .gxrc-picker {
    .picker-title {
      display: flex;
      // flex-direction: row;
      justify-content: space-between;
      margin-top: 15px;
    }

    .picker-input {
      flex: 1;
      width: 100%;
      display: flex;
      box-sizing: border-box;
      min-height: 36px;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .icon {
        font-size: 12px;
        color: $uni-text-color-placeholder;
      }
      .placehold {
        // color: #bbbbbb;
        color: #999;
        font-weight: 200;
      }
      .pd {
        padding-right: 8px;
        font-size: 15px;
      }
    }

    .font {
      // font-size: $uni-font-size-sm;
      color: $uni-color-primary;
    }

    .cancel {
      color: #666666;
      padding: 15px;
    }
    .confirm {
      padding: 15px;
    }
    .title {
      padding-top: 15px;
    }

    .picker-view {
      width: 750rpx;
      min-height: 400rpx;
      margin-top: 15rpx;

      .item {
        height: 50px;
        line-height: 33px;
        align-items: center;
        justify-content: center;
        text-align: center;
        // font-size: 36rpx;
      }
    }
  }
</style>
