<template>
  <view :class="areaSelectedCount > 0 ? 'place bg-blue' : 'place bg'" @click="openDrawer">
    {{ areaSelectedCount > 0 ? place + '*' : '地点' }}
    <span v-show="areaSelectedCount > 0" class="span">{{ areaSelectedCount || '' }}</span>
    <i class="iconfont icon-arrowDown5"></i>
  </view>
  <view @tap="testABC"></view>
  <uni-drawer ref="showDrawer" mode="right" :mask-click="true" :width="windowWidth">
    <view class="screen-area bg-white">
      <uni-nav-bar
        :title="cityname"
        height="40px"
        :fixed="true"
        left-icon="closeempty"
        :border="false"
        :status-bar="true"
        @clickLeft="closeDrawer"
      />
      <div class="con-wrap" :style="`height:${scrollHeight}px`">
        <div class="con">
          <div class="con-l">
            <ul class="ul">
              <li
                v-for="(item, index) in areaData"
                :key="index"
                :class="isActive == item.type ? 'current li' : 'li'"
                @click="tab(item.type)"
              >
                {{ item.title }}
              </li>
            </ul>
          </div>
          <!-- 商圈 -->
          <div v-if="isActive == 'a'" class="con-r con-r-s">
            <div class="sdl">
              <!-- 第一层 -->
              <scroll-view
                :style="{ height: scrollHeight + 'px' }"
                scroll-y="true"
                :show-scrollbar="false"
              >
                <ul class="area clearfix ul">
                  <li
                    v-for="(item, index) in arealistE"
                    :key="index"
                    :class="['li', { on: item.on }, { selected: item.selected }]"
                   
                    @click="areaLiFirstClick(item)"
                  >
                    {{ item.keywordName
                    }}<i v-if="item.selNum" class="van-icon">{{ item.selNum }}</i>
                  </li>
                </ul>
              </scroll-view>
            </div>
            <!-- 商圈第二层数据 -->
            <div class="sdr">
                <scroll-view
                :style="{ height: scrollHeight + 'px' }"
                scroll-y="true"
                :show-scrollbar="false"
              >
                <ul class="area clearfix ul">
                  <li
                    v-for="(item, index) in businessListchild"
                    :key="index"
                    :class="item.selected ? 'selected li' : 'li'"
                    @click="areaLiClick(item)"
                  >
                    {{ item.name }}
                    <i class="van-icon"
                  ><uni-icons type="checkmarkempty" color="#ffffff" size="16"></uni-icons
                ></i>
                  </li>
                </ul>
              </scroll-view>
            </div>
          </div>
          <!-- 地铁 -->
          <div v-if="isActive == 'm'" class="con-r con-r-metro">
            <ul class="metro clearfix ul" :style="`height:${scrollHeight}px`">
              <li
                v-for="(item, index) in metroList"
                :key="index"
                :class="item.selected ? 'selected li' : 'li'"
                @click="metroLiClick(item.stops, item)"
              >
                {{ item.trafficLine }}
              </li>
            </ul>
            <scroll-view
              :style="{ height: scrollHeight + 'px' }"
              scroll-y="true"
              :show-scrollbar="false"
              class="stops ul"
            >
              <ul ref="stops" class="clearfix">
                <li
                  v-for="(item, index) in stopsList"
                  :key="index"
                  :class="item.selected ? 'li selected' : 'li'"
                  @click="stopsLiClick(item)"
                >
                  {{ item.stopName
                  }}<i class="van-icon"
                    ><uni-icons type="checkmarkempty" color="#ffffff" size="16"></uni-icons
                  ></i>
                </li>
              </ul>
            </scroll-view>
          </div>
          <!-- 附近 -->
          <div v-if="isActive == 'f'" class="con-r">
            <view class="dingwei">
              <view class="t2">
                <i class="iconfont icon-position1"></i>
                <text class="doc">{{ address }}</text>
                <text class="btn-c" @click="goMap">更换</text>
              </view>
            </view>
            <ul class="area clearfix ul">
              <li
                v-for="(item, index) in nearbyList"
                :key="index"
                :class="item.selected ? 'selected li' : 'li'"
                @click="nearbyClick(item)"
              >
                {{ item.text
                }}<i class="van-icon"
                  ><uni-icons type="checkmarkempty" color="#ffffff" size="16"></uni-icons
                ></i>
              </li>
            </ul>
          </div>
          <!-- 产业园 -->
          <div v-if="isActive == 'c'" class="con-r">
            <ul class="area clearfix ul">
              <li
                v-for="(item, index) in estateParkData"
                :key="index"
                :class="item.selected ? 'selected li' : 'li'"
                @click="nearbyClickEstatePark(item)"
              >
                {{ item.name
                }}<i class="van-icon"
                  ><uni-icons type="checkmarkempty" color="#ffffff" size="16"></uni-icons
                ></i>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <view class="btn-wrap">
        <view class="box">
          <view class="reset" @click="reset()"><button class="btn btn-l">重置</button></view>
          <!-- <view class="sure" @click="sureAll()"
            ><button class="btn btn-r">
              {{ areaSelectedCount > 0 ? '确定(' + areaSelectedCount + ')' : '确定' }}
            </button></view
          > -->
          <view class="sure" @click="sureAll()"><button class="btn btn-r">确定</button></view>
        </view>
      </view>
      <!-- <uni-popup ref="popupSet" type="center">
        <view class="set-box">
          <view class="tit">定位权限</view>
          <view class="tips"
            >为了让您更精准的筛选职位所在地，需要获取您的定位权限，请在设置中打开权限</view
          >
          <view class="btn-set">
            <button class="cancel btn" @click="closePop">暂不</button>
            <button
              class="set btn"
              open-type="openSetting"
              @click="closePop"
              @opensetting="getSetting"
            >
              去设置
            </button>
          </view>
        </view>
      </uni-popup> -->
    </view>
  </uni-drawer>
</template>
<script lang="ts">
  export default {
    options: { styleIsolation: 'shared' }
  }
</script>
<script lang="ts" setup>
  import { onLoad } from '@dcloudio/uni-app'
  import { Data } from '@/services/home/<USER>'
  import {
    computed,
    nextTick,
    watch,
    toRef,
    toRefs,
    ref,
    onBeforeMount,
    inject,
    onMounted
  } from 'vue'
  import { positioningCity, getSetting } from '@/pages/index/hooks/location'
  import deepClone from '@/utils/deepClone'
  import { useAppStore } from '@/store/modules/app'
  import { BaseInfoOutput } from '@/services/home/<USER>'
  import { on } from 'events'
  const store = useAppStore()
  // let positionCity = $ref({ id: 0, name: '南宁' })
  let positionCity = $ref()
  let locations= $ref()
  let isGetlongitude = $ref<boolean>(false) //是否定位
  let address = $ref('获取位置信息失败，请开启位置权限')
  let currentLocation: any[] = []
  let place = $ref('地点')
  let currentareaid= $ref(0)
  let navTop = uni.getMenuButtonBoundingClientRect().top
  const emit = defineEmits<{
    (e: ['changeArea', 'clickFilter', 'open', 'close']): void
  }>()
  interface dataList {
    areaid?: number
    cityname?: string
  }
  const props = withDefaults(defineProps<dataList>(),{
    areaid: 0,
    cityname: '南宁'
  })
  const { areaid , cityname } = toRefs(props)
  const showDrawer = $ref<any>(null)
  const popupSet = $ref<any>(null)
  const scrollHeight = computed(() => {
    return uni.getSystemInfoSync().windowHeight - 80 - navTop
  })
  const windowWidth = computed(() => {
    return uni.getSystemInfoSync().windowWidth || 320
  })
  const initAreaSelected = [
    { type: 'a', value: [] },
    { type: 'm', value: [] },
    { type: 'f', value: [] },
    { type: '0', value: [] },
    { type: 'c', value: [] },
  ]
  let isActive = 'a'
  let areaSelected = $ref<Array[]>([
    { type: 'a', value: [] },
    { type: 'm', value: [] },
    { type: 'f', value: [] },
    { type: '0', value: [] },
    { type: 'c', value: [] },
  ])
  let areaData = [{ type: 'a', title: '商圈', selected: true }] //第一排
  let SubwayZone = $ref<Array[]>([]) //存放地铁和距离数据
  let arealistE = $ref<Array[]>([]) //商圈选择
  let metroList = $ref<Array[]>([]) //地铁选择1
  let stopsList = $ref<Array[]>([]) //地铁选择2
  let nearbyList = $ref<Array[]>([]) //附近
  let businessListchild = $ref<Array[]>([]) //商圈第三阶梯子数据
  const estateParkData = ref<BaseInfoOutput[]>([])

  const areaSelectedCount = computed(() => {
    if (isActive == 'a') {
      if (currentareaid == 0 || currentareaid == 2) {
        return areaSelected[3].value.length || 0
      } else {
        return areaSelected[0].value.length || 0
      }
    } else if (isActive == 'm') {
      return areaSelected[1].value.length || 0
    }  else if (isActive == 'c') {
      return areaSelected[4].value.length || 0
    }else {
      return areaSelected[2].value.length || 0
    }
  })
  // watch(
  //   () => areaid.value,
  //   async () => {
  //     await init(areaid.value,'',cityname.value)
  //   }
  // )
  // // #ifdef  MP-WEIXIN
  // onLoad(async (option) => {
  //   // await init(areaid)
  //   const { location } = option
  //   if (location) {
  //     const { locationResult } = await positioningCity(false)
  //     positionCity = locationResult
  //   }
  //   // console.log('定位的城市', positionCity)
  //   await init(areaid, location)
  // })
  // // #endif
 

  const option = inject('option')
 
  const parkGuid =option?.getParkGuid ? option.getParkGuid() : ''
    
  onMounted(async () => {
    const option = inject('option')

    const { location } = option

    
    if (location) {
      const { locationResult } = await positioningCity(false)
      positionCity = locationResult
    }
    locations = location
    await init(areaid.value || 2, location, cityname.value || '南宁',parkGuid.value)
  })
  const init = async (cityid: number, location: string,positionName:string,parkGuid?:string) => {
    currentareaid = cityid
    areaSelected = deepClone(initAreaSelected)
    let areaList = $ref<Array[] | undefined | null>([])
    let nearby = $ref<Array[] | undefined | null>([])
    const area = await Data.DistrictList({
      parentID: cityid,
      withCache: true,
      districtId: 0,
      from: 1
    })
    const metro = await Data.SubwayZoneList({ districtId: 0, from: 1 })
    SubwayZone = metro
    if (area == undefined) {
      areaList = [
        {
          keywordID: '',
          keywordName: '当前城市暂无区域',
          parentID: '',
          selected: false
        }
      ]
    } else {
      areaList = area.map((item: any, index: any) => {
        let arrs = {
          name: '全' + item.keywordName,
          cityDictId: item.keywordID,
          selected: false
        }
        item.selected = false
        item.selNum = '全'
        item.child = []
        item.child.push(arrs)
        return item
      })
      let arr = {
        keywordID: cityid,
        keywordName: '全' + positionName,
        parentID: 0,
        selected: false,
        child: [
          {
            name: '全' + positionName,
            cityDictId: cityid,
            selected: false
          }
        ]
      }
      areaList.unshift(arr)
      const businessArr = await Data.businessDistrictsByCity({
        cityDictId: cityid
    })
    // console.log('获取商圈列表', businessArr) 
      areaList.forEach((element: any)=>{
        businessArr.forEach((item:any) => {
          if (item.districtDictId == element.keywordID) {
            item.selected = false
          element.child.push(item)
          }
        })
      })
    }
    if (cityid == 2) {
      areaData = [
        { type: 'a', title: '商圈', selected: true },
        { type: 'm', title: '地铁', selected: false },
        { type: 'f', title: '附近', selected: false }
      ]
      let metros: [] = metro.subway
      metros.forEach((element: any) => {
        element.selected = false
        element.stops.forEach((item: any) => {
          item.selected = false
        })
      })
      metroList = metros
    } else {
      areaData = [
        { type: 'a', title: '区域', selected: true },
        { type: 'f', title: '附近', selected: false }
      ]
      isActive = 'a'
      areaData[0].selected = true
      metroList = [] //几号线
      stopsList = [] //地铁
    }
    areaData.push({
      type:'c',title:'产业园',selected:false
    })
    const estateParkDataRes = await Data.apiIndustrialParkBaseInfoListGet()
    console.log(estateParkDataRes)
    estateParkData.value = estateParkDataRes.map((item: any, index: any) => {
      item.selected = false
      return item
    })

    nearby = metro.distance.map((item: any, index: any) => {
      item.selected = false
      return item
    })

    place = areaList[0].keywordName.substring(1, 3)
    arealistE = areaList
    nearbyList = nearby
    if (location) {
      tab('f')
      nearbyClick(nearbyList[4])
    }
    if(parkGuid){
      tab('c')
      const item = estateParkData.value.find((item)=>item.guid ==parkGuid)
      if(item){
        nearbyClickEstatePark(item)
      }
    }
  }
  // 获取附近
  const getNearby = async () => {
    currentLocation = []
 
    if (store.positionCity.location != '') {
      currentLocation.push(store.positionCity.location)
      address = store.positionCity.address
      isGetlongitude = true
    } else {
      let { locationResult } = await positioningCity(false)
      if (locationResult.location) {
        currentLocation.push(locationResult.location)
 
        address = locationResult.address
        isGetlongitude = true
      } else {
        isGetlongitude = false
        address = '获取位置信息失败，请开启位置权限'
      }
    }
  }
  //保存7个数。用来在用户没有做任何改变的时候恢复原来的选择--修修补补有空再优化吧
  let copy_isActive = ''
  let copy_businessListchild = []
  let copy_arealistE = []
  let copy_metroList = []
  let copy_stopsList = []
  let copy_nearbyList = []
  let copy_areaSelected=[]
  let copy_estateParkData = []
  const openDrawer = async () => {
    copy_isActive = isActive
    copy_businessListchild = deepClone(businessListchild)
    copy_arealistE = deepClone(arealistE)
    copy_metroList = deepClone(metroList)
    copy_stopsList = deepClone(stopsList)
    copy_nearbyList = deepClone(nearbyList)
    copy_areaSelected = deepClone(areaSelected)
    copy_estateParkData = deepClone(estateParkData.value)
    emit('clickFilter', true) //用于禁止页面滚动
    showDrawer.open()
    emit('open')
    await getNearby()
  }
  const closeDrawer = () => {
    isActive = copy_isActive
    businessListchild = deepClone(copy_businessListchild)
    arealistE = deepClone(copy_arealistE)
    metroList = deepClone(copy_metroList)
    stopsList = deepClone(copy_stopsList)
    nearbyList = deepClone(copy_nearbyList)
    areaSelected = deepClone(copy_areaSelected)
    estateParkData.value = deepClone(copy_estateParkData)
    showDrawer.close()
    emit('close')
    emit('clickFilter', false)
  }
  const tab = (type: string) => {
    if (isActive == type) {
      return false
    }
    if (type == 'a') {
      place = arealistE[0].keywordName.substring(1, 3)
    }
    isActive = type
    reset()
  }
  const areaLiFirstClick = (item: any) => {
    if (item.parentID == 0) {
      //选择了全部
      arealistE.forEach((items: any) => {
        items.selected = false
        items.child.forEach((j: any) => {
          j.selected = false
        })
      })
      item.selected = true
      item.child[0].selected = true
    } else {
      let ite = arealistE.find((it: { parentID: number }) => it.parentID == 0)
      ite.selected = false
      ite.child[0].selected = false
 
      if (item.on) {
          if(item.selected){
            item.selected=false
            item.child.forEach((j:any)=>{
              j.selected = false
            })
          }else{
            item.selected=true
            item.child[0].selected=true
          }
        }else{
          if(!item.selected){
            item.selected=true
            item.child[0].selected=true
          }else{
            // console.log("不处理")
          }
        }
        statistics()
    }
    arealistE.forEach((items: any) => {
      items.on = false
    })
    item.on = true
    businessListchild = item.child 
  }
  const areaLiClick = (item: any) => {
    // console.log("第二次数据",item)
      if(item.selected){
        item.selected=false;
         statistics()
        return false
      }
    if(item.key){
      arealistE.forEach((items: any) => {
       if(items.keywordID==item.districtDictId){
          items.child.forEach((j:any)=>{
             if(j.cityDictId==items.keywordID){
              j.selected = false
         
            } else {
              item.selected = true
             
             }
          })
        }
      })
    } else {
      //这里选择的是全部
      arealistE.forEach((items: any) => {
        if (items.keywordID == item.cityDictId) {
          items.child.forEach((j: any) => {
              j.selected = false
          })
        }
      })
      item.selected = true
    }
    statistics()
  }
  //计算个数并 存入areaSelected
  // "businessDistinct": [
  //   "12:29_30_31_32",
  //   "66",
  //   "85:96_63"
  // ]
  const statistics = () => {
    arealistE.forEach((parents: any) => {
      let a = 0
          parents.child.forEach((it:any,index:number)=>{
              if(index!=0&&it.selected){
                a=++a
              }
              if(index==0&&it.selected){
                a='全'
                
              }
          })
          if(a>0||a=='全'){
            parents.selected=true
          }else{
            parents.selected=false
          }
          parents.selNum=a
    })
  }
  //单选
  const nearbyClick = (item: any) => {
    if (!item.selected) {
      nearbyList.forEach((item: any) => {
        item.selected = false
      })
      areaSelected[2].value = []
      item.selected = true
      areaSelected[2].value.push(item.value)
    } else {
      return false
    }
    place = item.text
  }

  const nearbyClickEstatePark = (item: BaseInfoOutput) => {
    if (!item.selected) {
      estateParkData.value.forEach((item: any) => {
        item.selected = false
      })
      areaSelected[4].value = []
      item.selected = true
      areaSelected[4].value.push(item.guid)
    } else {
      return false
    }
    place = '产业园'
  }
  const metroLiClick = (stops: Array, item: object) => {
    if (item.selected) {
      return false
    } else {
      metroList.forEach((element: any) => {
        element.selected = false
      })
      areaSelected[1].value = []
      stopsList = deepClone(stops)
      item.selected = true
    }
    place = item.trafficLine
  }
  const stopsLiClick = (item: any) => {
    let llStr = `${item.longitude},${item.latitude}`
    if (!item.selected) {
      item.selected = true
      areaSelected[1].value.push(llStr)
    } else {
      const index = areaSelected[1].value.findIndex((element: any) => element == llStr)
      areaSelected[1].value.splice(index, 1)
      item.selected = false
    }
  }
  const reset = () => {
    areaSelected = deepClone(initAreaSelected)
    arealistE.forEach((item: any) => {
      item.selected = false
      item.on = false
      item.child.forEach((j: any) => {
        j.selected = false
      })
    })
    metroList.forEach((item: any) => {
      item.selected = false
    })
    nearbyList.forEach((item: any) => {
      item.selected = false
    })
    estateParkData.value.forEach((item: any) => {
      item.selected = false
    })
    stopsList = []

  }
  const sureAll = () => {
    //遍历商圈
    let EArr = []
    let Aarr: any[]=[]
    
    arealistE.forEach((j:any)=>{
      let bb = ''
      let Err = ''
      let a: any[] = []
      let c: any[] = []
      if (j.selected) {
        Err = j.keywordID
        j.child.forEach((i: any,index:number) => {
          if (i.selected && index != 0) {
            a.push(i.key)
          }
          if (i.selected) {
            c.push(i.key)
          }
        })
        Aarr = Aarr.concat(c)
        let str = a.join('_')
        bb = str ? Err + ':' + str : Err
        EArr.push(bb)
      }
    })
    areaSelected[3].value = Aarr
    areaSelected[0].value = EArr
 
    let arr = areaSelected
    if (!isGetlongitude && areaSelected[2].value.length > 0) {
      // uni.showToast({
      //   title: '获取位置失败！请点击右上角进去设置页进行权限设置',
      //   icon: 'none'
      // })
      getSetting(getNearby)
      return false
    }
    if (areaSelected[2].value.length > 0) {
      arr[1].value = currentLocation
    }
    // closeDrawer()
    showDrawer.close()
    emit('close')
    emit('clickFilter', false)
    emit('changeArea', arr)
 
  }
  const goMap = () => {
    //#ifdef  MP-TOUTIAO
    uni.chooseLocation({
      success: function (res) {
        currentLocation = []
        address = res.name || res.address
        let location = `${res.latitude},${res.longitude}`
        currentLocation.push(location)
        // initRecommendList()
      },
      fail: function (err) {
        console.log(5, err)
        if (err.errNo == 10200) {
          //获取权限失败
          getSetting(getNearby)
          isGetlongitude = false
        }
      }
    })
    // #endif
 
    // uniapp 需要先调取用户授权请求询问用户是否授权
    // #ifdef MP-WEIXIN
 
    uni.authorize({
      scope: 'scope.userLocation',
      success(res) {
        uni.chooseLocation({
          success: function (res) {
            currentLocation = []
            address = res.name || res.address
            let location = `${res.latitude},${res.longitude}`
            currentLocation.push(location)
            // initRecommendList()
          }
        })
      },
      fail(err) {
        // popupSet.open()
        getSetting(getNearby)
        isGetlongitude = false
      }
    })
    // #endif
 
    // #ifdef MP-ALIPAY
    uni.getLocation({
      // type: 'gcj02',
      geocode: true,
      isHighAccuracy: true,
      highAccuracyExpireTime: 3000,
      success(res: any) {
        uni.chooseLocation({
          success: function (res) {
            currentLocation = []
            address = res.name
            let location = `${res.latitude},${res.longitude}`
            currentLocation.push(location)
            // initRecommendList()
          }
        })
      },
      fail(err) {
        // popupSet.open()
        getSetting(getNearby)
        isGetlongitude = false
      }
    })
    // #endif
  }
  // const closePop = () => {
  //   popupSet.close()
  // }
 
  // const getSetting = (res: any) => {
  //   let st = res.detail.authSetting['scope.userLocation']
  //   if (st) {
  //       // goMap()
  //       getNearby()
  //   } else {
  //   }
  // }
 
  const testABC = async (id: string, cityName: string) => {
    if (Number(id) != currentareaid) {
      await init(Number(id), locations, cityName)
    }
    businessListchild = []
  }
  defineExpose({
    testABC
  })
</script>
<style lang="scss" scoped>
  $blue: #5e8df5;
  .place {
    float: right;
    height: 60rpx;
    line-height: 60rpx;
    font-size: 24rpx;
    padding: 0 14rpx 0 20rpx;
    text-align: center;
    background: #f5f7fa;
    border-radius: 4rpx;
    color: #bbb;
    margin-left: 20rpx;
    min-width: 120rpx;
    .icon-arrowDown5 {
      font-size: 24rpx;
      float: right;
    }
  }
  .bg-blue {
    background: #eef5ff;
    color: #457ccf;
  }
  .screen-area {
    .con-wrap {
      // overflow-y: scroll;
      .con {
        display: flex;
        color: #666666;
        min-height: 100%;
        .ul {
          .li {
            line-height: 50rpx;
            padding: 12px 10px;
            font-size: 32rpx;
          }
        }
        .con-l {
          flex: 1;
          background: #ffffff;
          text-align: center;
          .ul {
            width: 200rpx;
            .li.current {
              background: #f5f5f5;
              border-left: 4rpx solid #457ccf;
            }
          }
        }
        .con-r {
          flex: 3;
          .ul {
            padding: 0 0rpx 40rpx 0rpx;
            overflow-y: scroll;
            .li {
              // padding: 0 30px;
              .van-icon {
                display: none;
              }
            }
            .li.selected {
              color: #457ccf;
              position: relative;
              .van-icon {
                display: block;
                font-size: 18rpx;
                color: #fdfdfd;
                line-height: 15px;
                width: 30rpx;
                height: 30rpx;
                text-align: center;
                background: #457ccf;
                border-radius: 30px;
                position: absolute;
                right: 30rpx;
                top: 43rpx;
              }
            }
            .li.on {
              background: #f5f5f5;
            }
          }
        }
        .con-r-s{
          display: flex;
 
          .sdr .li.selected{
            color: #457ccf;
            background: #f5f5f5;
          }
          .sdl{
            flex: 2;
          }
          .sdr{
            flex: 2;
            background: #ffffff;
 
          }
 
        }
        .con-r-metro {
          display: flex;
          .ul.metro {
            flex: 2;
          }
          .ul.stops {
            flex: 3;
          }
        }
      }
      .dingwei {
        padding: 0 0 0 30rpx;
        .t2 {
          font-size: 24rpx;
        }
        .btn-c {
          color: #457ccf;
        }
        .iconfont {
          font-size: 24rpx;
          color: #457ccf;
          float: left;
        }
      }
    }
    .btn-wrap {
      position: fixed;
      width: 100%;
      bottom: 0;
      left: 0;
      background: #fff;
      .box {
        padding: 5px;
        display: flex;
      }
      .reset {
        flex: 1;
        padding-right: 10px;
      }
      .sure {
        flex: 2;
      }
      .btn {
        width: 100%;
        height: 40px;
        line-height: 40px;
        border-radius: 5px;
        font-size: 14px;
      }
      .btn-l {
        background: #fff;
        color: #457ccf;
        border: 1rpx solid #457ccf;
      }
      .btn-r {
        border: 1rpx solid #457ccf;
        background: #457ccf;
        color: #fff;
      }
    }
    ::v-deep .uni-popup {
      z-index: 300;
    }
  }
  .set-box {
    width: 400rpx;
    height: auto;
    background: #fff;
    text-align: center;
 
    border-radius: 8rpx;
    .tit {
      font-size: 28rpx;
      color: #333;
      line-height: 70rpx;
    }
    .tips {
      font-size: 24rpx;
      color: #666;
      line-height: 40rpx;
      padding: 0 30rpx 20rpx;
    }
    .btn-set {
      display: flex;
      border-top: 1px solid #eee;
    }
    .btn {
      background: #fff;
      width: 50%;
      color: #457ccf;
      font-size: 24rpx;
      flex: 1;
      border: none;
    }
    ::v-deep .btn ::after {
      border: none;
    }
    .cancel {
    }
  }
  // ::v-deep.uni-navbar {
  //   padding-top: 120rpx;
  //   z-index: 200;
  // }
  ::v-deep.position-search-top {
    z-index: 9;
  }
</style>
}