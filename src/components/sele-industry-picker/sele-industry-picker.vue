<template>
  <view class="industry-picker clearfix">
    <view class="picker-input" @tap.stop="selecity()">
      <text
        v-if="type == 1"
        :class="[industryName ? 'uni-ellipsis-1' : 'uni-ellipsis-1 pleaseChoose', textAlign]"
        >{{ industryName || '请选择' }}</text
      >
      <view v-if="type == 2" class="industry-ul">
        <view v-for="(item, index) in industryName" :key="index" class="li">
          <text v-if="item" class="span">{{ item }}</text
          ><i v-if="item" class="iconfont icon-close iconx" @click.stop="delItem(index)"></i>
        </view>
        <view v-if="industryName.length < 1" class="pleaseChoose">请选择 </view>
      </view>
      <i class="iconfont icon-arrowRight14 icon" :style="type === 1 ? '' : 'bottom: 20rpx;'"></i>
    </view>
    <uni-popup ref="showRight" type="right" :is-mask-click="true" class="pop-industry-box">
      <view class="bg-white h100">
        <uni-nav-bar
          left-text="行业类别"
          left-width="200rpx"
          height="44px"
          :fixed="true"
          :status-bar="true"
          left-icon="left"
          :border="false"
          @clickLeft="closeDrawer"
        />
        <view class="search-bar bg-white" @click="openSearch">
          <view class="search-skip">
            <view class="cot">
              <i class="iconfont icon-search1"></i>
              <text class="txt">搜索行业类别</text>
            </view>
          </view>
        </view>
        <view v-show="maxCount > 1" class="selected-wrap bg-white">
          <view class="count"
            >已选择行业( <label class="bule">{{ selectedCount }}</label
            >/{{ maxCount }})</view
          >
          <view class="selected-items clearfix">
            <view
              v-for="val in activeGroup"
              :key="val.keywordID"
              class="item"
              @click="deleteCity(val)"
            >
              {{ val.keywordName }}
              <i class="iconclose">X</i>
            </view>
          </view>
        </view>
        <view v-if="recommendShow" class="wantTchoose">
          <view class="tit">
            <view class="nm">您可能想选</view>
            <i class="iconfont icon-close" @click="closeXiang"></i
          ></view>
          <view class="recommendList">
            <!-- 左右滚动 -->
            <scroll-view class="scroll-view" scroll-x="true">
              <view class="content">
                <view
                  v-for="(item, index) in recommendList"
                  :key="index"
                  :class="item.selected ? 'item_xiang item_C' : 'item_xiang'"
                  @click="ChooseRecommend(item, item.selected)"
                  >{{ item.keywordName }}</view
                >
              </view>
            </scroll-view>
            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~ -->
          </view>
        </view>

        <view class="sel-main clearfix bg-white" :style="`width:` + width + 'px'">
          <view class="box-city clearfix">
            <!-- 第一组 -->
            <view class="sel-industry-box sel-industry-boxA">
              <scroll-view
                :style="{ height: scrollHeight1 + 'px' }"
                scroll-y="true"
                :show-scrollbar="false"
              >
                <view class="ul">
                  <view
                    v-for="(item, index) in firstArr"
                    :key="index"
                    clickable
                    :class="[
                      'li clearfix',
                      { on: item.keywordID == isIDA, isseled: item.selected }
                    ]"
                    @click="cityChooseA(item)"
                  >
                    <view class="sdl">{{ item.keywordName }}</view>
                  </view>
                </view>
              </scroll-view>
            </view>
          </view>
          <!-- 第二组 -->
          <view class="sel-industry-box sel-industry-boxC">
            <view class="city-boxC">
              <scroll-view :style="{ height: scrollHeight1 + 'px' }" scroll-y="true">
                <view class="ul">
                  <view
                    v-for="(item, index) in secondArr"
                    :key="index"
                    clickable
                    :class="['li clearfix', { isseled: item.selected }]"
                    @click="cityChooseC(item)"
                  >
                    <view class="sdl">{{ item.keywordName }}</view>
                    <view class="sdr">
                      <i v-if="item.selected" class="hook"
                        ><uni-icons type="checkmarkempty" color="#5e8df5" size="16"></uni-icons
                      ></i>
                    </view>
                  </view>
                </view>
              </scroll-view>
            </view>
          </view>
        </view>
        <view class="btn-box" @click="bindComfirm">
          <button class="login-wrap-button">保存</button>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="popup" type="right">
      <view class="bg-white popup-search-box" :style="{ width: width + 'px' }">
        <uni-nav-bar
          title="行业类别"
          left-width="200rpx"
          :fixed="true"
          :status-bar="true"
          left-icon="left"
          :border="false"
          @clickLeft="closeSearch"
        />
        <view class="search-cell">
          <view class="search-cell-box">
            <input
              class="uni-input"
              :focus="focus"
              type="text"
              placeholder="搜索行业类别"
              confirm-type="search"
              @confirm="searchKey"
              @input="searchKey"
            />
          </view>
        </view>
        <scroll-view
          :style="{ height: scrollHeight + 'px' }"
          scroll-y="true"
          :show-scrollbar="false"
        >
          <view class="search-cell-group">
            <view
              v-for="i in IndustrySearchList"
              :key="i"
              class="item"
              @click="industryChoose(i.id)"
            >
              <view class="title">{{ i.name }}</view>
              <view class="subhead">{{ i.fullname }}</view>
            </view>
          </view>
          <view v-if="IndustrySearchList == ''" class="Nodata">
            <view class="title">搜索无数据哦，换个关键字吧！</view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>
  </view>
</template>
<script lang="ts">
  export default {
    options: { styleIsolation: 'shared' }
  }
</script>
<script lang="ts" setup>
  import { computed, watch, ref, nextTick, getCurrentInstance } from 'vue'
  import { onLoad } from '@dcloudio/uni-app'
  import { Options } from '@/services/my/Options'
  import { Autocompelete } from '@/services/my/Autocompelete'
  // import { KeywordItemDto } from '@/services/my/data-contracts'
  import { Register } from '@/services/my/Register'

  interface dataList {
    hideValue: []
    title?: string
    maxCount?: number
    industryName?: string | Array<[]>
    textAlign?: string
    type?: number
    careerId?: number
    showYouwant?: boolean
  }
  let allIndustry = $ref<Array<number | string>>()
  let IndustrySearchList = $ref({})
  let firstArr = $ref<Array<number | string>>()
  let secondArr = $ref<Array<number | string>>()
  let activeGroup = $ref<Array[]>([])
  let isIDA = $ref<any>(-1) //第一排高亮
  let msgType = $ref<string>() //success error warn info
  let focus = $ref<boolean>(false)
  let recommendShow = $ref<boolean>(true)
  let recommendList = $ref<Array[]>([])

  let navTop = uni.getMenuButtonBoundingClientRect().top
  let selectedH = $ref<number>(0)
  let recommendH = computed(() => {
    if (recommendShow) {
      return 70
    } else {
      return 0
    }
  })

  const {
    hideValue,
    maxCount = 1,
    title = '请选择',
    industryName,
    textAlign = 'right',
    type = 1,
    careerId = 0,
    showYouwant = true
  } = defineProps<dataList>()

  const instance = getCurrentInstance()
  const scrollHeight1 = computed(() => {
    return uni.getSystemInfoSync().windowHeight - 200 - selectedH - navTop - recommendH.value
  })
  const scrollHeight = computed(() => {
    return uni.getSystemInfoSync().windowHeight - 190
  })
  const width = computed(() => {
    return uni.getSystemInfoSync().windowWidth || '320'
  })

  const showRight = $ref<any>(null)
  const popup = $ref<any>(null)
  const message = $ref<any>(null)
  const emit = defineEmits<{
    (e: ['change', 'delItem', 'open', 'close']): void
  }>()
  const selectedCount = computed(() => {
    return activeGroup.length || 0
  })
  const canSelectMore = computed(() => {
    return maxCount <= selectedCount.value
  })
  const singleMode = computed(() => {
    return maxCount === 1
  })
  onLoad(async (option) => {})
  watch(
    () => selectedCount.value,
    () => {
      nextTick(() => {
        hideInspect()
      })
    }
  )
  watch(
    () => careerId,
    () => {
      if (careerId && showYouwant) {
        nextTick(() => {
          getRecommendIndustryData(Number(careerId))
          recommendShow = true
        })
      } else {
        recommendShow = false
      }
    }
  )
  const getDate = async () => {
    activeGroup = []
    firstArr = []
    secondArr = []
    isIDA = -1
    let datas = []
    let arr = uni.getStorageSync('IndustryList')
    if (arr.length > 0) {
      datas = arr
    } else {
      const Data: Array = await Options.optionsIndustryList()
      uni.setStorageSync('IndustryList', Data)
      datas = Data
    }

    datas.map((item: any, index: any) => {
      item.selected = false
    })
    allIndustry = datas

    hideValue.forEach((id: any) => {
      let item = allIndustry.find((p: any) => p.keywordID == id)
      if (!item) return
      item.selected = true

      addCity(item)
    })
    firstArr = datas.filter((i: any) => i.parentID == 0)
    cityChooseA(firstArr[0])
  }
  const selecity = () => {
    getDate()
    if (showYouwant && careerId) {
      recommendShow = true
    } else {
      recommendShow = false
    }

    showRight.open()
    emit('open')
  }
  const bindComfirm = () => {
    if (activeGroup.length < 1) {
      uni.showToast({
        title: '请至少选择一个',
        duration: 2000,
        icon: 'none'
      })
      return
    }
    emit('change', activeGroup)
    showRight.close()
    emit('close')
  }

  const openSearch = () => {
    popup.open('right')
    setTimeout(() => {
      focus = true
    }, 500)
  }
  const closeSearch = () => {
    popup.close('right')
  }

  const closeDrawer = () => {
    showRight.close()
    emit('close')
    setTimeout(() => {
      focus = false
    }, 500)
  }
  const getChildren = (pid: any, num: any) => {
    return allIndustry.filter((i: any) => i.parentID == pid)
  }
  const cityChooseA = (city: any) => {
    //第一行点击
    if (isIDA == city.keywordID && city.hasNext) {
      return false
    }
    if (isIDA == city.keywordID && !city.hasNext) {
      onSelectCity(city)
      return false
    }
    isIDA = city.keywordID
    secondArr = []
    if (!city.hasNext) {
      //如果没有子集直接选取
      onSelectCity(city)
      return false
    }
    secondArr = getChildren(city.keywordID, 1)
  }
  const cityChooseC = (city: any) => {
    onSelectCity(city)
  }
  const onSingleMode = (city: any) => {
    //单选模式
    allIndustry.forEach((item: any) => {
      item.selected = false
    })

    secondArr.forEach((item: any) => {
      item.selected = false
    })
    firstArr.forEach((item: any) => {
      item.selected = false
    })
    city.selected = true
    activeGroup = [city]
    bindComfirm()
  }
  const onMultipleMode = (city: any) => {
    //多选模式
    const isChecked = city.selected
    if (isChecked) {
      //被选过了
      deleteCity(city)
      return false
    }
    //如果没有被选过检测下面的子集和往上的父级
    if (!isChecked) {
      const item = activeGroup.find((item:any)=>item.keywordID == 10472)
      if(item && city.keywordID != 10472){
        deleteCity(item)
      }
      activeGroup.forEach((i: any) => {
        if (
          i.parentID == city.keywordID ||
          i.keywordID == city.parentID ||
          i.grandfather == city.keywordID ||
          i.keywordID == city.grandfather
        ) {
          deleteCity(i)
        }
      })
      if (canSelectMore.value && city.keywordID != 10472 ) {
        //如果没选过而且超限了
        const messageText = `最多只能选择${maxCount}个选项`
        uni.showToast({
          title: messageText,
          duration: 2000,
          icon: 'none'
        })
      } else {
        if (city.keywordID == 10472) {
          activeGroup.forEach((i: any) => {
            deleteCity(i)
          })
        }
        addCity(city)
      }
    }
  }
  const addCity = (city: any) => {
    city.selected = true
    activeGroup.push(city)

    let foundElement = recommendList.find((i: any) => i.keywordID == city.keywordID)
    if (foundElement) {
      foundElement.selected = true
    }
    let itemOne = secondArr.find((i: any) => i.keywordID == city.keywordID)
    if (itemOne) {
      itemOne.selected = true
    }
  }
  const onSelectCity = (city: any) => {
    if (!city) return
    if (singleMode.value) {
      onSingleMode(city)
    } else {
      onMultipleMode(city)
    }
  }
  const deleteCity = (item: any) => {
    item.selected = false
    secondArr.forEach((i: any) => {
      i.keywordID == item.keywordID ? (i.selected = false) : ''
    })
    activeGroup = activeGroup.filter((i: any) => i.keywordID !== item.keywordID)
    celR(item)
  }
  //搜索
  const searchKey = async (event: { target: { value: any } }) => {
    const keyword = event.target.value
    IndustrySearchList = await Autocompelete.autocompeleteSearchsecondindustry({ keyword: keyword })
  }
  const industryChoose = (id: number) => {
    allIndustry.forEach((item: any) => {
      if (item.keywordID === id) {
        cityChooseC(item)
      }
    })
    popup.close('right')
    setTimeout(() => {
      focus = false
    }, 500)
  }
  const delItem = (index: number) => {
    emit('delItem', index)
  }
  const hideInspect = () => {
    nextTick(() => {
      const query = uni.createSelectorQuery().in(instance)
      query
        .select('.selected-items')
        .boundingClientRect((res) => {
          selectedH = parseInt(res?.height)
        })
        .exec()
    })
    return false
  }
  const closeXiang = () => {
    recommendShow = false
  }
  const getRecommendIndustryData = async (id: number) => {
    let Date = await Register.recommendindustrydata({ keywordid: id, withcache: true })
    // console.log("获取推荐的职位",Date)
    recommendList = Date.map((item: any, index: any) => {
      return {
        ...item,
        selected: false
      }
    })
  }
  const ChooseRecommend = (item: any, type: boolean) => {
    if (type) {
      deleteCity(item)
      // item.selected = !type
    } else {
      // if (!canSelectMore.value) {
      //   item.selected = !type
      // }
      cityChooseC(item)
    }
  }
  const celR = (item: any) => {
    let foundElement = recommendList.find((i: any) => i.keywordID == item.keywordID)
    if (foundElement) {
      foundElement.selected = false
    }
  }
</script>

<style lang="scss" scoped>
  $blue: #5e8df5;
  .h100 {
    height: 100%;
  }
  .industry-picker {
    .picker-input {
      flex: 1;
      width: 100%;
      display: flex;
      box-sizing: border-box;
      min-height: 36px;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      position: relative;
      font-size: 28rpx;
      .left {
        position: absolute;
        left: 0;
        text-align: left;
      }
      .right {
        position: absolute;
        right: 38rpx;
        text-align: right;
      }
      .uni-ellipsis-1 {
        width: 100%;
        font-size: 15px;
      }
    }
    .icon {
      position: absolute;
      right: 0;
      font-size: 24rpx;
      color: #808080;
    }
    .industry-ul {
      padding-bottom: 10rpx;
      width: 100%;
      .li {
        width: 100%;
        float: left;
        margin: 12rpx 0 0 0;
      }
      .span {
        background: #eef5ff;
        float: left;
        color: #457ccf;
        font-size: 28rpx;
        padding: 0rpx 0rpx 0 20rpx;
        height: 54rpx;
        line-height: 54rpx;
      }
      .iconx {
        font-size: 18rpx;
        padding: 0rpx 15rpx 0 20rpx;
        height: 54rpx;
        line-height: 54rpx;
        background: #eef5ff;
        float: left;
        color: #457ccf;
      }
    }
  }
  .bg-white {
    background: #fff;
  }
  .pop-industry-box {
    background: #fff;
    width: 100%;
    .btn-box {
      background: #fff;
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      border-top: 1px solid #eee;
      .login-wrap-button {
        border: none;
        background: #5e8df5;
        color: #fff;
        margin: 10px 10px 30px;
        height: 40px;
        line-height: 40px;
        font-size: 16px;
      }
    }
    ::v-deep .uni-navbar__content_view {
      height: 40px;
      line-height: 40px;
    }
    ::v-deep .uni-navbar-btn-text {
      text {
        font-size: 14px !important;
      }
    }
  }

  /*  #ifdef MP-ALIPAY */
  .btn-box {
    background: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    border-top: 1px solid #eee;
    .login-wrap-button {
      border: none;
      background: #5e8df5;
      color: #fff;
      margin: 10px 10px 30px;
      height: 40px;
      line-height: 40px;
      font-size: 16px;
    }
  }
  ::v-deep .uni-navbar__content_view {
    height: 40px;
    line-height: 40px;
  }
  ::v-deep .uni-navbar-btn-text {
    text {
      font-size: 14px !important;
    }
  }

  /*  #endif  */
  .selected-wrap {
    padding: 0px 15px;
    .count {
      font-size: 13px;
      color: #666;
      line-height: 24px;
    }
    .blue {
      color: #228efd;
    }
    .selected-items {
      padding: 2px 0 10px 0;
      .item {
        color: #228efd;
        font-size: 13px;
        padding: 0px 10px;
        float: left;
        margin: 5px 5px 0 0;
        display: flex;
        border: 1px solid #228efd;
        border-radius: 3px;
        line-height: 22px;
        .iconclose {
          font-size: 14px;
          padding-left: 5px;
        }
      }
    }
  }
  .wantTchoose {
    .tit {
      font-size: 28rpx;
      color: #333333;
      display: flex;
      padding: 10rpx 0;
      margin: 0 30rpx;
      border-top: 1px solid #eeeeee;
      .nm {
        flex: 1;
      }
      .icon-close {
        font-size: 24rpx;
        color: #999999;
      }
    }
    .recommendList {
      width: 750rpx;
      height: 80rpx;
      overflow: hidden;
      .scroll-view {
        width: 750rpx;
        height: 80rpx;
        white-space: nowrap;
        .item_xiang {
          background: #f5f7fa;
          border-radius: 30rpx;
          font-size: 28rpx;
          color: #666666;
          margin: 10rpx 0rpx 10rpx 30rpx;
          padding: 0 26rpx;
          height: 60rpx;
          line-height: 60rpx;
        }
        .item_C {
          color: #228efd;
        }
      }

      .content {
        display: flex;
        flex-direction: row;
        white-space: nowrap; /* 防止换行 */
      }

      .item {
        flex-shrink: 0; /* 防止元素缩小 */
        padding: 10px;
        background-color: #f0f0f0;
        margin-right: 10px;
      }
    }
  }
  .sel-main {
    .box-city {
      float: left;
      margin-right: -410rpx;
      position: relative;
    }
    .sel-industry-box {
      float: left;
      .li {
        padding: 26rpx 20rpx 26rpx 20rpx;
        color: #666;
        position: relative;
        line-height: 40rpx;
        .sdl {
          float: left;
          font-size: 26rpx;
        }
        .sdr {
          position: absolute;
          right: 10rpx;
          top: 50%;
          margin-top: -16rpx;
          .iconfont {
            font-size: 24rpx;
            line-height: 40rpx;
          }
        }
      }
      .isseled {
        color: $blue;
      }
    }
    .sel-industry-boxA {
      width: 240rpx;
      background: #fff;
      .on {
        color: $blue;
      }
      .li {
        padding: 26rpx 20rpx 26rpx 20rpx;
        line-height: 40rpx;
      }
    }
    .sel-industry-boxC {
      width: 100%;
      background: #f5f5f5;
      .city-boxC {
        margin-left: 240rpx;
      }
    }
  }
  .uni-ellipsis-1 {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .search-bar {
    padding: 10px;
    .search-skip {
      background: #f5f7fa;
      border-radius: 20px;
      height: 35px;
      line-height: 35px;
      color: #999;
      font-size: 13px;
      padding: 0 15px;
    }
    .iconfont {
      font-size: 14px;
      padding-right: 2px;
      float: left;
    }
    .txt {
    }
  }

  .popup-search-box {
    height: 100%;
    .search-cell {
      padding: 10px 20px;
      &-box {
        border-bottom: 1px solid #457ccf;
        padding: 10px 0;
        font-size: 14px;
      }
      .uni-input {
        border: none;
        background: #fff;
      }
    }
    .search-cell-group {
      padding: 40rpx;
      .item {
        border-bottom: 1px solid #f2f2f2;
        text-align: left;
        font-size: 28rpx;
        padding: 14rpx 0;
        line-height: 40rpx;
      }
      .title {
        font-size: 28rpx;
      }
      .subhead {
        font-size: 24rpx;
        color: #bbb;
        padding: 10rpx 0 0 0;
      }
    }
    .Nodata {
      text-align: center;
      font-size: 28rpx;
    }
  }
  .pleaseChoose {
    color: #999;
    // font-size: 26rpx;
    font-weight: 200;
  }
</style>
