<template>
  <view class="gxrc-picker">
    <view class="picker-input" :class="{ end: type === 'end' }" @tap="bindTap">
      <text v-if="datavalue" class="pd">{{ datavalue }}</text>
      <text v-else class="placehold pd">{{ placeholder }}</text>
      <i class="iconfont icon-arrowRight14 icon"></i>
    </view>
    <uni-popup ref="myPopup" type="bottom" background-color="#fff">
      <view class="picker-title">
        <text class="cancel font" @tap="bindCancel">取消</text>
        <text class="title">{{ title }}</text>
        <text class="confirm font" @tap="bindComfirm">确认</text>
      </view>

      <picker-view
        class="picker-view"
        :value="selectValue"
        :immediate-change="immediatechange"
        @change="bindChange"
      >
        <picker-view-column>
          <view v-for="(item, id) in dataList" :key="id" class="item">
            {{ item.text || item.keywordName }}
          </view>
        </picker-view-column>
      </picker-view>
    </uni-popup>
  </view>
</template>
<script lang="ts" setup>
  import { nextTick, watch } from 'vue'

  interface pickerData {
    text: string

    value: number | string | boolean
  }

  interface Props {
    dataList: Record<string, any>
    modelValue: number | string | boolean | null | undefined

    title?: string

    placeholder?: string
    //文字左右两端space  一起在右边 end
    type?: 'space' | 'end'
  }

  const {
    dataList,
    modelValue,
    placeholder,
    title = '请选择',
    type = 'space'
  } = defineProps<Props>()

  const emit = defineEmits<{
    (e: 'update:modelValue', value: number | string | boolean): void
  }>()

  let datavalue = $ref<number | string | boolean | null>()

  let selectValue = $ref<number[]>([0])

  const myPopup = $ref<any>(null)

  const initSelect = (value: any) => {
    if (dataList && dataList.length > 0) {
      nextTick(() => {
        const index = dataList?.findIndex((item: any) => (item.value ?? item.keywordID) === value)
        selectValue = [index == -1 ? 0 : index]

        datavalue = dataList[index]?.text ?? dataList[index]?.keywordName ?? value
      })
    }
  }
  
  // #ifdef MP-WEIXIN || MP-ALIPAY
  let immediatechange = true
  // #endif
  // #ifdef MP-TOUTIAO
  let immediatechange = false
  // #endif

  watch(
    () => dataList,
    () => {
      initSelect(modelValue)
    }
  )

  watch(
    () => modelValue,
    (newval) => {
      initSelect(newval)
    },
    {
      deep: true,
      immediate: true
    }
  )

  const bindTap = () => {
    myPopup.open()
  }

  const bindCancel = () => {
    myPopup.close()
  }

  const bindChange = (e: any) => {
    console.log(2,e);
    const value = e.detail.value
    selectValue = value
  }

  const bindComfirm = () => {
    const [index] = selectValue
    datavalue = dataList[index].text || dataList[index].keywordName
    const value = dataList[index].value ?? dataList[index].keywordID

    emit('update:modelValue', value)
    myPopup.close()
  }
</script>
<style lang="scss">
  .gxrc-picker {
    .picker-title {
      display: flex;
      // flex-direction: row;
      justify-content: space-between;
    }
    .picker-input {
      flex: 1;
      width: 100%;
      display: flex;
      box-sizing: border-box;
      min-height: 36px;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .icon {
        font-size: 12px;
        color: $uni-text-color-placeholder;
      }
      .placehold {
        // color: #bbbbbb;
        color: #999;
        font-weight: 200;
      }
      .pd {
        padding-right: 8px;
        font-size: 15px;
      }
    }
    .end {
      justify-content: flex-end;
      color: #666666;
    }
    .font {
      // font-size: $uni-font-size-sm;
      color: $uni-color-primary;
    }
    .cancel {
      padding: 15px;
      color: #666666;
    }
    .confirm {
      padding: 15px;
    }
    .title {
      padding-top: 15px;
    }
    .picker-view {
      width: 750rpx;
      min-height: 400rpx;
      margin-top: 15rpx;
      .item {
        height: 100rpx;
        line-height: 66rpx;
        align-items: center;
        justify-content: center;
        text-align: center;
        
      }
    }
  }
</style>
