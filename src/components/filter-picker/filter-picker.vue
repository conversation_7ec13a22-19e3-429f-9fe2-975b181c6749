<template>
  <view :class="screenSelectedCount > 0 ? 'filter bg-blue' : 'filter bg'" @click="openDrawer">
    筛选
    <span v-show="screenSelectedCount > 0" class="span">{{
      screenSelectedCount ? `(${screenSelectedCount})` : ''
    }}</span>
    <i class="iconfont icon-arrowDown5"></i>
  </view>
  <uni-drawer ref="showDrawer" mode="right" :mask-click="true" :width="windowWidth">
    <view class="filter-pop">
      <!--  #ifdef  MP-ALIPAY -->

      <view style="height: 100rpx"> </view>
      <!--  #endif -->
      <!--  #ifdef MP-WEIXIN ||  MP-TOUTIAO -->
      <view class="uni-navbar">
        <view class="uni-navbar--fixed uni-navbar__content">
          <view class="uni-status-bar" :style="`height:${height3}px`"></view>
          <view class="uni-navbar__header">
            <view class="uni-navbar__header-btns uni-navbar__header-btns-left" @click="closeDrawer">
              <i class="iconfont icon-close"></i>
            </view>
            <view class="uni-navbar__header-container">
              <view class="uni-navbar__header-container-inner">
                <text class="uni-nav-bar-text uni-ellipsis-1"> 筛选 </text>
              </view>
            </view>
            <view class="uni-navbar__header-btns uni-navbar__header-btns-right"></view>
          </view>
        </view>
      </view>
      <!--  #endif -->
      <scroll-view
        :style="{ height: scrollHeight + 'px' }"
        scroll-y="true"
        :show-scrollbar="false"
        class="con"
      >
        <view v-for="(item, index) in screenData" :key="index" class="one">
          <view class="hd" v-html="item.title"></view>
          <view class="bd">
            <view class="ul clearfix">
              <view
                v-for="(item2, index2) in item.list"
                :key="index2"
                :class="item2.selected ? 'selected li' : 'li'"
                :data-id="item2.id"
                @click="screenLiClick(item.title, item2.id, item2.name, item2.selected)"
              >
                {{ item2.name }}
              </view>
            </view>
          </view>
        </view>
        <view class="two">
          <view v-show="isPosition" class="jinpin clearfix">
            <view class="sdl">只看急聘职位</view>
            <view class="sdr"
              ><switch :checked="emergencyChecked" class="switch" @change="switchChange"
            /></view>
          </view>
          <view class="leibie clearfix">
            <view class="sdl">行业类型</view>
            <view class="sdr">
              <sele-industry-picker
                title="期望行业"
                :industry-name="industryName"
                :max-count="3"
                :hide-value="industryValue"
                :type="1"
                text-align="right"
                @change="bindIndustry"
                @open="open"
                @close="close"
                @delItem="delItem"
              />
            </view>
          </view>
          <view v-if="isPosition" class="leibie clearfix">
            <view class="sdl">职位类别</view>
            <view class="sdr">
              <sele-position-picker
                title="职位类别"
                left-text="职位类别"
                :position-name="positionName"
                :max-count="3"
                :hide-value="positionValue"
                :type="2"
                text-align="right"
                @open="open"
                @close="close"
                @change="bindPosition"
              />
            </view>
          </view>
        </view>
      </scroll-view>
      <!--  #ifdef  MP-ALIPAY -->

      <view class="btn-wrap" :style="`z-index:${showChild ? 10 : 99}`">
        <view class="box">
          <view class="reset" @click="resetAll()"><button class="btn btn-l">重置</button></view>
          <view class="sure" @click="sureAll()"
            ><button class="btn btn-r">
              {{ screenSelectedCount > 0 ? '确定(' + screenSelectedCount + ')' : '确定' }}
            </button></view
          >
        </view>
      </view>
      <!--  #endif -->
      <!--  #ifdef MP-WEIXIN ||  MP-TOUTIAO -->
      <view class="btn-wrap">
        <view class="box">
          <view class="reset" @click="resetAll()"><button class="btn btn-l">重置</button></view>
          <view class="sure" @click="sureAll()"
            ><button class="btn btn-r">
              {{ screenSelectedCount > 0 ? '确定(' + screenSelectedCount + ')' : '确定' }}
            </button></view
          >
        </view>
      </view>
      <!--  #endif -->
    </view>
  </uni-drawer>
</template>
<script lang="ts">
  export default {
    options: { styleIsolation: 'shared' }
  }
</script>
<script lang="ts" setup>
  import { onLoad } from '@dcloudio/uni-app'
  import { Position } from '@/services/home/<USER>'
  import { computed, watch, nextTick, onMounted, inject } from 'vue'
  import deepClone from '@/utils/deepClone'
import { Options } from '@/services/my/Options';
import { KeywordItemDto } from '@/services/home/<USER>';
  // import { storeToRefs } from 'pinia'
  // import { useAppStore } from '@/store/modules/app'
  // const store = useAppStore()
  const menuButtonInfo = uni.getMenuButtonBoundingClientRect()
  interface dataList {
    isPosition?: boolean
  }
  const { isPosition = false } = defineProps<dataList>()
  const showDrawer = $ref<any>(null)

  // #ifdef MP-WEIXIN
  const height3 = menuButtonInfo.top
  // #endif

  // #ifdef MP-TOUTIAO || MP-ALIPAY
  const height3 = 0

  // #endif

  let showChild = $ref(false)

  const open = () => {
    showChild = true

    emit('opens')
  }
  const close = () => {
    showChild = false
    emit('closes')
  }

  let screenData = $ref<Array[]>([]) //数据整合
  let positionScreenData = $ref<Array[]>([]) //数据整合---职位--空数据
  let enterpriseScreenData = $ref<Array[]>([]) //数据整合---公司---空数据
  let positionScreenDataED = $ref<Array[]>([]) //数据整合---职位--存储过的数据
  let enterpriseScreenDataED = $ref<Array[]>([]) //数据整合---公司---存储过的数据
  let emergencyChecked = $ref<boolean>(false) //急聘
  let industryName = $ref<any>()
  let industryValue: Array<number> = []
  let positionName = $ref<any>()
  let positionValue: Array<number> = []
  let screenSelected: Array<number> = []
  let screenQuery = $ref<any>()
  let isLoading = $ref<boolean>(false) //是否加载过了
  let positionTypeList = $ref<KeywordItemDto[]>([])
  let industryTypeList = $ref<KeywordItemDto[]>([])
  watch(
    () => isPosition,
    () => {
      hasDAta()
      Count()
    }
  )
  let screenSelectedCount = 0
  const Count = () => {
    let aLeng = 0
    let len = 0
    if (isPosition) {
      screenSelected.forEach((target: any) => {
        if (target.type !== 'emergency' && target.type !== 'enterpriseEmployeeNumbers') {
          len = parseInt(target.value.length)
          aLeng = aLeng + len
        }
      })
      if (emergencyChecked) {
        aLeng += 1
      }
    } else {
      screenSelected.forEach((target: any) => {
        if (target.type == 'enterpriseProperty' || target.type == 'enterpriseEmployeeNumbers' || target.type == 'positionIndustry') {
          len = parseInt(target.value.length)
          aLeng = aLeng + len
        }
      })
    }

    screenSelectedCount = aLeng
  }
  const initScreenSelected = [
    { type: 'salary', value: [] },
    { type: 'education', value: [] },
    { type: 'workAge', value: [] },
    { type: 'enterpriseProperty', value: [] },
    { type: 'welfare', value: [] },
    { type: 'workProperty', value: [] },
    { type: 'emergency', value: '' },
    { type: 'positionIndustry', value: [] },
    { type: 'positionCaree', value: [] },
    { type: 'enterpriseEmployeeNumbers', value: [] }
  ]
  const emit = defineEmits<{
    (e: ['changeFilter', 'clickFilter', 'opens', 'closes']): void
  }>()

  // #ifdef MP-WEIXIN
  const scrollHeight = computed(() => {
    return uni.getSystemInfoSync().windowHeight - menuButtonInfo.top - 104
  })
  // #endif

  // #ifdef  MP-ALIPAY || MP-TOUTIAO
  const paddingValue = uni.upx2px(200)
  const scrollHeight = computed(() => {
    return uni.getSystemInfoSync().windowHeight - menuButtonInfo.top - 104 - 108
  })
  // #endif
  //console.log(uni.getSystemInfoSync())

  const windowWidth = computed(() => {
    return uni.getSystemInfoSync().windowWidth || '320'
  })

  const openDrawer = () => {
    showDrawer.open()
    emit('opens')
    emit('clickFilter', true) //用于禁止页面滚动
    hasDAta()
  }
  const closeDrawer = () => {
    emit('closes')
    showDrawer.close()
    emit('clickFilter', false)
  }
 
  // #ifdef MP-TOUTIAO || MP-ALIPAY || MP-WEIXIN
  const option = inject('option')
  onMounted(() => {
    // #ifdef MP-TOUTIAO || MP-WEIXIN
    const option = inject('option')
    // #endif

    screenQuery = option
    screenSelected = deepClone(initScreenSelected)
    getDate()

    if (screenQuery.workProperty) {
      let workPropertyAry = []
      if(screenQuery.workProperty.indexOf(',')>-1){
        workPropertyAry = screenQuery.workProperty.split(",")
        screenSelectedCount =screenSelectedCount+workPropertyAry.length
      }else{
        workPropertyAry = [screenQuery.workProperty]
        screenSelectedCount = screenSelectedCount+1
      }
    }

    
  })
  // #endif

  const getDate = async () => {
    if (isLoading) {
      return false
    }
    const Data: Array = await Position.SearchOptionList({ districtId: 0, from: 2 })
    let screen: Array<[]> = []
    let screen2: Array<[]> = []
    screen.push({
      type: 'salary',
      title: '薪资范围',
      list: []
    })
    Data.paypackages.forEach((item: any) => {
      let selected = false
      if (item.name == '不限') {
        selected = true
      }
      screen[0].list.push({
        id: item.id,
        name: item.name,
        selected: selected
      })
    })
    screen.push({
      type: 'workProperty',
      title: '招聘类型',
      list: []
    })
    // #ifdef MP-TOUTIAO
    Data.workProperties = Data.workProperties.filter((item: any) => item.id != 916)
    // #endif
    Data.workProperties.forEach((item: any) => {
      let selected = false
      if (item.name == '不限') {
        selected = true
      }
      screen[1].list.push({
        id: item.id,
        name: item.name,
        selected: selected
      })
    })
    
    screen.push({
      type: 'education',
      title: '学历要求',
      list: []
    })
    Data.degrees.forEach((item: any) => {
      let selected = false
      if (item.name == '不限') {
        selected = true
      }
      screen[2].list.push({
        id: item.id,
        name: item.name,
        selected: selected
      })
    })
    screen.push({
      type: 'workAge',
      title: '经验要求<span>（单选）</span>',
      list: []
    })
    Data.workAges.forEach((item: any) => {
      let selected = false
      if (item.name == '不限') {
        selected = true
      }
      screen[3].list.push({
        id: item.id,
        name: item.name,
        selected: selected
      })
    })
    screen.push({
      type: 'enterpriseProperty',
      title: '单位性质',
      list: []
    })
    Data.enterpriseProperties.forEach((item: any) => {
      let selected = false
      if (item.keywordName == '不限') {
        selected = true
      }
      screen[4].list.push({
        id: item.keywordID,
        name: item.keywordName,
        selected: selected
      })
    })
    screen.push({
      type: 'welfare',
      title: '公司福利',
      list: []
    })
    Data.welfares.forEach((item: any) => {
      let selected = false
      if (item.keywordName == '不限') {
        selected = true
      }
      screen[5].list.push({
        id: item.keywordID,
        name: item.keywordName,
        selected: selected
      })
    })
    positionScreenData = deepClone(screen)
    positionScreenDataED = deepClone(screen)
    screen2.push({
      type: 'enterpriseProperty',
      title: '单位性质',
      list: []
    })
    Data.enterpriseProperties.forEach((item: { keywordName: string; keywordID: any }) => {
      let selected = false
      if (item.keywordName == '不限') {
        selected = true
      }
      screen2[0].list.push({
        id: item.keywordID,
        name: item.keywordName,
        selected: selected
      })
    })
    screen2.push({
      type: 'enterpriseEmployeeNumbers',
      title: '公司规模',
      list: []
    })
    Data.enterpriseEmployeeNumbers.forEach((item: any) => {
      let selected = false
      if (item.keywordName == '不限') {
        selected = true
      }
      screen2[1].list.push({
        id: item.keywordID,
        name: item.keywordName,
        selected: selected
      })
    })
    enterpriseScreenData = deepClone(screen2)
    enterpriseScreenDataED = deepClone(screen2)
    isLoading = true

    positionTypeList = await Options.optionsPositionList({})
    if (screenQuery.positionCaree) {
      positionName = screenQuery.positionCareeName
      if(screenQuery.positionCaree.indexOf(',')>-1){
        positionValue = screenQuery.positionCaree.split(",")
        screenSelectedCount =screenSelectedCount+positionValue.length
      }else{
        positionValue = [screenQuery.positionCaree]
        screenSelectedCount = screenSelectedCount+1
      }
      if(!positionName){
        const arr = []
        for(let index of positionValue){
          const item = positionTypeList.find(item=> item.keywordID == index)
          if(item) arr.push(item.keywordName)
        }
        positionName = arr.join(',')
      }
      screenSelected[8].value =positionValue
    }
    industryTypeList = await Options.optionsIndustryList({})

    if (screenQuery.positionIndustry) {
      industryName = screenQuery.positionIndustryName
      if(screenQuery.positionIndustry.indexOf(',')>-1){
        industryValue = screenQuery.positionIndustry.split(",")
        screenSelectedCount =screenSelectedCount+industryValue.length
      }else{
        industryValue = [screenQuery.positionIndustry]
        screenSelectedCount = screenSelectedCount+1
      }
      if(!industryName){
        const arr = []
        for(let index of industryValue){
          const item = industryTypeList.find(item=> item.keywordID == index)
          if(item) arr.push(item.keywordName)
        }
        industryName = arr.join(',')
      }
      screenSelected[7].value = industryValue
    }
  }
  const hasDAta = () => {
    if (isPosition) {
      screenData = positionScreenDataED

      if (screenSelected[5].value.length==0&&screenQuery.workProperty) {
        screenSelectedCount=0
        if(screenQuery.workProperty.indexOf(',')>-1){
         let workPropertyAry = screenQuery.workProperty.split(",")
         for(let i=0;i<workPropertyAry.length;i++){
          screenData.forEach(element1 => {
            if(element1.type=='workProperty'){
              element1.list.forEach(element2 => {
            if(element2.id==workPropertyAry[i]){
              screenLiClick('招聘类型', element2.id, element2.name, false)
            }
        });
            }
        });
         }
      }else{
        if (screenQuery?.workProperty == -1) {
          screenLiClick('招聘类型', -1, '毕业生', false)
        } else if (screenQuery?.workProperty == 1009) {
          screenLiClick('招聘类型', 1009, '实习', false)
        } else if (screenQuery?.workProperty == 915) {
          screenLiClick('招聘类型', 915, '全职', false)
        } else if (screenQuery?.workProperty == 916) {
          screenLiClick('招聘类型', 916, '兼职', false)
        }
      }
    }
    } else {
      screenData = enterpriseScreenDataED
    }
  }

  const screenLiClick = (title: string, id: number, name: string, selected: boolean) => {
    if (name == '不限') {
      screenData.forEach((item: any) => {
        if (item.title.indexOf(title) > -1) {
          item.list.forEach((element: any) => {
            element.selected = false
          })
          item.list[0].selected = true

          screenSelected.forEach((target) => {
            if (item.type == target.type) {
              target.value = []
            }
          })
        }
      })
    } else {
      if (title.indexOf('单选') > -1) {
        screenData.forEach((item: any) => {
          if (item.title.indexOf(title) > -1) {
            item.list.forEach((element: any) => {
              if (selected) {
                if (element.id == id && element.name == name) {
                  element.selected = false
                  screenSelected.forEach((target) => {
                    if (item.type == target.type) {
                      target.value = []
                    }
                  })
                }
                item.list[0].selected = true
              } else {
                element.selected = false
                if (element.id == id && element.name == name) {
                  element.selected = true
                  screenSelected.forEach((target) => {
                    if (item.type == target.type) {
                      target.value = []
                      target.value.push(id)
                    }
                  })
                }
              }
            })
          }
        })
      } else {
        //多选
        screenData.forEach((item: any) => {
          if (item.title.indexOf(title) > -1) {
            item.list.forEach((element: any) => {
              if (selected) {
                if (element.id == id && element.name == name) {
                  element.selected = false
                  screenSelected.forEach((target) => {
                    if (item.type == target.type) {
                      const index = target.value.findIndex((target2: number) => target2 == id)
                      target.value.splice(index, 1)
                    }
                  })
                }
                if (JSON.stringify(item.list).indexOf('true') < 0) {
                  item.list[0].selected = true
                }
              } else {
                if (element.id == id && element.name == name) {
                  element.selected = true
                  screenSelected.forEach((target) => {
                    if (item.type == target.type) {
                      target.value.push(id)
                    }
                  })
                }
                item.list[0].selected = false
              }
            })
          }
        })
      }
    }
    Count()
  }
  const resetAll = () => {
    if (isPosition) {
      screenData = deepClone(positionScreenData)
    } else {
      screenData = deepClone(enterpriseScreenData)
    }

    emergencyChecked = false
    industryName = ''
    industryValue = []
    positionName = ''
    positionValue = []
    screenSelected = deepClone(initScreenSelected)
    Count()
  }
  const sureAll = () => {
    let json = {}
    if (screenSelected[0].value.length > 0) {
      json.salary = screenSelected[0].value
    }
    if (screenSelected[1].value.length > 0) {
      json.education = screenSelected[1].value
    }
    if (screenSelected[2].value.length > 0) {
      json.workAge = screenSelected[2].value[0]
    }
    if (screenSelected[3].value.length > 0) {
      json.enterpriseProperty = screenSelected[3].value
    }
    if (screenSelected[4].value.length > 0) {
      json.welfare = screenSelected[4].value
    }
    if (screenSelected[5].value.length > 0) {
      json.workProperty = screenSelected[5].value
    }
    if (screenSelected[6].value != '') {
      json.emergency = screenSelected[6].value
    }
    if (screenSelected[7].value.length > 0) {
      json.positionIndustry = screenSelected[7].value
    }
    if (screenSelected[8].value.length > 0) {
      json.positionCaree = screenSelected[8].value
    }
    if (screenSelected[9].value.length > 0) {
      json.enterpriseEmployeeNumbers = screenSelected[9].value
    }
    closeDrawer()
    emit('changeFilter', json)

    if (isPosition) {
      positionScreenDataED = screenData
      //单独处理单位性质
      enterpriseScreenDataED[0] = positionScreenDataED[4]
    } else {
      enterpriseScreenDataED = screenData
      positionScreenDataED[4] = enterpriseScreenDataED[0]
    }
  }
  const switchChange = (e: any) => {
    emergencyChecked = !emergencyChecked
    screenSelected[6].value = emergencyChecked ? 1 : ''
    Count()
  }
  //行业
  const bindIndustry = (industry: any) => {
    let names = industry.map((i: any) => i.keywordName).join(',') //type==1用这个
    let ids: Array = []
    industry.forEach((i: any) => {
      ids.push(i.keywordID)
    })
    screenSelected[7].value = ids
    industryName = names
    industryValue = ids
    Count()
  }
  //职位
  const bindPosition = (citys: any) => {
    let names = citys.map((i: any) => i.keywordName).join(',')
    let ids: Array = []
    citys.forEach((i: any) => {
      ids.push(i.keywordID)
    })
    positionName = names
    positionValue = ids
    screenSelected[8].value = ids
    Count()
  }
  const delItem = (index: number) => {
    console.log('没有')
  }
</script>

<style lang="scss" scoped>
  $blue: #5e8df5;
  .filter {
    float: right;
    height: 60rpx;
    line-height: 60rpx;
    font-size: 24rpx;
    padding: 0 14rpx 0 20rpx;
    text-align: center;
    background: #f5f7fa;
    border-radius: 4rpx;
    color: #bbb;
    margin-left: 20rpx;
    min-width: 120rpx;

    .icon-arrowDown5 {
      font-size: 24rpx;
      float: right;
    }
  }
  .bg-blue {
    background: #eef5ff;
    color: #457ccf;
  }
  .filter-pop {
    /*  #ifdef MP-ALIPAY */
    padding-top: 100rpx;
    /*  #endif  */
    .con {
      overflow-y: scroll;
      position: relative;
      z-index: 66;
      /*  #ifdef MP-ALIPAY  */
      padding-bottom: 54px;
      /*  #endif  */
      .one {
        padding: 20rpx;
        .hd {
          height: 80rpx;
          line-height: 80rpx;
          margin-bottom: 20rpx;
          font-size: 32rpx;
          font-weight: 700;
        }
        .li {
          float: left;
          width: 206rpx;
          height: 66rpx;
          line-height: 66rpx;
          font-size: 26rpx;
          color: #999;
          background: #f5f7fa;
          border-radius: 8rpx;
          text-align: center;
          margin: 0 20rpx 20rpx 0;
        }
        .selected {
          color: #457ccf;
          background: #eef5ff;
        }
      }
      .two {
        padding: 20rpx;
        .jinpin {
          border-bottom: 1px solid #e7e7e7;
          line-height: 120rpx;
        }
        .switch {
          transform: scale(0.75);
        }
        .sdl {
          float: left;
          font-size: 32rpx;
          font-weight: 700;
        }
        .sdr {
          float: right;
        }
        .leibie {
          border-bottom: 1px solid #e7e7e7;
          line-height: 120rpx;
          display: flex;
          .sdl {
            flex: 2;
          }
          .sdr {
            width: 300rpx;
            padding-top: 24rpx;
            flex: 2;
          }
          ::v-deep.uni-navbar {
            z-index: 60;
          }
        }
      }
    }
  }
  .btn-wrap {
    position: fixed;
    width: 100%;
    bottom: 0;
    left: 0;
    z-index: 8;
    background: #fff;
    /*  #ifdef MP-ALIPAY */
    z-index: 99;
    /*  #endif  */
    .box {
      padding: 5px;
      display: flex;
    }
    .reset {
      flex: 1;
      padding-right: 10px;
    }
    .sure {
      flex: 2;
    }
    .btn {
      width: 100%;
      height: 44px;
      line-height: 44px;
      border-radius: 5px;
      font-size: 14px;
    }
    .btn-l {
      background: #fff;
      color: #457ccf;
      border: 1px solid #457ccf;
    }
    .btn-r {
      border: 1px solid #457ccf;
      background: #457ccf;
      color: #fff;
    }
  }
  .uni-navbar {
    .uni-navbar--fixed {
    }
    .uni-navbar__header {
      display: flex;
      padding: 0 10px;
      flex-direction: row;
      height: 44px;
      line-height: 44px;
      color: #333;
      background-color: #fff;
    }
    .uni-navbar__header-btns {
      flex-wrap: nowrap;
      flex-direction: row;
      overflow: hidden;
      display: flex;
      width: 60px;
      align-items: center;
      .iconfont {
        font-family: uniicons;
        text-decoration: none;
        text-align: center;
        color: #333;
        font-size: 15px;
      }
    }
    .uni-navbar__header-btns-left {
      justify-content: flex-start;
    }
    .uni-navbar__header-btns-right {
      justify-content: flex-end;
    }
    .uni-navbar__header-container {
      display: flex;
      flex: 1;
      padding: 0 10px;
      overflow: hidden;
    }
    .uni-navbar__header-container-inner {
      display: flex;
      flex: 1;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      overflow: hidden;
      .uni-nav-bar-text {
        font-size: 14px;
      }
    }
  }
</style>
