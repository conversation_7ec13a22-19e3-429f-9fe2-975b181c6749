<template>
  <view>
 
      <!-- #ifdef MP-ALIPAY  -->
      <view
      class="position-search-top"
      :style="`top:${docTop}px` + ';' + `z-index:${showChild ? 10 : 60}`"
    >
       <!--  #endif -->

        <!-- #ifdef MP-WEIXIN  || MP-TOUTIAO  -->
        <view
      class="position-search-top"
      :style="`top:${docTop}px`"
    >
        <!--  #endif -->
      <view class="position-search-box clearfix">
        <view class="city clearfix">
          <view class="cy">
            <search-city-picker
              title="选择城市"
              text-align="left"
              :city-name="cityName"
              :max-count="1"
              :hide-value="hideValue"
              @open="openSearchTop"
              @close="closeSearchTop"
              @change="bindChange"
            />
          </view>
        </view>
        <view class="position-search-inp">
          <view class="search-inp-box">
            <uni-easyinput
              :styles="{ background: 'rgba(255, 255, 255, 0)' }"
              :value="keyword"
              :placeholder="placeholder"
              :input-border="false"
              :clearable="true"
              confirm-type="search"
              :focus="focus"
              @confirm="Search"
              @input="changeInput"
            ></uni-easyinput>
          </view>
        </view>
      </view>
      <view class="position-search-cancel" @click="Search(keyword)">搜索</view>
    </view>
    <!-- 搜索列表 -->
    <view v-if="hasSearch" class="positon-autocomplete">
      <view class="search-ul">
        <view
          v-for="item in searchList"
          :key="item.id"
          class="search-li"
          @click="Search(item.text)"
        >
          {{ item.text }}
        </view>
      </view>
    </view>
  </view>
</template>
<script lang="ts">
  export default {
    inheritAttrs: false,
    options: { styleIsolation: 'shared' }
  }
</script>
<script lang="ts" setup>
  import { AutoComplete } from '@/services/home/<USER>'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import { useAppStore } from '@/store/modules/app'
  import { storeToRefs } from 'pinia'
  import { computed, onMounted, watch } from 'vue'
  import { Data } from '@/services/home/<USER>'

  interface Props {
    type?: 1 | 2
    keyword?: ''
    cityId?: 2
    cityName?: '南宁'
    placeholder?: string
  }

  let hideValue: Array<number> = [227]
  let cityName = $ref<any>('全国')
  let searchList = $ref<Array<string>>([])
  let hasSearch = $ref<boolean>(false)
  let keyword = $ref<any>()
  let focus = $ref<boolean>(false)
  const emit = defineEmits<{
    onSearch: [value: boolean]
    changeCity: [citys: any]
    goResult: [keyword: string]
  }>()
  const store = useAppStore()
  const { statusNavBarHeight } = storeToRefs(store)
  
  // #ifdef MP-WEIXIN
  const docTop = statusNavBarHeight
  // #endif

  // #ifdef  MP-ALIPAY
  const docTop = computed(() => {
    return statusNavBarHeight.value - 5
  })
  // #endif

    // #ifdef MP-TOUTIAO
    const docTop = 0
  // #endif

  let showChild = $ref(false)

  const openSearchTop = () => {
    showChild = true
  }
  const closeSearchTop = () => {
    showChild = false
  }

  const isLogin = $computed(()=> store.isLogin)
  const props = defineProps<Props>()
  const { type = 1 } = props
  
  
  const placeholder = $computed(()=>{
    return props.placeholder || '搜索职位或公司'
  })
  // #ifdef MP-WEIXIN
  onLoad(async (option) => {
    const district = uni.getStorageSync('district')

    keyword = option?.keyword || ''
    cityName = option?.cityName || district.name || '南宁'


    const id: number = parseInt(option?.cityId) || 2
    hideValue = [id]
    let routes = getCurrentPages()
    let curRoute = routes[routes.length - 1].route
    if (curRoute == 'pages/position/index') {
      setTimeout(() => {
        focus = true
      }, 500)
    }
  })
  onShow(async()=>{
    let routes = getCurrentPages()
    let curRoute = routes[routes.length - 1].route
    if (curRoute == 'pages/position/index') {
      setTimeout(() => {
        focus = true
      }, 500)
    }
  })
  // #endif

  // #ifdef  MP-TOUTIAO || MP-ALIPAY
  onMounted(async () => {
    let routes = getCurrentPages()
    let curRoute = routes[routes.length - 1].route
    if (curRoute == 'pages/position/index') {
      setTimeout(() => {
        focus = true
      }, 500)
    }
  })

  watch(
    () => props,
    () => {
      const district = uni.getStorageSync('district')
      keyword = props.keyword || ''
      cityName = props.cityName || district.name || '南宁'

      const id: number = parseInt(props.cityId?.toString() || '2') || 2
      hideValue = [id]
    },
    {
      deep: true,
      immediate: true
    }
  )
  // #endif

  // 搜索历史数据结构接口
  interface SearchHistoryItem {
    keyword: string
    count: number
    lastSearchTime: number
  }

 

  const Search = (e: any) => {
    if(e != ''){
      // 获取现有搜索历史（新数据结构）
      const history: SearchHistoryItem[] = uni.getStorageSync('search_history') || []

      // 查找是否已存在该关键词
      const existingIndex = history.findIndex(item => item.keyword === e)

      if (existingIndex !== -1) {
        // 如果关键词已存在，增加搜索次数并移到最前面
        const existingItem = history[existingIndex]
        existingItem.count += 1
        existingItem.lastSearchTime = Date.now()

        // 移除原位置的记录
        history.splice(existingIndex, 1)
        // 添加到最前面
        history.unshift(existingItem)
      } else {
        // 如果是新关键词，创建新记录并添加到最前面
        const newItem: SearchHistoryItem = {
          keyword: e,
          count: 1,
          lastSearchTime: Date.now()
        }
        history.unshift(newItem)
      }

      // 限制历史记录数量（可根据需要调整）
      if (history.length > 10) {
        history.splice(10)
      }

      // 保存更新后的历史记录
      uni.setStorageSync('search_history', history)
    }

    keyword = e
    let tinyKeyword = e ? e : props.placeholder
    emit('goResult', tinyKeyword)
    hasSearch = false
    emit('onSearch', false)
  }
  const changeInput = async (e: any) => {
    if(e == ''){
      placeholderKeyword = ''
    }
    keyword = e
    await getDate(e)
  }

  const bindChange = (citys: any) => {
    let ids: Array<number> = []
    citys.forEach((i: any) => {
      ids.push(i.keywordID)
    })
    hideValue = ids
    cityName = citys[0].keywordName
    emit('changeCity', citys)
  }
  const getDate = async (key: any) => {
    let DAtalist = await AutoComplete.MatchList({ keyword: key })
    searchList = DAtalist
    if (DAtalist.length > 0) {
      hasSearch = true
      emit('onSearch', type === 1 ? true : false)
    } else {
      hasSearch = false
      emit('onSearch', false)
    }
  }
  const goHome = () => {
    uni.switchTab({
      url: '/pages/index/index'
    })
  }
  const navigateTo = (url: string) => {
    uni.navigateTo({
      url
    })
  }
</script>

<style lang="scss" scoped>
  .position-search-top {
    display: flex;
    padding: 20rpx 0 20rpx 20rpx;
    position: fixed;
    left: 0;
    width: 100%;
    background: #fff;
    z-index: 480;
    /*  #ifdef MP-ALIPAY */
    z-index: 9;
    /*  #endif  */
  }

  .position-search-box {
    position: relative;
    flex: 7;
    background-color: #f5f7fa;
    padding: 0 0rpx 0 10rpx;
    border-radius: 4px;
  }

  .position-search-inp {
    width: 100%;
    float: left;
    font-size: 28rpx;
    ::v-deep.uni-easyinput__content {
      height: 80rpx;
    }
    ::v-deep.uni-easyinput__content-input {
      padding-left: 0px !important;
      font-size: 28rpx;
      height: 80rpx;
    }
    .search-inp-box {
      margin-left: 160rpx;
      ::v-deep .content-clear-icon {
        .uni-icons {
          font-size: 40rpx !important;
        }
      }
    }
  }

  .position-search-cancel {
    flex: 2;
    text-align: center;
    line-height: 80rpx;
    font-size: 28rpx;
    color: #333;
  }

  .city {
    width: 160rpx;
    float: left;
    margin-right: -160rpx;
    height: 80rpx;
    line-height: 80rpx;

    .cy {
      float: left;
      font-size: 28rpx;
      overflow: hidden;
      position: relative;
      z-index: 9;
      ::v-deep.picker-input {
        line-height: 80rpx;
        height: 80rpx;
      }
    }

    .vl {
      float: left;
      color: #d9d9d9;
      font-size: 24rpx;
    }

    .iconfont {
      float: left;
      color: #d9d9d9;
      font-size: 20rpx;
      padding: 0 10rpx;
    }
  }
  .positon-autocomplete {
    .search-ul {
      padding: 100rpx 40rpx 20rpx;
    }
    .search-li {
      height: 100rpx;
      line-height: 100rpx;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      color: #333333;
      font-size: 32rpx;
      border-bottom: 1px solid #eeeeee;
    }
  }
  ::v-deep .uni-easyinput__placeholder-class {
    font-size: 28rpx !important;
  }
</style>
