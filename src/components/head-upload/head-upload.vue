<template>
  <view class="headIMG">
    <image :src="modelValue" class="photo" mode="aspectFill" @tap="headPhoto" />
  </view>
</template>
<script lang="ts" setup>
  import { Photo } from '@/services/my/Photo'
  import { baseUrl } from '@/services/my/data-contracts'
  interface dataList {
    avatar?: string
    modelValue?: string
  }
  // eslint-disable-next-line vue/no-setup-props-destructure
  const { avatar = 'https://image.gxrc.com/gxrcsite/webApp/zc1.png', modelValue } =
    defineProps<dataList>()
  const headPhoto = () => {
    uni.chooseImage({
      count: 1, //默认9
      sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
      sourceType: ['album'], //从相册选择
      success: function (res) {
        const tempFilePaths = res.tempFilePaths
        upDAta(tempFilePaths)
        uni.uploadFile({
          url: baseUrl + `/api/photo/uploadavatar`, //仅为示例，非真实的接口地址
          filePath: tempFilePaths[0],
          name: 'file',
          formData: {
            image: tempFilePaths[0] // 上传附带参数
          },
          success: (uploadFileRes) => {
            // 根据接口具体返回格式   赋值具体对应url
            console.log('success2', uploadFileRes.data)
          },
          fail: function (err) {
            console.log('错误2', err.errMsg)
          }
        })
      },

      fail: function (err) {
        console.log('错误1', err.errMsg)
      }
    })
  }
  const upDAta = async (tempFilePaths: string | string[]) => {
    const data = await Photo.photoUploadavatar({ image: tempFilePaths[0] })
    console.log('返回的数据', data)
  }
</script>
<style lang="scss" scoped>
  .headIMG {
    width: 140rpx;
    height: 140rpx;
    float: right;
    .photo {
      width: 100%;
      height: 100%;
    }
  }
</style>
