<template>
  <view class="position-picker clearfix">
    <view class="picker-input" @tap="selecity()">
      <text :class="[textAlign, positionName ? 'uni-ellipsis-1' : 'uni-ellipsis-1 pleaseChoose']">{{
        positionName || '请选择'
      }}</text>
      <slot />
      <i class="iconfont icon-arrowRight14 icon"></i>
    </view>
    <uni-popup ref="showRight" type="right" :is-mask-click="true" class="pop-industry-box">
      <view class="bg-white h100">
        <uni-nav-bar
          :left-text="leftText"
          left-width="200rpx"
          height="44px"
          :fixed="true"
          :status-bar="true"
          left-icon="left"
          :border="false"
          @clickLeft="closeDrawer"
        />
        <view class="search-bar bg-white" @click="openSearch">
          <view class="search-skip">
            <view class="cot">
              <i class="iconfont icon-search1"></i>
              <text class="txt">搜索职位类别</text>
            </view>
          </view>
        </view>
        <view v-if="maxCount > 1" class="selected-wrap bg-white">
          <view class="count"
            >已选择职位( <label class="bule">{{ selectedCount }}</label
            >/{{ maxCount }})</view
          >
          <view class="selected-items clearfix">
            <view
              v-for="val in activeGroup"
              :key="val.keywordID"
              class="item"
              @click="deleteCity(val)"
            >
              {{ val.keywordName }}
              <i class="iconclose">X</i>
            </view>
          </view>
        </view>
        <view class="sel-main clearfix bg-white" :style="`width:` + width + 'px'">
          <!-- 第一组 -->
          <view class="sel-position-box sel-position-boxA">
            <scroll-view
              :style="{ height: scrollHeight1 + 'px' }"
              scroll-y="true"
              :show-scrollbar="false"
            >
              <view class="ul">
                <view
                  v-for="(item, index) in firstArr"
                  :key="index"
                  clickable
                  class="li clearfix"
                  @click="cityChooseA(item)"
                >
                  <view class="sdl">{{ item.keywordName }}</view>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>

        <view
          v-if="show"
          :class="show ? 'children-picker slide-right' : 'children-picker slide-left'"
          class="sel-main-2"
          @click.self="show = false"
        >
          <view class="sel-main clearfix bg-white children-picker-body">
            <!-- 第二组 -->
            <view class="sel-position-box sel-position-boxB">
              <scroll-view :style="{ height: scrollHeight1 + 'px' }" scroll-y="true">
                <view class="ul">
                  <view
                    v-for="(item, index) in secondArr"
                    :key="index"
                    :class="['li clearfix', { on: item.keywordID == isIDB }]"
                    @click.stop="cityChooseB(item, index)"
                  >
                    <view class="sdl">{{ item.keywordName }}</view>
                  </view>
                </view>
              </scroll-view>
            </view>
            <!-- 第三组 -->
            <view class="sel-position-box sel-position-boxC">
              <view class="city-boxC">
                <scroll-view :style="{ height: scrollHeight1 + 'px' }" scroll-y="true">
                  <view class="ul">
                    <view
                      v-for="(item, index) in thirdArr"
                      :key="index"
                      clickable
                      :class="['li clearfix', { isseled: item.selected }]"
                      @click.stop="cityChooseC(item)"
                    >
                      <view class="sdl">
                        <view v-if="type === 1" class="sdl">{{ item.keywordName }}</view>
                        <view v-else class="sdl">{{ index == 0 ? '全部' : item.keywordName }}</view>
                      </view>
                      <view class="sdr">
                        <i v-if="item.selected" class="hook"
                          ><uni-icons type="checkmarkempty" color="#5e8df5" size="16"></uni-icons
                        ></i>
                      </view>
                    </view>
                  </view>
                </scroll-view>
              </view>
            </view>
          </view>
        </view>
        <view class="btn-box" @click="bindComfirm">
          <button class="login-wrap-button">保存</button>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="popup" type="right">
      <view class="bg-white popup-search-box" :style="{ width: width + 'px' }">
        <uni-nav-bar
          title="搜索职位"
          left-width="200rpx"
          :fixed="true"
          :status-bar="true"
          left-icon="left"
          :border="false"
          @clickLeft="closeSearch"
        />
        <view class="search-cell">
          <view class="search-cell-box">
            <input
              ref="sesrchInp"
              class="uni-input"
              :focus="focus"
              :auto-blur="true"
              type="text"
              placeholder="搜索职位类别"
              confirm-type="search"
              @confirm="searchKey"
              @input="searchKey"
            />
          </view>
        </view>
        <scroll-view
          :style="{ height: scrollHeight + 'px' }"
          scroll-y="true"
          :show-scrollbar="false"
        >
          <view class="search-cell-group">
            <view
              v-for="i in IndustrySearchList"
              :key="i"
              class="item"
              @click="industryChoose(i.id)"
            >
              <view class="title">{{ i.name }}</view>
              <view class="subhead">{{ i.fullname }}</view>
            </view>
          </view>
          <view v-if="IndustrySearchList == ''" class="Nodata">
            <view class="title">搜索无数据哦，换个关键字吧！</view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>
  </view>
</template>
<script lang="ts">
  export default {
    options: { styleIsolation: 'shared' }
  }
</script>
<script lang="ts" setup>
  import { computed, watch, nextTick, getCurrentInstance } from 'vue'
  import { Options } from '@/services/my/Options'
  import { Autocompelete } from '@/services/my/Autocompelete'
  import { onLoad } from '@dcloudio/uni-app'
  import { KeywordItemDto } from '@/services/my/data-contracts'
  let statusBarheight = $ref<number>(0)
  uni.getSystemInfo({
    success: (e) => {
      let custom = uni.getMenuButtonBoundingClientRect()
      statusBarheight = custom.bottom + custom.top - e.statusBarHeight + 4
    }
  })
  interface dataList {
    hideValue: []
    title?: string
    maxCount?: number
    positionName?: string
    textAlign?: string
    type?: number
    leftText?: string
  }
  let allPosition = $ref<Array<number | string>>()
  let IndustrySearchList = $ref({})
  let firstArr = $ref<Array<number | string>>()
  let secondArr = $ref<Array<number | string>>()
  let thirdArr = $ref<Array[] | undefined | null>([])
  let activeGroup = $ref<Array[]>([])
  // let isIDA = $ref<any>(-1) //第一排高亮
  let isIDB = $ref<any>(-1) //第二排高亮
  let show = $ref<boolean>(false)
  let focus = $ref<boolean>(false)
  let navTop = uni.getMenuButtonBoundingClientRect().top

  const {
    hideValue,
    maxCount = 1,
    title = '请选择',
    positionName = '',
    textAlign = 'right',
    type = 1,
    leftText = '期望职位'
  } = defineProps<dataList>()
  const selectedCount = computed(() => {
    return activeGroup.length || 0
  })
  const canSelectMore = computed(() => {
    return maxCount <= selectedCount.value
  })
  const singleMode = computed(() => {
    return maxCount === 1
  })
  const instance = getCurrentInstance()
  let selectedH = $ref<number>(0)

  const scrollHeight1 = computed(() => {
    return uni.getSystemInfoSync().windowHeight - 180 - selectedH - navTop
  })

  const scrollHeight = computed(() => {
    return uni.getSystemInfoSync().windowHeight - 190
  })
  const width = computed(() => {
    return uni.getSystemInfoSync().windowWidth || '320'
  })
  const showRight = $ref<any>(null)
  const popup = $ref<any>(null)
  const popupB = $ref<any>(null)
  const message = $ref<any>(null)
  const sesrchInp = $ref<any>(null)
  const emit = defineEmits<{
    (e: 'change'): void
    (e: 'open'): void
    (e: 'close'): void
  }>()
  onLoad(async () => {
    selectedH = singleMode.value ? 0 : 35
    // console.log("selectedH======",wx.getSystemInfoSync().windowHeight,selectedH,navTop)
  })
  const getDate = async () => {
    activeGroup = []
    const Data: Array = await Options.optionsPositionList()
    Data.map((item: any, index: any) => {
      item.selected = false
    })
    allPosition = Data
    
    hideValue.forEach((id: any) => {
      let item = allPosition.find((p: any) => p.keywordID == id)
      if (!item) return
      item.selected = true
      addCity(item)
    })
    firstArr = Data.filter((i: any) => i.parentID == 0)
  }
  watch(
    () => selectedCount.value,
    () => {
      nextTick(() => {
        hideInspect()
      })
    }
  )
  const selecity = () => {
    getDate()
    showRight.open()
    emit('open')
  }
  const bindComfirm = () => {
    if (activeGroup.length < 1) {
      uni.showToast({
        title: '请至少选择一个',
        duration: 2000,
        icon: 'none'
      })

      return
    }
    emit('change', activeGroup)
    showRight.close()
    emit('close')
    show = false
  }

  const openSearch = () => {
    if((allPosition?.length || 0) <=0){
      uni.showToast({
        title: '职位数据未加载',
        icon: 'none'
      })
      return
    }
    popup.open('right')
    setTimeout(() => {
      focus = true
    }, 500)
  }
  const closeSearch = () => {
    popup.close('right')

    setTimeout(() => {
      focus = false
    }, 500)
  }

  const closeDrawer = () => {
    // console.log('取消')
    showRight.close()
    emit('close')
  }
  const getChildren = (pid: any, num: any) => {
    if (num == 2 && type === 2) {
      return allPosition.filter((i: any) => i.parentID == pid || i.keywordID == pid)
    } else {
      return allPosition.filter((i: any) => i.parentID == pid)
    }

    //return allPosition.filter((i: any) => i.parentID == pid)
  }
  const cityChooseA = (city: any) => {
    //第一行点击
    if (!city.hasNext) {
      onSelectCity(city)
      return false
    }
    secondArr = []
    thirdArr = []
    if (!city.hasNext) {
      //如果没有子集直接选取
      onSelectCity(city)
      return false
    }
    secondArr = getChildren(city.keywordID, 1)
    show = true
    isIDB = -1
  }
  const cityChooseB = (item: any, index: number) => {
    //第二行点击
    if (isIDB == item.keywordID) {
      return false
    }
    // activeIndex = -1;
    isIDB = item.keywordID
    thirdArr = []
    if (!item.hasNext) {
      //如果没有子集直接选取
      onSelectCity(item)
      return false
    }
    thirdArr = getChildren(item.keywordID, 2)
  }
  const cityChooseC = (city: any) => {
    onSelectCity(city)
  }
  const onSingleMode = (city: any) => {
    //单选模式
    allPosition.forEach((item: any) => {
      item.selected = false
    })
    thirdArr.forEach((item: any) => {
      item.selected = false
    })
    city.selected = true
    activeGroup = [city]
    bindComfirm()
  }
  const onMultipleMode = (city: any) => {
    //多选模式
    const isChecked = city.selected
    if (isChecked) {
      //被选过了
      deleteCity(city)
      return false
    }
    //如果没有被选过检测下面的子集和往上的父级
    if (!isChecked) {
      activeGroup.forEach((i: any) => {
        if (
          i.parentID == city.keywordID ||
          i.keywordID == city.parentID ||
          i.grandfather == city.keywordID ||
          i.keywordID == city.grandfather
        ) {
          deleteCity(i)
        }
      })
      if (canSelectMore.value) {
        //如果没选过而且超限了
        const messageText = `最多只能选择${maxCount}个选项`
        uni.showToast({
          title: messageText,
          duration: 2000,
          icon: 'none'
        })
      } else {
        addCity(city)
      }
    }
  }
  const addCity = (city: any) => {
    city.selected = true
    activeGroup.push(city)
  }
  const onSelectCity = (city: any) => {
    if (!city) return
    if (singleMode.value) {
      onSingleMode(city)
    } else {
      onMultipleMode(city)
    }
  }
  const deleteCity = (item: any) => {
    item.selected = false
    thirdArr.forEach((i: any) => {
      i.keywordID == item.keywordID ? (i.selected = false) : ''
    })
    activeGroup = activeGroup.filter((i: any) => i.keywordID !== item.keywordID)
  }
  //搜索
  const searchKey = async (event: { target: { value: any } }) => {
    const keyword = event.target.value
    IndustrySearchList = await Autocompelete.autocompeleteSearchposition({ keyword: keyword })
  }
  const industryChoose = (id: number) => {
    allPosition.forEach((item: any) => {
      if (item.keywordID === id) {
        cityChooseC(item)
      }
    })
    popup.close('right')
    setTimeout(() => {
      focus = false
    }, 500)
  }
  const hideInspect = () => {
    nextTick(() => {
      if (singleMode.value) {
        selectedH = 0
      } else {
        const query = uni.createSelectorQuery().in(instance)
        query
          .select('.selected-wrap')
          .boundingClientRect((res) => {
            selectedH = parseInt(res?.height)
          })
          .exec()
      }
    })
    return false
  }
</script>

<style lang="scss" scoped>
  $blue: #5e8df5;
  .h100 {
    height: 100%;
  }
  .position-picker {
    .picker-input {
      flex: 1;
      width: 100%;
      display: flex;
      box-sizing: border-box;
      min-height: 72rpx;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      position: relative;
      font-size: 28rpx;
      .left {
        position: absolute;
        left: 0;
        text-align: left;
      }
      .right {
        position: absolute;
        right: 38rpx;
        text-align: right;
      }
      .uni-ellipsis-1 {
        width: 100%;
        font-size: 15px;
      }
    }
    .icon {
      position: absolute;
      right: 0;
      font-size: 24rpx;
      color: #808080;
    }
  }
  .bg-white {
    background: #fff;
  }
  .pop-industry-box {
    width: 100%;
    .btn-box {
      background: #fff;
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      border-top: 1px solid #eee;

      .login-wrap-button {
        border: none;
        background: #5e8df5;
        color: #fff;
        margin: 10px 10px 30px;
        height: 40px;
        line-height: 40px;
        font-size: 16px;
      }
    }
    ::v-deep .uni-navbar__content_view {
      height: 40px;
      line-height: 40px;
    }
    ::v-deep .uni-navbar-btn-text {
      text {
        font-size: 14px !important;
      }
    }
  }

  /*  #ifdef MP-ALIPAY */
  .btn-box {
    background: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    border-top: 1px solid #eee;
    z-index: 99;

    .login-wrap-button {
      border: none;
      background: #5e8df5;
      color: #fff;
      margin: 10px;
      height: 40px;
      line-height: 40px;
      font-size: 16px;
    }
  }
  ::v-deep .uni-navbar__content_view {
    height: 40px;
    line-height: 40px;
  }
  ::v-deep .uni-navbar-btn-text {
    text {
      font-size: 14px !important;
    }
  }

  /*  #endif  */
  .selected-wrap {
    padding: 0px 15px;
    .count {
      font-size: 13px;
      color: #666;
      line-height: 24px;
    }
    .blue {
      color: #228efd;
    }
    .selected-items {
      padding: 2px 0 10px 0;
      .item {
        color: #228efd;
        font-size: 13px;
        padding: 0px 10px;
        float: left;
        margin: 5px 5px 0 0;
        display: flex;
        border: 1px solid #228efd;
        border-radius: 3px;
        line-height: 22px;
      }
      .iconclose {
        font-size: 14px;
        padding-left: 5px;
      }
    }
  }
  /*  #ifdef MP-ALIPAY */
  .sel-main-2 {
    top: 200rpx !important;
  }
  /*  #endif  */

  .sel-main {
    .sel-position-box {
      float: left;
      .li {
        padding: 26rpx 20rpx 26rpx 20rpx;
        color: #666;
        position: relative;
        line-height: 40rpx;
        .sdl {
          float: left;
          font-size: 26rpx;
        }
        .sdr {
          position: absolute;
          right: 10rpx;
          top: 50%;
          margin-top: -16rpx;
          .iconfont {
            font-size: 24rpx;
            line-height: 40rpx;
          }
        }
      }
      .isseled {
        color: $blue;
      }
    }
    .sel-position-boxA {
      width: 100%;
      background: #fff;
      .li {
        padding: 26rpx 20rpx 26rpx 20rpx;
      }
      line-height: 40rpx;
    }
    .sel-position-boxB {
      width: 240rpx;
      background: #f4f5f9;
      margin-right: -240rpx;
      position: relative;
      z-index: 1;
      .on {
        background: #fff;
        color: $blue;
        background: #fff;
        border-left: 2px solid $blue;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        padding: 20rpx 30rpx 20rpx 26rpx;
      }
    }
    .sel-position-boxC {
      width: 100%;
      background: #fff;
      .city-boxC {
        margin-left: 240rpx;
      }
    }
  }
  .uni-ellipsis-1 {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .search-bar {
    padding: 10px;
    .search-skip {
      background: #f5f7fa;
      border-radius: 20px;
      height: 35px;
      line-height: 35px;
      color: #999;
      font-size: 13px;
      padding: 0 15px;
    }
    .iconfont {
      font-size: 14px;
      padding-right: 5px;
      float: left;
    }
    .txt {
    }
  }

  .popup-search-box {
    height: 100%;
    .search-cell {
      padding: 10px 20px;
      &-box {
        border-bottom: 1px solid #457ccf;
        padding: 10px 0;
        font-size: 14px;
      }
      .uni-input {
        border: none;
        background: #fff;
      }
    }
    .search-cell-group {
      padding: 40rpx;
      .item {
        border-bottom: 1px solid #f2f2f2;
        text-align: left;
        font-size: 28rpx;
        padding: 14rpx 0;
        line-height: 40rpx;
      }
      .title {
        font-size: 28rpx;
      }
      .subhead {
        font-size: 24rpx;
        color: #bbb;
        padding: 10rpx 0 0 0;
      }
    }
  }
  .Nodata {
    text-align: center;
    font-size: 28rpx;
  }
  .children-picker {
    position: fixed;
    bottom: 82px;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: hidden;
    z-index: 80;

    /*  #ifdef MP-ALIPAY */
    position: absolute;
    top: 0;
    bottom: 0;

    /*  #endif  */
    .children-picker-body {
      margin-left: 80rpx;
      background-color: #fff;
      height: 100%;
      display: flex;
      flex-flow: row;
    }
  }
  .slide-right {
    -webkit-transform: translateX(0%);
    transform: translateX(0%);
    transition: 1s;
  }
  .slide-left {
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
    transition: 1s;
  }
  .pleaseChoose {
    color: #999;
    font-weight: 200;
  }
</style>
