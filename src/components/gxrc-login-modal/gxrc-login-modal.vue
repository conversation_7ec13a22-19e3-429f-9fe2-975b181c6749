<template>
  <view class="login-modal">
    <uni-popup ref="loginpopup" background-color="#fff" @change="change">
      <view v-if="!showAgreeView" class="login-wrap">
        <view class="login-wrap-title">登录或注册</view>
        <gxrc-login-button
          class-name="login-wrap-button toutiao_login_button"
          :is-agree="isAgree"
          @change="close"
          @success="success"
          @error="cancel"
          @notAgree="notAgree"
        >
          <!--  #ifdef  MP-ALIPAY -->
          支付宝账号快捷登录
          <!--  #endif -->
          <!--  #ifdef  MP-ALIPAY -->
          <!-- <template #zhima>芝麻工作证登录</template> -->
          <!--  #endif -->
          <!--  #ifdef  MP-WEIXIN -->
          手机号快捷登录
          <!--  #endif -->
          <!--  #ifdef  MP-TOUTIAO -->
          抖音手机号一键登录
          <!--  #endif -->
        </gxrc-login-button>
        <!--  #ifdef  MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO -->
        <view class="login-wrap-tip">
          <checkbox-group @change="checkboxChange"
            ><checkbox class="radio" :value="isAgree"
          /></checkbox-group>
          <view>
            我已阅读并同意<text class="link" @tap="protocol">《用户协议》</text>和<text
              class="link"
              @tap="privacy"
              >《隐私政策》</text
            >
          </view>
        </view>
        <!--  #endif -->
      </view>
      <view v-else class="login-wrap">
        <view class="login-wrap-title">请阅读并同意协议</view>

        <view class="login-wrap-tip">
          <checkbox-group @change="checkboxChange"
            ><checkbox class="radio" :value="isAgree"
          /></checkbox-group>
          <view>
            我已阅读并同意<text class="link" @tap="protocol">《用户协议》</text>和<text
              class="link"
              @tap="privacy"
              >《隐私政策》</text
            >
          </view>
        </view>
        <gxrc-login-button
          class-name="login-wrap-agreeButton toutiao_login-wrap-agreeButton"
          @change="close"
          @success="success"
          @error="cancel"
          >同意并继续</gxrc-login-button
        >
      </view>
      <view class="close" @tap="close">x</view>
    </uni-popup>
  </view>
</template>
<script lang="ts">
  export default {
    inheritAttrs: false,
    options: { styleIsolation: 'shared' }
  }
</script>
<script lang="ts" setup>
  import { useAppStore } from '@/store/modules/app'
  import { computed, nextTick, watch } from 'vue'

  const props = withDefaults(
    defineProps<{
      emitSuccess?: boolean
    }>(),
    {
      emitSuccess: false
    }
  )
  const emits = defineEmits<{
    (e: 'successCallback'): void
  }>()

  const emitSuccess = computed(() => props.emitSuccess)

  const loginpopup = $ref<any>(null)

  const store = useAppStore()

  let isAgree = $ref(false)
  let showAgreeView = $ref(false)
  const tarbarList = [
    'pages/index/index',
    'pages/enterprise/index',
    'pages/message/index',
    'pages/my/index'
  ]

  const whiteList = [
    'pages/position/result',
    'pages/jobFair/jobFairDetail',
    'pages/jobFair/positionDetail',
    'pageToutiao/toutiao/index',
    'pages/jobFair/jobFairIndexs',
    'pages/sydw/index',
    'pages/sydw/article',
    'mypages/my/activity/tickets',
    'mypages/my/activity/school-tickets'
  ]

  const info = uni.getSystemInfoSync()
  const showLoginModal = computed(() => store.showLoginModal)
  watch(
    () => showLoginModal.value,
    (newval) => {
      const accessToken = uni.getStorageSync('access-token')
      const pages = getCurrentPages()
      const page = pages[pages.length - 1]
      const { route } = page
      if (newval && !accessToken && !whiteList.includes(route || '') && !route?.startsWith('pageEnterprise')) {
        try {
          loginpopup.open('center')
        } catch (e) {
          setTimeout(() => {
            nextTick(() => {
              loginpopup.open('center')
            })
          }, 200)
        }
      } else {
        store.showLoginModal = false

        try {
          loginpopup?.close()
        } catch (e) {
          setTimeout(() => {
            nextTick(() => {
              loginpopup?.close()
            })
          }, 200)
        }
      }
    },
    {
      deep: true,
      immediate: true
    }
  )

  const change = (e: any) => {
    const { show } = e
    if (!show) {
      store.showLoginModal = false
      showAgreeView = false
    }
  }
  const close = () => {
    loginpopup?.close()
    isAgree = false
    showAgreeView = false
  }
  const success = () => {
    isAgree = true
    if (emitSuccess.value) {
      emits('successCallback')
      return
    }
    const pages = getCurrentPages()
    const page = pages[pages.length - 1]
    const { route } = page
    const options = page.options
    if (store.fromfw) {
      store.fromfw = false
      uni.switchTab({
        url: '/pages/index/index'
      })
      return
    }
    if (route && tarbarList.includes(route || '')) {
      //pc端小程序
      if (info.osName === 'windows' && route === 'pages/my/index') {
        if (page.onHide) {
          page.onHide()
        }
        if (page.onShow) {
          page.onShow()
        }
      }
      uni.reLaunch({ url: '/' + route || '' })
    } else {
      const query = getQueryString(options)
      uni.redirectTo({ url: '/' + route + query })
    }
  }
  const cancel = () => {
    // uni.switchTab({ url: '/pages/my/index' })
    // store.showLoginModal = false
    // loginpopup.close()
  }

  const protocol = () => {
    const url = encodeURIComponent('https://www.gxrc.com/HtmlView/Licence.html')
    uni.navigateTo({
      url: `/pages/web-view/index?src=${url}`
    })
  }

  const privacy = () => {
    const url = encodeURIComponent('https://share.gxrc.com/HtmlView/app/privacy.html')
    uni.navigateTo({
      url: `/pages/web-view/index?src=${url}`
    })
  }

  const getQueryString = (params: Record<string, any>): string => {
    const qs: string[] = []

    const append = (key: string, value: any) => {
      qs.push(`${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`)
    }

    const process = (key: string, value: any) => {
      if (Array.isArray(value)) {
        value.forEach((v) => {
          process(key, v)
        })
      } else if (typeof value === 'object') {
        Object.entries(value).forEach(([k, v]) => {
          process(`${key}[${k}]`, v)
        })
      } else {
        append(key, value)
      }
    }

    Object.entries(params).forEach(([key, value]) => {
      process(key, value)
    })

    if (qs.length > 0) {
      return `?${qs.join('&')}`
    }

    return ''
  }

  const checkboxChange = (e: any) => {
    isAgree = !isAgree
  }

  const notAgree = () => {
    showAgreeView = true
  }
</script>

<style lang="scss">
  .login-modal {
    /*  #ifdef MP-TOUTIAO || MP-WEIXIN */
    width: 100%;
    height: 100%;
    /*  #endif  */
    border-radius: 8rpx;
    .login-wrap {
      width: 608rpx;
      min-height: 318rpx;

      &-title {
        display: flex;
        justify-content: center;
        margin: 48rpx 0;
        font-size: 40rpx;
        font-weight: 500;
        color: #333333;
      }
      &-button {
        width: 528rpx;
        height: 96rpx;
        line-height: 96rpx;
        margin: 0 40rpx 28rpx 40rpx;
        background: #608bf8;
        border-radius: 4px;
        font-size: 30rpx;
        font-weight: 400;
        color: #ffffff;
      }
      &-agreeButton {
        width: 528rpx;
        height: 96rpx;
        line-height: 96rpx;
        margin: 0 40rpx 28rpx 40rpx;
        background: #608bf8;
        border-radius: 4px;
        font-size: 30rpx;
        font-weight: 400;
        color: #ffffff;
        margin-top: 20px;
      }
      &-tip {
        padding: 0 35rpx;
        font-size: 24rpx;
        color: #999999;
        font-weight: 400;
        display: flex;
        .radio {
          transform: scale(0.7);
          margin-top: -3px;
        }
        .link {
          color: #608bf8;
        }
      }
    }
    .close {
      position: absolute;
      top: 40rpx;
      right: 40rpx;
      color: #bbbbbb;
    }
    ::v-deep .uni-popup .uni-popup__wrapper {
      display: block;
      position: relative;
      border-radius: 8px;
    }

    ::v-deep checkbox .wx-checkbox-input {
      border-radius: 50%;
    }
  }
</style>
