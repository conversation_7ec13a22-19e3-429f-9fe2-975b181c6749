<template>
    <uni-popup ref="popupOutsideLinkBox" type="center" @change="change">
				<view class="popup-outside-link-box">
          <view class="content">
      <view class="text">该链接为外部链接，小程序无法跳转，请点击复制链接，在浏览器中打开</view>
      <view class="link">{{url}}</view>
      </view>
      <view class="btn-wrap">
            <button class="btn btn-cancel" plain type="default" @click="cancel">
            取消
          </button>
          <button class="btn btn-confirm" plain type="primary" @click="confirm">
            复制链接
          </button>
          </view>
    </view>
			</uni-popup>
</template>
<script lang="ts">
  export default {
    inheritAttrs: false,
    options: { styleIsolation: 'shared' }
  }
</script>
<script lang="ts" setup>
 import {
    defineComponent,
    reactive,
    toRef,
    toRefs,
    ref,
    watch,
    computed,
    getCurrentInstance,
    onMounted,
    onBeforeMount,
    nextTick
  } from 'vue'
  interface Props {
    url: string
    show:boolean
  }

  const { url,show } = defineProps<Props>()
  const emit = defineEmits<{
    (e: 'cancel'): void
    (e: 'confirm'): void
  }>()
  let popupOutsideLinkBox = ref(null)
  
watch(
  () => show,
  async (newval) => {
    if(newval){
      popupOutsideLinkBox.value.open()
    }else{
      popupOutsideLinkBox.value.close()
    }
  }
)

const cancel=()=>{
    emit('cancel')
}
const confirm=()=>{
  uni.setClipboardData({
    data: url,
    success: function (res) {
      uni.showToast({
        title:'复制成功'
      })
      popupOutsideLinkBox.value.close()
    }
  })
  
}
const change = (e) => {
    if (!e.show) {
      emit('cancel')
    }
  }
</script>
<style lang="scss" scoped>
.popup-outside-link-box{width: 600rpx; background: #fff;
border-radius:20rpx;overflow: hidden;
.content{padding:50rpx;text-align: center;
.text{font-size:28rpx;padding-bottom:30rpx;word-break: break-all;}
.link{font-size:24rpx;color: #457ccf;word-break: break-all;}
}

.btn-wrap{
        display: flex;border-top: 2rpx solid #F2F2F2;
    .btn{flex:1;height:90rpx;line-height:90rpx; font-size: 28rpx;border:none;background:#fff;border-radius:0;}
    .btn-cancel{
        background-color:#fff;
        color: #666;
        border-right: 2rpx solid #F2F2F2;
    }
    .btn-confirm {color: #457ccf;font-weight: bold;}
}
}
</style>
