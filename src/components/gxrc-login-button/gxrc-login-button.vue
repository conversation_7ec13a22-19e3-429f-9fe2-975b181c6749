<template>
  <!--  #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO -->
  <button
    v-if="isAgree"
    :class="className"
    open-type="getPhoneNumber"
    @getphonenumber="decryptPhoneNumber"
  >
    <slot></slot>
  </button>
  <button v-if="!isAgree" :class="className" @tap="action">
    <slot name="default"></slot>
  </button>

  <!-- <button v-if="!isAgree" :class="className" @tap="actionZhima">
    <slot name="zhima"></slot>
  </button> -->

  <!--  #endif -->
</template>
<script lang="ts">
  export default {
    inheritAttrs: false,
    options: { styleIsolation: 'shared' }
  }
</script>
<script lang="ts" setup>
  import { ApiError } from '@/utils/ApiError'
  import { Account } from '@/services/my/Account'
  import { Loginbyweixinmini } from '@/services/my/data-contracts'
  import { useImSdkStore } from '@/store/modules/im-sdk-init'
  import { useAppStore } from '@/store/modules/app'
  import { useNavigate } from '@/hooks/page/useNavigate'

  const { redirectTo } = useNavigate()

  interface Props {
    className?: string
    isAgree?: boolean
    needRegister?: boolean
  }
  const imSdkStore = useImSdkStore()
  const store = useAppStore()

  const { className, isAgree = true, needRegister = true } = defineProps<Props>()
  const emit = defineEmits<{
    (e: 'change'): void
    (e: 'success'): void
    (e: 'error'): void
    (e: 'notAgree'): void
  }>()
  const sdk = $computed(() => {
    if (store.hostSDKVersion) {
      const a = store.hostSDKVersion.replace(/\./g, '')
      return a.length > 4 ? a.substring(0, 4) : a
    }
    return '0'
  })
  // #ifdef MP-WEIXIN || MP-ALIPAY
  //兼容旧版本 获取手机号需要提前获取code
  if (parseInt(sdk) < 2212) {
    store.updateWeiXinCode()
  }
  // #endif

  // #ifdef MP-TOUTIAO
  store.updateWeiXinCode()
  // #endif

  const getPhoneNumberHandler = (e) => {
    console.log(e)
  }

  const actionZhima = async () => {
    uni.showLoading({
      title: '加载中'
    })
    let { data } = await Account.zhimasign()

    if (data.isSuccess) {
      my.call(
        'startBizService',
        {
          name: 'openCreditEvaluation',
          param: `{"signStr":"${data.signStr}"}`
        },
        (result) => {
          console.log(result)
        }
      )
    } else {
      uni.showToast({
        title: '参数有误'
      })
    }
    uni.hideLoading()
  }

  const decryptPhoneNumber = async (e: any) => {
    const { detail } = e
    const { errMsg } = detail

    if (errMsg.includes('getPhoneNumber:ok')) {
      // #ifdef MP-WEIXIN
      if (parseInt(sdk) >= 2212) {
        await store.updateWeiXinCode()
      }
      // #endif

      // #ifdef MP-ALIPAY
      await store.updateWeiXinCode()
      // #endif
      uni.showLoading({
        title: '登录中'
      })

      const jsCode = store.wx_code

      // #ifdef MP-WEIXIN
      const params: Loginbyweixinmini = {
        code: detail.code,
        jsCode,
        siteId: store.districtId || 0,
        iv: detail.iv,
        encryptedData: detail.encryptedData
      }

      const result = await Account.accountLoginbyweixinmini(params).catch((err) => {
        return err as ApiError
      })
      // #endif

      // #ifdef MP-TOUTIAO
      const params: Loginbyweixinmini = {
        code: jsCode,
        anonymous_code: store.toutiao_code,
        siteId: store.districtId || 0,
        iv: detail.iv,
        encryptedData: detail.encryptedData
      }

      const result = await Account.accountLoginbydouyinmini(params).catch((err) => {
        return err as ApiError
      })
      // #endif

      // #ifdef MP-ALIPAY

      const params: any = {
        response: detail.encryptedData,
        siteId: 0,
        sign: detail.sign,

        authCode: jsCode
      }

      const result = await Account.accountLoginbyzhifubaomini(params).catch((err) => {
        return err as ApiError
      })
      // #endif

      uni.hideLoading()
      const { code, message } = result
      if (code == 0) {
        emit('error')
        uni.showToast({
          title: message as string,
          icon: 'none'
        })
      } else {
        let routes = getCurrentPages() 
        let curRoute = routes[routes.length - 1]?.route
        //微信小程序活动页未注册不跳转注册页
        if (result.data.step === 'noresume' && (!curRoute?.includes('mypages/my/activity') && needRegister)) {
          uni.redirectTo({ url: '/mypages/registerBlue/baseInfo' })
          return
        } else {
          store.isLogin = true
          imSdkStore.connect()

          emit('success')
          // #ifdef MP-ALIPAY
          setTimeout(function () {
            emit('success')
          }, 100)
          // #endif
        }
      }

      uni.removeStorageSync('wx_code')
      uni.removeStorageSync('toutiao_code')
      store.wx_code = ''
      store.toutiao_code = ''
    } else {
      uni.showToast({
        title: '您已拒绝登录',
        icon: 'none',
        mask: true
      })
    }
    emit('change')
  }

  const toutiaoLogin = () => {
    let routes = getCurrentPages() // 获取当前打开过的页面路由数组
    let curRoute = routes[routes.length - 1].route
    redirectTo(`/pageToutiao/toutiao/index?route=${curRoute}`)
  }

  const action = () => {
    if (!isAgree) {
      emit('notAgree')
      return
    }
  }
</script>
<style lang="scss" scoped>
  .toutiao_login_button {
    // #ifdef MP-TOUTIAO
    width: 380rpx;
    // #endif
    // #ifdef MP-WEIXIN || MP-ALIPAY
    width: 360rpx;
    // #endif
    background: #608bf8;
    margin: 20px auto;
    color: #fff;
  }
  .toutiao_login-wrap-agreeButton {
    width: 360rpx;
    background: #608bf8;
    margin: 20px auto;
    color: #fff;
  }
  /*  #ifdef MP-TOUTIAO */
  .toutiao_im_login_button {
    width: auto;
    background: #608bf8;
    color: #fff;
  }
  /*  #endif  */

  /*  #ifdef MP-ALIPAY */
  .alipay_im_login_button {
    width: 320rpx;
    height: 80rpx;
    line-height: 80rpx;
    font-size: 28rpx;
    border-radius: 8rpx;
    text-align: center;
    padding: 0;
    background: #608bf8;
    color: #fff;
    margin-top: 40rpx;
  }
  .alipay_index_login_button {
    float: right;
    width: 160rpx;

    height: 60rpx;
    line-height: 58rpx;
    border: 2rpx solid #457ccf;
    font-size: 26rpx;
    border-radius: 30rpx;
    text-align: center;
    font-weight: bold;
    margin-top: 22rpx;
    padding: 0;
    background: none;
    color: #457ccf;
  }

  .alipay_job_login_button {
    width: 100%;
    color: #fff;
    background-color: #457ccf;
    border: 2rpx solid #457ccf;
    border-radius: 10rpx;
    box-sizing: border-box;
  }
  /*  #endif  */

  .activity-button {
    border-radius: 44rpx;
    background: linear-gradient(115deg, #FDA71A 0%, #FF8B27 100%);
    width: 400rpx;
    color: #fff;
    font-weight: bold;
  }
</style>
