<template>
  <view class="gxrc-picker">
    <view class="picker-input" @tap="bindTap">
      <text v-if="datavalue" class="pd">{{ datavalue }}</text>
      <text v-else class="placehold pd">{{ placeholder }}</text>
      <i v-if="showArrow" class="iconfont icon-arrowRight14 icon"></i>
    </view>
    <uni-popup ref="datePopup" type="bottom" background-color="#fff">
      <view class="picker-title">
        <text class="cancel font" @tap="bindCancel">取消</text>
        <text class="title">{{ title }}</text>
        <text class="confirm font" @tap="bindComfirm">确认</text>
      </view>

      <picker-view
        class="picker-view"
        :value="selectValue"
        :immediate-change="immediatechange"
        @change="bindChange"
      >
        <picker-view-column>
          <view v-for="(item, index) in years" :key="index" class="item">
            {{ item }}
          </view>
        </picker-view-column>
        <picker-view-column>
          <template v-if="showMonthAndDay">
            <view v-for="(item, index) in months" :key="index" class="item"> {{ item }}月 </view>
          </template>
        </picker-view-column>
        <picker-view-column v-if="incluedeDay">
          <template v-if="showMonthAndDay">
            <view v-for="(item, index) in days" :key="index" class="item"> {{ item }}日 </view>
          </template>
        </picker-view-column>
      </picker-view>
    </uni-popup>
  </view>
</template>
<script lang="ts" setup>
  import { watch, onMounted, nextTick } from 'vue'

  interface Props {
    incluedeDay?: boolean

    modelValue?: string | Date

    title?: string

    placeholder?: string

    showArrow?: boolean

    isAttendWork?: boolean

    isEducation?: boolean

    isBirthday?: boolean

    lastSelectDate?: string

    afterSelectDate?: string
  }

  // #ifdef MP-WEIXIN || MP-ALIPAY
  let immediatechange = true
  // #endif
  // #ifdef MP-TOUTIAO
  let immediatechange = false
  // #endif

  const {
    incluedeDay = false,
    modelValue,
    placeholder,
    title = '请选择',
    showArrow = true,
    isAttendWork = false,
    isEducation = false,
    isBirthday = false,
    lastSelectDate = '',
    afterSelectDate = ''
  } = defineProps<Props>()

  const emit = defineEmits<{
    (e: 'update:modelValue', value: number | string | boolean): void
  }>()

  let datavalue = $ref<number | string | boolean | null>()

  let selectValue = $ref<Array<number | string>>()

  let changeValue = $ref<string>()

  let showMonthAndDay = $ref<boolean>(true)

  let months = $ref<number[]>([])

  const datePopup = $ref<any>(null)

  const date = new Date()
  const years: Array<string> = []
  const year = date.getFullYear()

  const month = date.getMonth() + 1
  const days: number[] = []
  const day = date.getDate()

  const initYear = isBirthday ? year - 16 : isEducation ? year + 5 : year
  for (let i = 1930; i <= initYear; i++) {
    years.push(i + '年')
  }
  if (isAttendWork) {
    years.push('无工作经验')
  }
  for (let i = 1; i <= (isEducation ? 12 : month); i++) {
    months.push(i)
  }
  if (incluedeDay) {
    for (let i = 1; i <= 31; i++) {
      days.push(i)
    }
  }

  selectValue = incluedeDay
    ? [isEducation ? years.indexOf(year + '年') : years.length - 1, month - 1, day - 1]
    : [isEducation ? years.indexOf(year + '年') : years.length - 1, month - 1]

  const initSelect = (value: any) => {
    nextTick(() => {
      if (isAttendWork && value === '0') {
        selectValue = [years.length - 1]
        datavalue = years[years.length - 1]
        changeValue = datavalue
        showMonthAndDay = false
      } else if (value) {
        const date = new Date(value)
        const y = date.getFullYear() + '年'
        const m = date.getMonth() + 1
        const d = date.getDay()

        if (date.getFullYear() == year && !isEducation) {
          months = months.slice(0, month)
        } else {
          months = Array(12)
            .fill(0)
            .map((item, index) => index + 1)
        }

        setTimeout(function () {
          selectValue = incluedeDay
            ? [years.indexOf(y), months.indexOf(m), days.indexOf(d)]
            : [years.indexOf(y), months.indexOf(m)]

          datavalue = changeValue = value
        }, 10)
      } else {
        datavalue = value
        const y = (
          isEducation ? years[years.indexOf(year + '年')] : years[years.length - 1]
        ).replace('年', '')
        const mm = month < 10 ? `0${month}` : month
        changeValue = incluedeDay ? `${y}-${mm}-${days[days.length - 1]}` : `${y}-${mm}`
      }
    })
  }

  initSelect(modelValue)
  watch(
    () => modelValue,
    (newval) => {
      initSelect(newval)
    }
  )

  const bindTap = () => {
    datePopup.open()
  }

  const bindCancel = () => {
    datePopup.close()
  }

  const bindChange = (e: any) => {
    let value = e.detail.value
    value = value.map((item) => {
      return item || 0
    })
    const y = years[value[0]]?.replace('年', '')
    if (y?.includes('无工作经验')) {
      showMonthAndDay = false
      changeValue = y
      
    } else {
      showMonthAndDay = true
      if (y == year.toString() && !isEducation) {
        months = months.slice(0, month)
      } else {
        months = Array(12)
          .fill(0)
          .map((item, index) => index + 1)
      }
      const m = months[value[1]]
      const mm = m < 10 ? `0${m}` : m
      changeValue = incluedeDay ? `${y}-${mm}-${days[value[2]]}` : `${y}-${mm}`
    }
  }

  const bindComfirm = () => {
    if (lastSelectDate) {
      const date = new Date(lastSelectDate)
      const current = new Date(changeValue)
      if (date > current) {
        let message = isEducation ? '毕业时间不能小于开始时间' : '结束时间不能小于开始时间'
        uni.showToast({
          title: message,
          icon: 'none'
        })
        datePopup.close()
        return
      }
    }

    if (afterSelectDate) {
      const date = new Date(afterSelectDate)
      const current = new Date(changeValue)
      if (date < current) {
        let message = isEducation ? '开始时间不能大于毕业时间' : '开始时间不能大于结束时间'
        uni.showToast({
          title: message,
          icon: 'none'
        })
        datePopup.close()
        return
      }
    }
    datavalue = changeValue.includes('无工作经验') ? '无工作经验' : changeValue

    emit('update:modelValue', datavalue === '无工作经验' ? '0' : datavalue)
    datePopup.close()
  }
</script>
<style lang="scss">
  .gxrc-picker {
    .picker-title {
      display: flex;
      // flex-direction: row;
      justify-content: space-between;
      margin-top: 15px;
    }

    .picker-input {
      flex: 1;
      width: 100%;
      display: flex;
      box-sizing: border-box;
      min-height: 36px;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .icon {
        font-size: 12px;
        color: $uni-text-color-placeholder;
      }
      .placehold {
        // color: #bbbbbb;
        color: #999;
        font-weight: 200;
      }
      .pd {
        padding-right: 8px;
        font-size: 15px;
      }
    }

    .font {
      // font-size: $uni-font-size-sm;
      color: $uni-color-primary;
    }

    .cancel {
      padding: 15px;
      color: #666666;
    }
    .confirm {
      padding: 15px;
    }
    .title {
      padding-top: 15px;
    }

    .picker-view {
      width: 750rpx;
      min-height: 400rpx;
      margin-top: 15rpx;

      .item {
        height: 50px;
        line-height: 33px;
        align-items: center;
        justify-content: center;
        text-align: center;
        // font-size: 36rpx;
      }
    }
  }
</style>
