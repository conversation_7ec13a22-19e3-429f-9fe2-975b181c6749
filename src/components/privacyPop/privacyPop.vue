<template>
  <uni-popup ref="popup" type="bottom" :safe-area="false" :mask-click="false">
    <view class="privacy">
      <view class="title">隐私保护指引</view>
      <view class="content">
        感谢您选择广西人才网小程序，我们非常重视您的个人信息安全和隐私保护。依据最新要求，使用我们的产品前，请仔细阅读并同意<text
          class="bl"
          @tap="handleOpenPrivacyContract"
          >{{ privacyContractName }}</text
        >, 以便我们向你提供更优质的服务。
        <view
          >当您点击“同意”时，视为您已阅读和愿意接受<text
            class="bl"
            @tap="handleOpenPrivacyContract"
            >{{ privacyContractName }}</text
          >的所有内容。当您点击“拒绝”时，我们将无法为您提供完整的产品和服务。</view
        >
        <view>我们承诺将尽全力保护你的个人信息及合法权益，再次感谢您的信任！</view>
      </view>
      <!-- <view class="content" v-else>
        您拒绝了广西人才网用户协议和隐私政策，我们将无法为您提供相应的服务。请仔细阅读并同意<text
          class="bl"
          @tap="handleOpenPrivacyContract"
          >{{ privacyContractName }}</text
        >, 以便为您提供更优质的服务。
      </view> -->
      <view class="btn-list">
        <!-- <button class="cancel-btn" v-if="!refuse" @tap="refuse = true">拒绝</button> -->
        <button class="cancel-btn" @tap="showTip">拒绝</button>
        <button
          class="agree-btn"
          open-type="agreePrivacyAuthorization"
          @agreeprivacyauthorization="handleAgreePrivacyAuthorization"
        >
          同意并继续
        </button>
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
  import { onMounted } from 'vue'

  const popup = $ref<any>(null)
  let refuse = $ref(false)
  let privacyContractName = $ref('')
  const emits = defineEmits<{
    (e: 'successCallback'): void
  }>()

  const tarbarList = [
    'pages/index/index',
    'pages/enterprise/index',
    'pages/message/index',
    'pages/my/index'
  ]

  const pages = getCurrentPages()
  const page = pages[pages.length - 1]
  const { route } = page

  onMounted(() => {
    try {
      uni?.getPrivacySetting({
        success: (res: any) => {
          console.log(res)
          privacyContractName = res.privacyContractName
          if (res.needAuthorization) {
            if (tarbarList.includes(route)) {
              uni.hideTabBar()
            }
            popup?.open()
          } else {
            // 不需要授权
            emits('successCallback')
          }
        }
      })
    } catch {
      emits('successCallback')
    }
  })

  const handleOpenPrivacyContract = () => {
    uni.openPrivacyContract({
      success: (res: any) => {
        console.log('success', res)
      },
      fail: (err: any) => {
        console.log('fail', err)
      }
    })
  }

  const handleAgreePrivacyAuthorization = () => {
    emits('successCallback')
    if (tarbarList.includes(route)) {
      uni.showTabBar()
    }
    refuse = false
    popup?.close()
  }
  const showTip = () => {
    // uni.exitMiniProgram({
    //   success: (res: any) => {
    //     console.log('success', res)
    //   },
    //   fail: (err: any) => {
    //     uni.showToast({
    //       title: '很遗憾我们无法继续为您提供服务，您可以手动退出小程序',
    //       icon: 'none',
    //       duration: 2000
    //     })
    //   }
    // })
    uni.showToast({
      title: '很遗憾我们无法继续为您提供服务，您可以手动退出小程序',
      icon: 'none',
      duration: 2000
    })
  }
</script>

<style lang="scss" scoped>
  .privacy {
    min-height: 200px;
    border-radius: 20px;
    background: #ffffff;
    padding: 20px;
    .title {
      font-size: 18px;
      font-weight: 600;
    }
    .content {
      padding-top: 20px;
      font-size: 14px;
      line-height: 24px;
      .bl {
        color: #1a73e8;
      }
    }
    .btn-list {
      display: flex;
      padding-top: 20px;
      justify-content: space-between;
      button::after {
        border: none;
      }
      .agree-btn {
        background: #1a73e8;
        color: #ffffff;
        // padding: 2px 60px;
        font-size: 16px;
        margin: 0px;
        flex: 1;
        width: 300px;
      }
      .cancel-btn {
        // padding: 2px 20px;
        margin-right: 10px;
        // max-width: 80px;
        margin-left: 0;
        font-size: 16px;
        // flex: 1;
      }
    }
  }
</style>
