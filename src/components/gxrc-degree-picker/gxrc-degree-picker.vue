<template>
  <view class="gxrc-picker">
    <view class="picker-input" :class="{ end: type === 'end' }" @tap="bindTap">
      <text v-if="datavalue" class="pd">{{ datavalue }}{{ isshow ? '-' + datavalue2 : '' }} </text>
      <text v-if="!datavalue" class="placehold pd">{{ placeholder }}</text>
      <i class="iconfont icon-arrowRight14 icon"></i>
    </view>
    <uni-popup ref="myPopup" type="bottom" background-color="#fff">
      <view class="picker-title">
        <text class="cancel font" @tap="bindCancel">取消</text>
        <text class="title">{{ title }}</text>
        <text class="confirm font" @tap="bindComfirm">确认</text>
      </view>

      <picker-view
        class="picker-view"
        :value="selectValue"
        @change="bindChange"
        :immediate-change="immediatechange"
      >
        <picker-view-column>
          <view class="item" v-for="(item, id) in educationOptions" :key="id">
            {{ item.keywordName }}
          </view>
        </picker-view-column>
        <picker-view-column v-if="ishighly">
          <view class="item" v-for="(item, id) in fulltimeOption" :key="id">
            {{ item.text }}
          </view>
        </picker-view-column>
        <picker-view-column v-else> </picker-view-column>
      </picker-view>
    </uni-popup>
  </view>
</template>
<script lang="ts" setup>
  import { nextTick, watch, computed, onMounted } from 'vue'
  import { useOptionsStore } from '@/store/modules/options'

  interface pickerData {
    text: string

    value: number | string | boolean
  }

  interface Props {
    degreeId: number | null | undefined

    fullTimeFlag: boolean

    title?: string

    placeholder?: string
    //文字左右两端space  一起在右边 end
    type?: 'space' | 'end'
  }

  const {
    fullTimeFlag,
    degreeId,
    placeholder = '请选择',
    title = '请选择',
    type = 'space'
  } = defineProps<Props>()

  // #ifdef MP-WEIXIN || MP-ALIPAY
  let immediatechange = true
  // #endif
  // #ifdef MP-TOUTIAO
  let immediatechange = false
  // #endif

  const emit = defineEmits<{
    (e: 'update:degreeId', value: number | string | boolean): void
    (e: 'update:fullTimeFlag', val: boolean): void
  }>()

  let datavalue = $ref<number | string | boolean | null>()
  let datavalue2 = $ref<number | string | boolean | null>()
  let isshow = $ref<boolean>(false)

  let selectValue = $ref<number[]>([0, 0])
  const myPopup = $ref<any>(null)

  const store = useOptionsStore()

  const fulltimeOption: pickerData[] = [
    {
      text: '全日制',
      value: true
    },
    {
      text: '非全日制',
      value: false
    }
  ]

  onMounted(async () => {
    await store.degreeOptions()

    initSelect(degreeId, fullTimeFlag)
  })

  const educationOptions = computed(() => store.degree)

  const ishighly = computed(() => {
    let ind = selectValue?.[0]
    return (store.degree[ind]?.keywordID ?? 0) > 353
  })

  const initSelect = (value: any, Flag: boolean | null) => {
    if (store.degree && store.degree.length > 0) {
      nextTick(() => {
        let val = value
        const index = store.degree?.findIndex((item) => item.keywordID === val) //找出下标
        const index2 = fulltimeOption?.findIndex((item) => item.value === Flag)
        selectValue = [index == -1 ? 0 : index, index2 === -1 ? 0 : index2]
        datavalue = store.degree[index]?.keywordName ?? value
        datavalue2 = fulltimeOption[index2]?.text
        isshow = (store.degree[index]?.keywordID ?? 0) > 353
      })
    }
  }

  watch(
    () => degreeId,
    (newval) => {
      initSelect(newval, fullTimeFlag)
    }
  )
  const bindTap = () => {
    myPopup.open()
  }

  const bindCancel = () => {
    myPopup.close()
  }

  const bindChange = (e: any) => {
    const index1 = e.detail.value[0]
    const index2 = e.detail.value[1]
    selectValue = [index1, index2]
    // ishighly = dataList[index1]?.value > 353
  }

  const bindComfirm = () => {
    const index = selectValue[0]
    const index2 = selectValue[1]
    datavalue = store.degree[index]?.keywordName ?? ''
    const value = store.degree[index]?.keywordID ?? 0
    emit('update:degreeId', value)
    if (index2 > -1) {
      isshow = value > 353
      datavalue2 = fulltimeOption[index2].text
      if (value <= 353) {
        fulltimeOption[index2].value = true
      }
      const fulltime = fulltimeOption[index2].value
      emit('update:fullTimeFlag', fulltime as boolean)
    }
    myPopup.close()
  }
</script>
<style lang="scss">
  .gxrc-picker {
    .picker-title {
      display: flex;
      // flex-direction: row;
      justify-content: space-between;
    }
    .picker-input {
      flex: 1;
      width: 100%;
      display: flex;
      box-sizing: border-box;
      min-height: 36px;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .icon {
        font-size: 12px;
        color: $uni-text-color-placeholder;
      }
      .placehold {
        // color: #bbbbbb;
        color: #999;
        font-weight: 200;
      }
      .pd {
        padding-right: 8px;
        font-size: 15px;
      }
    }
    .end {
      justify-content: flex-end;
      color: #666666;
    }
    .font {
      // font-size: $uni-font-size-sm;
      color: $uni-color-primary;
    }
    .cancel {
      padding: 15px;
      color: #666666;
    }
    .confirm {
      padding: 15px;
    }
    .title {
      padding-top: 15px;
    }
    .picker-view {
      width: 750rpx;
      min-height: 400rpx;
      margin-top: 15rpx;
      .item {
        height: 50px;
        line-height: 33px;
        align-items: center;
        justify-content: center;
        text-align: center;
        // font-size: 36rpx;
      }
    }
  }
</style>
