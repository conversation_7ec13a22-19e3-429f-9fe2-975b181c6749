<template>
  <view class="policy-card" @click="handleClick">
    <view class="policy-card__content">
      <view class="policy-card__left">
        <text class="policy-card__title">{{ title }}</text>
        <text class="policy-card__subtitle">{{ subTitle }}</text>
      </view>
      <view class="policy-card__right">
        <i class="iconfont icon-arrowRight14"></i>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useNavigate } from '@/hooks/page/useNavigate';

interface Props {
  title: string;
  subTitle: string;
  to?: string;
}

const props = withDefaults(defineProps<Props>(), {
  to: ''
});

const { navigateTo } = useNavigate();

const handleClick = () => {
  if (props.to) {
    navigateTo(props.to);
  }
};
</script>

<style lang="scss" scoped>
.policy-card {
  width: 100%;
  background-color: #f1f3f9;
  border-radius: 16rpx;
  padding: 50rpx 24rpx;
  box-sizing: border-box;
  margin-bottom: 16rpx;
  height: 380rpx;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.04);

  &__content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__left {
    display: flex;
    flex-direction: column;
  }

  &__title {
    font-size: 82rpx;
    font-weight: 500;
    color: #181D46;
    margin-bottom: 8rpx;
    font-family: PingFang SC, PingFang SC;
font-weight: bold;
  }

  &__subtitle {
    font-size:44rpx;
    color: #464646;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    margin-top: 30rpx;
  }

  &__right {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30rpx;
    width:80rpx;
    height: 80rpx;
    background-color: #8995bb;
    border-radius: 50%;
    .iconfont {
      font-size: 32rpx;
      color: #fff;
    }
  }
}
</style> 