<script setup lang="ts">
type Props = {
    width?: number
    height?: number
}
const { width = 10, height = 10} = defineProps<Props>()
const widthStyle = `${width}rpx;`
const heightStyle = `${height}rpx;`
</script>
<template>
  <view class="dotLoad">
    <view class="dot1"> </view>
    <view class="dot2"></view>
    <view class="dot3"></view>
  </view>
</template>

<style lang="scss" scoped>
  .dotLoad {
    display: flex;
    align-items: center;
    .dot1,
    .dot2,
    .dot3 {
      width: v-bind(widthStyle);
      height: v-bind(heightStyle);
      border-radius: 50%;
      margin: 10rpx;
    }
    .dot1 {
      animation: jump 1.6s -0.32s linear infinite;
      background: #333;
    }
    .dot2 {
      animation: jump 1.6s -0.16s linear infinite;
      background: #333;
    }
    .dot3 {
      animation: jump 1.6s linear infinite;
      background: #333;
    }

    @keyframes jump {
      0%,
      80%,
      100% {
        -webkit-transform: scale(0);
        transform: scale(0);
      }
      40% {
        -webkit-transform: scale(2);
        transform: scale(2);
      }
    }
  }
</style>
