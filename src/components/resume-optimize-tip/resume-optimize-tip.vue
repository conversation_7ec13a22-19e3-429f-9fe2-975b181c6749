<script setup lang="ts">
  import { ResumeOptimizationItemModel } from '@/services/my/data-contracts'
  import { Resume } from '@/services/my/Resume'
  import { onMounted, ref, watch } from 'vue'
  const props = withDefaults(
    defineProps<{
      resumeGuid: string
      position?: 'index' | 'job'
    }>(),
    {
      resumeGuid: '',
      position: 'index'
    }
  )
  const resumeTip = ref<ResumeOptimizationItemModel>()
  const show = ref(true)
  const dic: Record<number, string> = {
    0: '/mypages/my/resume/index', //求职意向填写未完成
    1: '/mypages/my/resume/work', //工作描述待完善
    2: '/mypages/my/resume/project', //项目经历未填写
    3: '/mypages/my/resume/description', //个人描述未填写
    4: '/mypages/my/resume/index', //当前简历已隐藏
    5: '/mypages/my/resume/education', //校内实践经验未填写
    6: '/mypages/my/photo/PhotoManage', //我的头像
    7: '/mypages/my/resume/work', //工作技能
    99: '/mypages/my/resume/resumeOptimize' //简历优化页面
  }

  onMounted(async () => {
    const result = await Resume.resumeOptimizationmessage(props.resumeGuid).catch((err) => err)
    const { code, data } = result
    
    if (code == 1 && data) {
      const res = await Resume.resumeResumeoptimizationlistList({
        resumeguid: props.resumeGuid
      }).catch((err) => err)
      if (res.code == 1) {
        const list = res.data.items.filter(
          (item: ResumeOptimizationItemModel) => item.isNeedOptimization
        )
        
        if (list.length >= 2) {
          resumeTip.value = {
            isNeedOptimization: true,
            isEnable: true,
            resumeGuid: props.resumeGuid,
            resumeID: 0,
            sectionName: '',
            sectionTitle: '增加面试机会',
            sectionDescription: '优化简历，面试机会翻倍',
            optimization: 99,
            optimizationName: '',
            targetId: 0
          }
        } else {
          resumeTip.value = list[0]
        }
      }
    } else {
      resumeTip.value = undefined
    }
  })
  // watch(
  //   () => props.resumeGuid,
  //   async (newval) => {
  //     const result = await Resume.resumeOptimizationmessage(newval).catch((err) => err)
  //     const { code, data } = result
  //     console.log(data)
  //     if (code == 1 && data) {
  //       const res = await Resume.resumeResumeoptimizationlistList({ resumeguid: newval }).catch(
  //         (err) => err
  //       )
  //       if (res.code == 1) {

  //         const list = res.data.items.filter(
  //           (item: ResumeOptimizationItemModel) => item.isNeedOptimization
  //         )
  //         if (list.length >= 2) {
  //           resumeTip.value = {
  //             isNeedOptimization: true,
  //             isEnable: true,
  //             resumeGuid: newval,
  //             resumeID: 0,
  //             sectionName: '',
  //             sectionTitle: '增加面试机会',
  //             sectionDescription: '优化简历，面试机会翻倍',
  //             optimization: 99,
  //             optimizationName: '',
  //             targetId: 0
  //           }
  //         } else {
  //           resumeTip.value = list[0]
  //         }
  //       }
  //     }else{
  //       resumeTip.value = undefined
  //     }
  //   }
  // )

  const go = () => {
    let url = dic[resumeTip.value.optimization]
    if (resumeTip.value?.optimization != 6 && resumeTip.value?.optimization != 99) {
      url += `?resumeId=${resumeTip.value?.resumeID}`
    }
    switch (resumeTip.value?.optimization) {
      case 0:
        url += '&anchor=c'
        break
      case 1:
        if (resumeTip.value.targetId > 0) {
          url += `&workId=${resumeTip.value?.targetId}&anchor=desc`
        }
        break
      case 2:
        if (resumeTip.value.targetId > 0) {
          url += `&projectId=${resumeTip.value?.targetId}`
        }
        break
      case 3:
        if (resumeTip.value.targetId > 0) {
          url += `&desId=${resumeTip.value?.targetId}`
        }
        break
      case 4:
        url += '&auto=true'
        break
      case 5:
        if (resumeTip.value.targetId > 0) {
          url += `&educationId=${resumeTip.value?.targetId}`
        }
        break
      case 7:
        if (resumeTip.value.targetId > 0) {
          url += `&workId=${resumeTip.value?.targetId}&anchor=desc`
        }
        break
      case 99:
        url += `?guid=${resumeTip.value?.resumeGuid}`
    }

    const pages = getCurrentPages()
    if (pages.length >= 9) {
      uni.redirectTo({ url })
    } else {
      uni.navigateTo({
        url
      })
    }
  }

  const close = async () => {
    const result = await Resume.resumeoptimizationcloseonmessage({
      resumeGuid: props.resumeGuid,
      closeOnType: 1,
      type: 1
    }).catch((err) => err)
    if (result.code == 1) {
      show.value = false
    }
  }
</script>

<template>
  <view class="resume-optimize">
    <view class="resume-optimize-tip" v-if="resumeTip && show && position == 'index'">
      <view>
        <view class="title"
          >{{ resumeTip.sectionTitle }}
          <image
            src="https://image.gxrc.com/gxrcsite/wxMiniApp/2024/<EMAIL>"
            class="icon-go"
            mode="scaleToFill"
        /></view>
        <view class="desc">{{ resumeTip.sectionDescription }}</view>
      </view>
      <view class="btn" @tap="go">去优化</view>
      <i class="iconfont icon-close close" @tap="close"></i>
    </view>
    <view class="position-optimize-tip" v-if="resumeTip && show && position == 'job'">
      <view class="maintitle">
        <image
          src="https://image.gxrc.com/gxrcsite/wxMiniApp/2024/<EMAIL>"
          style="width: 50rpx; height: 50rpx;margin-right: 13rpx;"
          mode="scaleToFill"
        />
        {{ resumeTip.sectionTitle }}
      </view>
      <view class="desc">{{ resumeTip.sectionDescription }}</view>
      <view class="btn-list">
        <view class="btn" @tap="close">暂不处理</view>
        <view class="btn go" @tap="go">去优化</view>
      </view>
      <i class="iconfont icon-close close" @tap="close"></i>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .resume-optimize-tip {
    background: url('https://image.gxrc.com/gxrcsite/wxMiniApp/2024/<EMAIL>')
      no-repeat center center;
    background-size: 100% 100%;
    height: 140rpx;
    margin: 10rpx 20rpx 40rpx 20rpx;
    padding: 0 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    .title {
      font-size: 34rpx;
      color: #212121;
      font-weight: bolder;
    }
    .icon-go {
      width: 20rpx;
      height: 34rpx;
      position: relative;
      top: 6rpx;
      left: 4rpx;
    }
    .desc {
      font-size: 25rpx;
      color: #414659;
    }

    .btn {
      background: linear-gradient(91deg, #7bb2ee 0%, #306add 100%);
      box-shadow: 2rpx 10rpx 12rpx 1rpx #bbd1ff;
      border-radius: 24rpx;
      font-size: 25rpx;
      color: #ffffff;
      padding: 6rpx 22rpx;
      margin-right: 30rpx;
      flex-shrink: 0;
    }
    .close {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      color: #999999;
      font-size: 26rpx;
    }
  }
  .position-optimize-tip {
    background: url('https://image.gxrc.com/gxrcsite/wxMiniApp/2024/<EMAIL>?v=1') no-repeat center
      center;
    background-size: 100% 100%;
    // height: 266rpx;
    padding: 30rpx;
    position: relative;
    .maintitle {
      display: flex;
      align-items: center;
      font-size: 33rpx;
      font-weight: bolder;
      color: #212121;
    }
    .desc{
      font-size: 28rpx;
      color: #4F5364;
      margin-top: 14rpx;
    }
    .btn-list{
      margin-top: 30rpx;
      display: flex;
      justify-content: space-around;
      .btn{
        width: 290rpx;
        padding: 16rpx 0;
        background: #fff;
        border-radius: 8rpx;
        font-size: 28rpx;
        color: #959BB2;
        text-align: center;
      }
      .go{
        background: linear-gradient( 90deg, #5C84EC 0%, #306ADD 100%);
        color: #fff;
      }
    }
    .close {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      color: #999999;
      font-size: 26rpx;
    }
  }
</style>
