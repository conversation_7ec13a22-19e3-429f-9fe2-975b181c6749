<template>
  <view class="city-picker clearfix">
    <view class="picker-input" @tap="selecity()">
      <text :class="[cityName ? 'uni-ellipsis' : 'pleaseChoose', textAlign]">{{
        cityName || '请选择'
      }}</text>
      <i v-if="hasarrow" class="iconfont icon-icon_more icon"></i>
    </view>
    <uni-popup ref="showRight" type="right" :is-mask-click="true" class="pop-city-box">
      <view class="bg-white h100">
        <uni-nav-bar
          left-text="选择地点"
          left-width="200rpx"
          :fixed="true"
          :status-bar="true"
          left-icon="left"
          :border="false"
          @clickLeft="closeDrawer"
        />
        <view class="selected-wrap">
          <view class="count"
            >已选择( <label class="bule">{{ selectedCount }}</label
            >/{{ maxCount }})</view
          >
          <view class="selected-items clearfix">
            <view
              v-for="val in activeGroup"
              :key="val.keywordID"
              class="item"
              @click="deleteCity(val)"
            >
              {{ val.keywordName }}
              <i class="iconclose">X</i>
            </view>
          </view>
        </view>
        <view class="sel-main clearfix" :style="`width:` + width + 'px'">
          <view class="box-city clearfix">
            <!-- 第一组 -->
            <view class="sel-city-box sel-city-boxA">
              <scroll-view
                :style="{ height: scrollHeight + 'px' }"
                scroll-y="true"
                :show-scrollbar="false"
              >
                <view class="ul">
                  <view
                    v-for="(item, index) in firstArr"
                    :key="index"
                    clickable
                    :class="[
                      'li clearfix',
                      { on: item.keywordID == isIDA, isseled: item.selected }
                    ]"
                    @click="cityChooseA(item)"
                  >
                    <view class="sdl">{{ item.text }}</view>
                    <view class="sdr">
                      <i v-if="item.hasNext" class="iconfont icon-arrowRight14"></i>
                      <i v-if="item.selected && !item.hasNext" class="hook"
                        ><uni-icons type="checkmarkempty" color="#5e8df5" size="16"></uni-icons
                      ></i>
                    </view>
                  </view>
                </view>
              </scroll-view>
            </view>
            <!-- 第二组 -->
            <view class="sel-city-box sel-city-boxB">
              <scroll-view :style="{ height: scrollHeight + 'px' }" scroll-y="true">
                <view class="ul">
                  <view
                    v-for="(item, index) in secondArr"
                    :key="index"
                    clickable
                    :class="[
                      'li clearfix',
                      { on: item.keywordID == isIDB, isseled: item.selected }
                    ]"
                    @click="cityChooseB(item, index)"
                  >
                    <view class="sdl">{{
                      index == 0 && !noguangxi ? '全' + item.text : item.text
                    }}</view>
                    <view class="sdr">
                      <i
                        v-if="item.hasNext && ((index != 0 && !noguangxi) || noguangxi)"
                        class="iconfont icon-arrowRight14"
                      ></i>
                      <i
                        v-if="(item.selected && !item.hasNext) || (item.selected && index == 0)"
                        class="hook"
                        ><uni-icons type="checkmarkempty" color="#5e8df5" size="16"></uni-icons
                      ></i>
                    </view>
                  </view>
                </view>
              </scroll-view>
            </view>
          </view>
          <!-- 第三组 -->
          <view class="sel-city-box sel-city-boxC">
            <view class="city-boxC">
              <scroll-view :style="{ height: scrollHeight + 'px' }" scroll-y="true">
                <view class="ul">
                  <view
                    v-for="(item, index) in thirdArr"
                    :key="index"
                    clickable
                    :class="['li clearfix', { isseled: item.selected }]"
                    @click="cityChooseC(item)"
                  >
                    <view class="sdl">{{
                      index == 0 && !noguangxi ? '全' + item.text : item.text
                    }}</view>
                    <view class="sdr">
                      <i v-if="item.selected" class="hook"
                        ><uni-icons type="checkmarkempty" color="#5e8df5" size="16"></uni-icons
                      ></i>
                    </view>
                  </view>
                </view>
              </scroll-view>
            </view>
          </view>
        </view>
        <view class="btn-box" @click="bindComfirm">
          <button class="login-wrap-button">保存</button>
        </view>
      </view>
    </uni-popup>
    <!-- 提示信息弹窗 -->
  </view>
</template>
<script lang="ts">
  export default {
    options: { styleIsolation: 'shared' }
  }
</script>
<script lang="ts" setup>
  import { computed, watch, nextTick, getCurrentInstance } from 'vue'
  import { Options } from '@/services/my/Options'
  import { onLoad } from '@dcloudio/uni-app'
  interface dataList {
    hideValue: []
    textAlign?: string
    maxCount?: number
    cityName?: string
    hasarrow?: boolean
    noguangxi?: boolean
  }
  let allCity = $ref<Array[] | undefined | null>([])
  let firstArr = $ref<Array[] | undefined | null>([])
  let secondArr = $ref<Array[] | undefined | null>([])
  let thirdArr = $ref<Array[] | undefined | null>([])
  let activeGroup = $ref<Array[]>([])
  let isIDA = $ref<any>(-1) //第一排高亮
  let isIDB = $ref<any>(-1) //第二排高亮
  let messageText = $ref<string>() //信息弹窗内容
  let navTop = uni.getMenuButtonBoundingClientRect().top
  let selectedH = $ref<number>(0)
  const instance = getCurrentInstance()
  const {
    hideValue,
    maxCount = 1,
    textAlign = 'left',
    cityName = '请选择',
    hasarrow = true,
    noguangxi = false
  } = defineProps<dataList>()
  const scrollHeight = computed(() => {
    return uni.getSystemInfoSync().windowHeight - 156 - selectedH - navTop
  })

  const width = computed(() => {
    return uni.getSystemInfoSync().windowWidth || '320'
  })
  const showRight = $ref<any>(null)
  const message = $ref<any>(null)
  const emit = defineEmits<{
    (e: 'change'): void
  }>()
  const selectedCount = computed(() => {
    return activeGroup.length
  })
  const canSelectMore = computed(() => {
    return maxCount <= selectedCount.value
  })
  const singleMode = computed(() => {
    return maxCount === 1
  })
  onLoad(async () => {
    // console.log('监听页面加载-zhi')
  })
  watch(
    () => selectedCount.value,
    () => {
      nextTick(() => {
        hideInspect()
      })
    }
  )
  const getDate = async () => {
    firstArr = []
    secondArr = []
    thirdArr = []
    isIDA = -1
    isIDB = -1
    activeGroup = []
    let Data = {}
    const citys = uni.getStorageSync('cityData')
    if (citys.length > 0) {
      Data = citys
    } else {
      Data = await Options.optionsDistrictList()
      uni.setStorageSync('cityData', Data)
    }
    allCity = Data.map((item: any, index: any) => {
      item.text = item.keywordName
      item.selected = false
      item.index = index
      item.grandfather = '000'
      if (item.keywordID == 1) item.text = '广西'
      return item
    })
    Getgrand()
    if (Array.isArray(hideValue)) {
      hideValue.forEach((id: any) => {
        let item = allCity.find((p: any) => p.keywordID == id)
        if (!item) return
        item.selected = true
        addCity(item)
      })
    }

    firstArr = allCity.filter((i: any) => i.parentID == -1)
  }
  const Getgrand = () => {
    let brr = []
    allCity.forEach((i: any) => {
      //  找第一级
      if (i.parentID === -1 && i.hasNext) {
        // 找到第二级
        const arr = allCity.filter((j: any) => j.parentID == i.keywordID && j.hasNext)
        if (arr.length > 0) {
          arr.forEach((a: any) => {
            // 找到第三级
            brr = allCity.filter((j: any) => j.parentID == a.keywordID)
            brr.forEach((k: any) => {
              k.grandfather = i.keywordID
            })
          })
        }
      }
      // i.keywordID == hideValue ? (i.selected = true) : ''
    })
  }
  const selecity = () => {
    getDate()
    showRight.open()
  }
  const bindComfirm = () => {
    if (activeGroup.length < 1) {
      uni.showToast({
        title: '请至少选择一个',
        duration: 2000,
        icon: 'none'
      })
      return
    }
    emit('change', activeGroup)
    showRight.close()
  }
  const closeDrawer = () => {
    // console.log('取消')
    showRight.close()
  }
  const getChildren = (pid: any, num: any) => {
    if (noguangxi) {
      //排除全广西
      return allCity.filter((i: any) => i.parentID == pid)
    } else {
      return allCity.filter((i: any) => i.parentID == pid || i.keywordID == pid)
    }
  }
  const cityChooseA = (city: any) => {
    //第一行点击
    if (isIDA == city.keywordID && city.hasNext) {
      return false
    }
    if (isIDA == city.keywordID && !city.hasNext) {
      onSelectCity(city)
      return false
    }
    isIDA = city.keywordID
    isIDB = -1
    secondArr = []
    thirdArr = []
    if (!city.hasNext) {
      //如果没有子集直接选取
      onSelectCity(city)
      return false
    }
    secondArr = getChildren(city.keywordID, 1)
  }
  const cityChooseB = (item: any, index: number) => {
    //第二行点击
    if (index == 0 && !noguangxi) {
      onSelectCity(item)
      thirdArr = []
      isIDB = item.keywordID
      return false
    }
    if (isIDB == item.keywordID && item.hasNext) {
      return false
    }
    if (isIDB == item.keywordID && !item.hasNext) {
      onSelectCity(item)
      return false
    }
    // activeIndex = -1;
    isIDB = item.keywordID
    thirdArr = []
    if (!item.hasNext) {
      //如果没有子集直接选取
      onSelectCity(item)
      return false
    }
    thirdArr = getChildren(item.keywordID, 2)
  }
  const cityChooseC = (city: any) => {
    onSelectCity(city)
  }
  const onSingleMode = (city: any) => {
    //单选模式
    allCity.forEach((item: any) => {
      item.selected = false
    })
    thirdArr.forEach((item: any) => {
      item.selected = false
    })
    secondArr.forEach((item: any) => {
      item.selected = false
    })
    firstArr.forEach((item: any) => {
      item.selected = false
    })
    city.selected = true
    activeGroup = [city]
    bindComfirm()
  }
  const onMultipleMode = (city: any) => {
    //多选模式
    const isChecked = city.selected
    if (isChecked) {
      //被选过了
      deleteCity(city)
      return false
    }
    //如果没有被选过检测下面的子集和往上的父级
    if (!isChecked) {
      activeGroup.forEach((i: any) => {
        if (
          i.parentID == city.keywordID ||
          i.keywordID == city.parentID ||
          i.grandfather == city.keywordID ||
          i.keywordID == city.grandfather
        ) {
          deleteCity(i)
        }
      })
      if (canSelectMore.value) {
        //如果没选过而且超限了
        // msgType = 'warn'
        messageText = `最多只能选择${maxCount}个选项`
        // message.open()
        uni.showToast({
          title: messageText,
          duration: 2000,
          icon: 'none'
        })
      } else {
        addCity(city)
      }
    }
  }
  const addCity = (city: any) => {
    city.selected = true
    activeGroup.push(city)
  }
  const onSelectCity = (city: any) => {
    if (!city) return
    if (singleMode.value) {
      onSingleMode(city)
    } else {
      onMultipleMode(city)
    }
  }
  const deleteCity = (item: any) => {
    item.selected = false
    thirdArr.forEach((i: any) => {
      i.keywordID == item.keywordID ? (i.selected = false) : ''
    })
    secondArr.forEach((i: any) => {
      i.keywordID == item.keywordID ? (i.selected = false) : ''
    })
    activeGroup = activeGroup.filter((i: any) => i.keywordID !== item.keywordID)
  }
  const hideInspect = () => {
    nextTick(() => {
      const query = uni.createSelectorQuery().in(instance)
      query
        .select('.selected-items')
        .boundingClientRect((res) => {
          return (selectedH = parseInt(res?.height-12))
        })
        .exec()
    })
    return false
  }
</script>

<style lang="scss" scoped>
  $blue: #5e8df5;
  .h100 {
    height: 100%;
  }
  .bg-white {
    background: #fff;
  }
  .city-picker {
    .picker-input {
      flex: 1;
      width: 100%;
      display: flex;
      box-sizing: border-box;
      min-height: 36px;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      position: relative;
      font-size: 28rpx;
      .left {
        position: absolute;
        left: 0;
        font-size: 15px;
      }
      .right {
        position: absolute;
        right: 38rpx;
        font-size: 15px;
        text-align: right;
      }
      .pleaseChoose {
        color: #999;
        font-weight: 200;
      }
      .uni-ellipsis {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        width: 100%;
      }
    }
    .icon {
      position: absolute;
      right: 0;
      font-size: 28rpx;
      color: #808080;
    }
  }
  .pop-city-box {
    width: 100%;
    .btn-box {
      background: #fff;
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      border-top: 1px solid #eee;
      .login-wrap-button {
        border: none;
        background: $blue;
        color: #fff;
        margin: 10px 10px 30px;
        height: 40px;
        line-height: 40px;
        font-size: 16px;
      }
    }
    ::v-deep .uni-navbar__content_view {
      height: 40px;
      line-height: 40px;
    }
    ::v-deep .uni-navbar-btn-text {
      text {
        font-size: 14px !important;
      }
    }
  }
  /*  #ifdef MP-ALIPAY */
  .btn-box {
    background: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    border-top: 1px solid #eee;
    .login-wrap-button {
      border: none;
      background: $blue;
      color: #fff;
      margin: 10px;
      height: 40px;
      line-height: 40px;
      font-size: 16px;
    }
  }
  ::v-deep .uni-navbar__content_view {
    height: 40px;
    line-height: 40px;
  }
  ::v-deep .uni-navbar-btn-text {
    text {
      font-size: 14px !important;
    }
  }
  /*  #endif  */
  .selected-wrap {
    padding: 0px 15px;
    .count {
      font-size: 12px;
      color: #666;
      line-height: 22px;
    }
    .blue {
      color: #228efd;
    }
  }
  .selected-items {
    padding: 2px 0 10px 0;
    .item {
      color: #228efd;
      font-size: 13px;
      padding: 0px 10px;
      float: left;
      display: flex;
      border: 1px solid #228efd;
      border-radius: 3px;
      line-height: 22px;
      margin: 5px 5px 0rpx 0px;
      .iconclose {
        font-size: 14px;
        padding-left: 5px;
      }
    }
  }
  .sel-main {
    .box-city {
      float: left;
      margin-right: -380rpx;
      position: relative;
    }
    .sel-city-box {
      float: left;
      .li {
        padding: 0px 10px;
        color: #666;
        line-height: 90rpx;
        height: 90rpx;
        .sdl {
          float: left;
          font-size: 28rpx;
        }
        .sdr {
          float: right;
          .iconfont {
            font-size: 22rpx;
            line-height: 90rpx;
            height: 90rpx;
          }
        }
      }
      .isseled {
        color: $blue;
      }
    }
    .sel-city-boxA {
      width: 180rpx;
      background: #ebedf6;
      .on {
        background: #f4f5f9;
      }
    }
    .sel-city-boxB {
      width: 200rpx;
      background: #f4f5f9;
      .on {
        background: #fff;
      }
    }
    .sel-city-boxC {
      width: 100%;
      background: #fff;
      float: left;
      .city-boxC {
        margin-left: 380rpx;
      }
    }
  }
</style>
