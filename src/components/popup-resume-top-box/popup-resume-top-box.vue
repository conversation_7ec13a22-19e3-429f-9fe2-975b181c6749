<template>
  <uni-popup ref="popupResumeTopBox" type="center" @change="change">
    <view class="popup-resume-top">
      <view class="bg-blue">
        <view class="pos-name">当前职位：{{ positionName }}</view>
        <view class="shengyu">剩余{{ leftCount }}次</view>
      </view>
      <view class="shiyong">是否使用1次{{ contentText }}？</view>

      <view class="btn-wrap">
        <button class="btn btn-cancel" plain type="default" @click="cancel">取消</button>
        <button class="btn btn-confirm" plain type="primary" @click="confirm">确定</button>
      </view>
    </view>
  </uni-popup>
</template>
<script lang="ts">
  export default {
    inheritAttrs: false,
    options: { styleIsolation: 'shared' }
  }
</script>
<script lang="ts" setup>
  import {
    defineComponent,
    reactive,
    toRef,
    toRefs,
    ref,
    watch,
    computed,
    getCurrentInstance,
    onMounted,
    onBeforeMount,
    nextTick
  } from 'vue'
  interface Props {
    contentText: string
    positionName: string
    show: boolean
    leftCount: number
  }

  const { contentText, positionName, show, leftCount } = defineProps<Props>()
  let popupResumeTopBox = ref(null)
  const emit = defineEmits<{
    (e: 'cancel'): void
    (e: 'confirm'): void
  }>()

  watch(
    () => show,
    async (newval) => {
      if (newval) {
        popupResumeTopBox.value.open()
      } else {
        popupResumeTopBox.value.close()
      }
    }
  )

  const cancel = () => {
    emit('cancel')
  }
  const confirm = () => {
    emit('confirm')
  }
  const change = (e) => {
    if (!e.show) {
      emit('cancel')
    }
  }
</script>
<style lang="scss" scoped>
  .popup-resume-top {
    width: 528rpx;
    background: #fff;
    border-radius: 8rpx;
    overflow: hidden;
    .bg-blue {
      text-align: left;
      background: #f5f7fa;
      color: #3d456b;
      padding: 30rpx;
      .pos-name {
        font-size: 28rpx;
        padding-bottom: 10rpx;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .shengyu {
        font-size: 36rpx;
        font-weight: bold;
      }
    }
    .shiyong {
      font-size: 28rpx;
      padding: 40rpx 30rpx;
      text-align: left;
    }
    .btn-wrap {
      display: flex;
      border-top: 2rpx solid #f2f2f2;
      .btn {
        flex: 1;
        height: 90rpx;
        line-height: 90rpx;
        font-size: 28rpx;
        border: none;
        background: #fff;
        border-radius: 0;
      }
      .btn-cancel {
        background-color: #fff;
        color: #666;
        border-right: 2rpx solid #f2f2f2;
      }
      .btn-confirm {
        color: #457ccf;
      }
    }
  }
</style>
