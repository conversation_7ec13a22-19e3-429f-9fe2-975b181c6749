<template>
  <view id="baseMap" class="base-map">
    <view class="page-body">
      <view class="page-section page-section-gap">
        <map
          :scale="scale"
          :style="{ width: width, height: height }"
          :enable-satellite="false"
          :enable-traffic="false"
          :enable-scroll="false"
          :enable-zoom="false"
          layer-style="3"
          :subkey="subkey"
          :enable-overlooking="true"
          :show-location="false"
          :latitude="latitude"
          :longitude="longitude"
          :markers="markers"
          @callouttap="road"
          @tap="road"
        >
        </map>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
  import { computed } from 'vue'

  // // #ifdef MP-WEIXIN
  // const subkey = 'FLXBZ-YQBC6-GZSSC-EFZ4M-FFIJF-TSBF5'
  // // #endif

  // // #ifdef MP-TOUTIAO
  // const subkey = 'RYYBZ-FWI64-KUBUZ-DMIB7-CCPEH-6CBR3'
  // // #endif

  // // #ifdef MP-ALIPAY
  // const subkey = 'DAXBZ-S53WW-UJ4R6-36CZL-VWZYJ-LEF47'
  // // #endif

  //  scale: '18',
  //         latitude: 25.61169,
  //         longitude: 110.67147

  const subkey = ''
  interface Props {
    width?: string
    height?: string
    scale?: string
    latitude: number
    longitude: number
    content: string
  }
  const props = withDefaults(defineProps<Props>(), {
    width: '100%',
    height: '360rpx',
    scale: '16',
    latitude: 25.61169,
    longitude: 110.67147,
    content: ''
  })

  const emits = defineEmits<{
    (e: 'callouttap', value: { detail: number }): void //点击气泡触发
  }>()

  const callouttap = (e: { detail: number }) => {
    emits('callouttap', e)
  }

  const latitude = computed(() => props.latitude)
  const longitude = computed(() => props.longitude)

  const addressContent = computed(() => {
    let content = props.content.length > 37 ? props.content.substring(0, 37) + '...' : props.content
    content = subStr(content)
    return content
  })

  const markers = computed(() => {
    let obj = {
      id: 1,
      latitude: latitude.value,
      longitude: longitude.value,
      callout: {
        content: addressContent,
        color: '#000',
        fontSize: 12,
        borderWidth: 6,
        borderColor: '#fff',
        borderRadius: 0,
        padding: 5,
        textAlign: 'center',
        bgColor: '#fff',
        display: 'ALWAYS'
      },
      iconPath: 'https://image.gxrc.com/gxrcsite/wxMiniApp/2022/static/img/map/address.png',
      width: 21,
      height: 24,
      anchor: {
        x: 0.5,
        y: 0.5
      }
    }
    let arr = []
    arr.push(obj)
    return arr
  })

  const road = () => {
    uni.navigateTo({
      url:
        '/pages/map/index?latitude=' +
        latitude.value +
        '&longitude=' +
        longitude.value +
        '&content=' +
        props.content
    })
  }

  const subStr = (str: string): string => {
    let newstr = ''

    if (str.length > 20) {
      newstr = str.slice(0, 20) + '\n'
      return newstr + subStr(str.slice(20))
    } else {
      return str
    }
  }
</script>
<script lang="ts">
  export default {
    inheritAttrs: false,
    options: { styleIsolation: 'shared' }
  }
</script>
<style lang="scss" scoped>
  .page-section-gap {
    border-radius: 8rpx;
    transform: translateY(0);
    overflow: hidden;
  }
  ::v-deep map {
    z-index: 1;
  }
</style>
