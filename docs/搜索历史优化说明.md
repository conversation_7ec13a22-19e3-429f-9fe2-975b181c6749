# 搜索历史功能优化说明

## 概述

本次优化对搜索历史功能进行了全面升级，实现了搜索频率统计和常用关键词识别功能。新功能不仅保留了原有的搜索历史记录，还增加了智能分析和统计能力。

## 主要改进

### 1. 数据结构升级

**原始数据结构（字符串数组）：**
```javascript
['关键词1', '关键词2', '关键词3']
```

**新数据结构（对象数组）：**
```javascript
[
  {
    keyword: '关键词1',
    count: 5,                    // 搜索次数
    lastSearchTime: 1640995200000 // 最后搜索时间戳
  },
  {
    keyword: '关键词2',
    count: 3,
    lastSearchTime: 1640995100000
  }
]
```

### 2. 核心功能

#### 搜索频率统计
- 记录每个关键词的搜索次数
- 相同关键词再次搜索时，计数器自动增加
- 按搜索频率和时间排序

#### 常用关键词识别
- 基于最近10条搜索记录进行频率统计
- 自动识别搜索频率最高的关键词
- 实时更新常用关键词列表

#### 数据迁移
- 自动检测并转换旧格式数据
- 保证向后兼容性
- 无缝升级用户体验

## 技术实现

### 1. 修改后的 Search 函数

```typescript
const Search = (e: any) => {
  if (e === '') return
  
  // 获取现有搜索历史（新数据结构）
  const history: SearchHistoryItem[] = uni.getStorageSync('search_history') || []
  
  // 查找是否已存在该关键词
  const existingIndex = history.findIndex(item => item.keyword === e)
  
  if (existingIndex !== -1) {
    // 如果关键词已存在，增加搜索次数并移到最前面
    const existingItem = history[existingIndex]
    existingItem.count += 1
    existingItem.lastSearchTime = Date.now()
    
    history.splice(existingIndex, 1)
    history.unshift(existingItem)
  } else {
    // 如果是新关键词，创建新记录并添加到最前面
    const newItem: SearchHistoryItem = {
      keyword: e,
      count: 1,
      lastSearchTime: Date.now()
    }
    history.unshift(newItem)
  }
  
  // 限制历史记录数量
  if (history.length > 50) {
    history.splice(50)
  }
  
  // 保存更新后的历史记录
  uni.setStorageSync('search_history', history)
  
  // 获取并保存常用关键词
  const popularKeywords = getPopularKeywords(history)
  uni.setStorageSync('popular_keywords', popularKeywords)
  
  // ... 其他逻辑
}
```

### 2. 常用关键词算法

```typescript
const getPopularKeywords = (historyList: SearchHistoryItem[]): string[] => {
  // 取最近10条记录
  const recentHistory = historyList.slice(0, 10)
  
  // 统计关键词频率
  const frequencyMap = new Map<string, number>()
  recentHistory.forEach(item => {
    const currentCount = frequencyMap.get(item.keyword) || 0
    frequencyMap.set(item.keyword, currentCount + item.count)
  })
  
  // 按频率排序，返回频率最高的关键词
  return Array.from(frequencyMap.entries())
    .sort((a, b) => b[1] - a[1])
    .map(([keyword]) => keyword)
}
```

### 3. 数据迁移机制

```typescript
const migrateSearchHistory = () => {
  const history = uni.getStorageSync('search_history') || []
  
  // 检查是否是旧格式（字符串数组）
  if (history.length > 0 && typeof history[0] === 'string') {
    console.log('检测到旧格式搜索历史，正在迁移...')
    
    const newHistory: SearchHistoryItem[] = history.map((keyword: string, index: number) => ({
      keyword,
      count: 1, // 旧数据默认搜索次数为1
      lastSearchTime: Date.now() - (index * 60000) // 模拟时间间隔
    }))
    
    // 保存新格式数据
    uni.setStorageSync('search_history', newHistory)
    
    // 生成初始常用关键词
    const popularKeywords = getPopularKeywords(newHistory)
    uni.setStorageSync('popular_keywords', popularKeywords)
    
    console.log('搜索历史迁移完成')
  }
}
```

## 工具类使用

### SearchHistoryManager 类

提供了完整的搜索历史管理功能：

```typescript
import { SearchHistoryManager } from '@/utils/searchHistoryUtils'

// 添加搜索记录
SearchHistoryManager.addSearchRecord('前端开发')

// 获取搜索统计
const stats = SearchHistoryManager.getSearchStatistics()
console.log('总搜索次数:', stats.totalSearches)
console.log('常用关键词:', stats.popularKeywords)

// 获取关键词排行榜
const ranking = SearchHistoryManager.getKeywordRanking(10)

// 获取搜索建议
const suggestions = SearchHistoryManager.getKeywordSuggestions('前端', 5)

// 清除历史记录
SearchHistoryManager.clearSearchHistory()
```

## 存储结构

### 主要存储键

1. **search_history**: 搜索历史记录
2. **popular_keywords**: 常用关键词缓存

### 数据示例

```javascript
// search_history
[
  {
    "keyword": "前端开发",
    "count": 8,
    "lastSearchTime": 1640995200000
  },
  {
    "keyword": "Vue.js",
    "count": 5,
    "lastSearchTime": 1640995100000
  },
  {
    "keyword": "JavaScript",
    "count": 3,
    "lastSearchTime": 1640995000000
  }
]

// popular_keywords
["前端开发", "Vue.js", "JavaScript", "React", "TypeScript"]
```

## 性能优化

1. **缓存机制**: 常用关键词结果缓存，避免重复计算
2. **数量限制**: 历史记录限制在50条，防止存储过大
3. **懒加载**: 统计计算仅在需要时执行
4. **批量操作**: 减少存储读写次数

## 使用建议

1. **初始化**: 在应用启动时调用数据迁移函数
2. **搜索时**: 使用 `SearchHistoryManager.addSearchRecord()` 记录搜索
3. **展示**: 使用 `getSearchStatistics()` 获取统计信息
4. **建议**: 使用 `getKeywordSuggestions()` 提供搜索建议

## 兼容性

- ✅ 完全向后兼容旧版本数据
- ✅ 自动数据迁移，用户无感知
- ✅ 支持 uni-app 所有平台
- ✅ TypeScript 类型安全

## 示例页面

参考 `src/examples/SearchHistoryExample.vue` 查看完整的使用示例，包括：

- 搜索统计展示
- 常用关键词列表
- 关键词排行榜
- 搜索建议功能
- 历史记录管理

这个示例展示了如何在实际项目中使用新的搜索历史功能。
