---
description: 个人小程序通用rules
globs: *.ts,*.vue
alwaysApply: true
---
# Project Rules for gxrc-miniprogram

## 项目概述
这是一个基于uni-app框架开发的小程序项目，使用Vue 3 + TypeScript技术栈，支持多端发布（微信、支付宝、字节跳动等平台）。

## 技术栈规范
- 框架：uni-app (Vue 3)
- 语言：TypeScript 4.7.4
- 状态管理：Pinia 2.0.13
- 样式：SCSS
- 包管理器：pnpm
- 国际化：vue-i18n
- UI组件库：@dcloudio/uni-ui

## 目录结构规范
项目遵循以下目录结构：

```typescript
src/
├── pages/            // 主包页面
├── mypages/          // 分包 - 我的相关页面
├── pageEcharts/      // 分包 - 图表相关页面
├── pagelive/         // 分包 - 直播相关页面
├── pageSchool/       // 分包 - 学校相关页面
├── pageEvaluation/   // 分包 - 评估相关页面
├── components/       // 公共组件
├── store/           // Pinia状态管理
├── utils/           // 工具函数
├── hooks/           // 自定义hooks
├── services/        // API服务
├── static/          // 静态资源
├── uni_modules/     // uni-app插件
├── ttcomponents/    // 字节跳动小程序专用组件
└── wxcomponents/    // 微信小程序专用组件
```

## 编码规范

### TypeScript规范
- 必须使用TypeScript进行开发
- 严格遵守类型定义
- 接口定义使用interface
- 类型定义使用type
- 避免使用any类型

### Vue组件规范
- 使用Vue 3 Composition API
- 组件名使用PascalCase
- Props必须定义类型
- 使用`<script setup lang="ts">`
- 组件样式使用scoped

### 样式规范
- 使用SCSS预处理器
- 遵循BEM命名规范
- 优先使用uni-app内置样式类
- 响应式设计使用rpx单位

### 状态管理规范
- 使用Pinia进行状态管理
- Store按模块拆分
- 异步操作使用actions
- 避免直接修改state

### API请求规范
- API接口统一在services目录管理
- 使用uni.request发起请求
- 请求封装统一错误处理
- 支持请求拦截和响应拦截

### 分包规范
- 按功能模块进行分包
- 分包预下载配置在pages.json
- 分包大小控制在2M以内
- 公共组件放在主包中

### Git提交规范
- 使用commitizen规范提交信息
- 遵循conventional commits规范
- 分支管理遵循gitflow工作流

### 环境配置规范
- 支持development/staging/production环境
- 环境变量配置在.env文件中
- 构建命令统一在package.json中管理
- 不同平台构建配置在manifest.json中

## 性能优化规则
1. 图片资源优化
   - 使用webp格式
   - 小图片base64内联
   - 大图片CDN加速

2. 包体积优化
   - 合理使用分包
   - 及时清理无用代码
   - 第三方依赖按需引入

3. 渲染性能优化
   - 合理使用v-if和v-show
   - 长列表使用虚拟列表
   - 避免深层组件嵌套

## 跨平台兼容规则
1. 条件编译
   - 使用平台特有API时必须使用条件编译
   - 平台差异化样式使用条件编译
   - 按平台引入不同组件库

2. 组件兼容
   - 优先使用uni-ui组件库
   - 平台专有组件放在对应目录
   - 自定义组件做好兼容性处理

## 安全规则
1. 数据安全
   - 敏感数据加密存储
   - 清理本地存储敏感信息
   - API请求使用HTTPS

2. 代码安全
   - 避免硬编码敏感信息
   - 生产环境关闭调试
   - 及时更新依赖版本

## 调试规则
1. 日志规范
   - 统一使用utils中的日志工具
   - 区分日志级别
   - 生产环境禁用debug日志

2. 错误处理
   - 统一错误处理机制
   - 异常信息上报
   - 友好的错误提示

## 发布规则
1. 版本管理
   - 遵循语义化版本
   - 更新日志及时维护
   - 版本号统一管理

2. 发布流程
   - 代码审查
   - 测试验证
   - 分环境发布